import { JobContract } from '@ioc:Rocketseat/Bull'
import Env from '@ioc:Adonis/Core/Env'
import RentalEvent, { RentalEventProcessedStatus } from 'App/Models/RentalEvent'
import Rental, { RentalStatus } from 'App/Models/Rental'
import { DateTime } from 'luxon'

export default class ProcessRentalEventJob implements JobContract {
  public key = `ProcessRentalEventJob-${Env.get('NODE_ENV')}`

  public async handle(job) {
    const { data } = job
    const rentalEvent = data

    try {
      const findEvent = await RentalEvent.query()
        .where('transactionHash', rentalEvent.transactionHash)
        .where('logIndex', rentalEvent.logIndex)
        .first()

      if (findEvent && findEvent.processed === RentalEventProcessedStatus.PROCESSED) {
        console.log('SKIP (Event already processed):', rentalEvent.transactionHash)
        return {
          status: 0,
          message: 'Event already processed',
        }
      }

      if (findEvent && findEvent.processed === RentalEventProcessedStatus.PENDING) {
        console.log('Reprocessing rental event:', rentalEvent.transactionHash)

        // Process the event
        const { isSuccess } = await this.processData(findEvent)

        // After processing
        if (isSuccess) {
          findEvent.processed = RentalEventProcessedStatus.PROCESSED
          await findEvent.save()

          console.log('DONE:', findEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${findEvent.transactionHash}`,
          }
        }
      } else {
        console.log('Processing rental event:', rentalEvent.transactionHash)
        const createEvent = await RentalEvent.create({
          address: rentalEvent.address,
          blockHash: rentalEvent.blockHash,
          blockNumber: rentalEvent.blockNumber,
          data: rentalEvent.data,
          logIndex: rentalEvent.logIndex,
          transactionHash: rentalEvent.transactionHash,
          transactionIndex: rentalEvent.transactionIndex,
          removed: rentalEvent.removed,
          args: rentalEvent.args,
          eventName: rentalEvent.eventName,
          processed: RentalEventProcessedStatus.PENDING,
        })

        // Process the event
        const { isSuccess } = await this.processData(createEvent)

        // After processing
        if (isSuccess) {
          createEvent.processed = RentalEventProcessedStatus.PROCESSED
          await createEvent.save()

          console.log('DONE:', createEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${createEvent.transactionHash}`,
          }
        }
      }

      return {
        status: 0,
        message: 'Failed to process event',
      }
    } catch (error) {
      console.log('Error processing rental event:', error)
      return {
        status: 0,
        message: error.message,
      }
    }
  }

  private async processData(rentalEvent: RentalEvent) {
    switch (rentalEvent.eventName) {
      case 'ChickenRented':
        return this.processChickenRentedEvent(rentalEvent)
      case 'ChickenUnlistedForRent':
        return this.processChickenUnlistedForRentEvent(rentalEvent)
      case 'ChickenListedForRent':
        return this.processChickenListedForRentEvent(rentalEvent)
      default:
        return {
          isSuccess: false,
        }
    }
  }

  private async processChickenListedForRentEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Verify the rental status
      if (rental.status !== RentalStatus.AVAILABLE) {
        // Update rental status if it's not already marked as available
        rental.status = RentalStatus.AVAILABLE
        await rental.save()
      }

      if (rental.roninPrice == BigInt(0)) {
        rental.status = RentalStatus.RENTED
        await rental.save()
      }

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing rental event data:', error)
      return {
        isSuccess: false,
      }
    }
  }

  private async processChickenUnlistedForRentEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Verify the rental status
      if (rental.status !== RentalStatus.CANCELLED) {
        // Update rental status if it's not already marked as cancelled
        rental.status = RentalStatus.CANCELLED
        await rental.save()
      }

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing rental event data:', error)
      return {
        isSuccess: false,
      }
    }
  }

  private async processChickenRentedEvent(rentalEvent: RentalEvent) {
    try {
      // Find the rental by ID from the event
      const rental = await Rental.find(Number(rentalEvent.args.rentId))

      if (!rental) {
        throw new Error(`Rental with ID ${rentalEvent.args.rentId} not found`)
      }

      // Verify the rental status
      if (rental.status !== RentalStatus.RENTED) {
        // Update rental status if it's not already marked as rented
        rental.status = RentalStatus.RENTED
        rental.renterAddress = rentalEvent.args.renter
        rental.rentedAt = DateTime.now()
        rental.expiresAt = rentalEvent.args.expiresAt
        await rental.save()
      }

      return {
        isSuccess: true,
      }
    } catch (error) {
      console.log('Error processing rental event data:', error)
      return {
        isSuccess: false,
      }
    }
  }
}
