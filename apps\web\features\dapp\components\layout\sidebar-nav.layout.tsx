"use client";

import React from "react";
import { Sidebar } from "ui";
import AppSidebar from "../sidebar/app-sidebar";
import AppSidebarNav from "../sidebar/app-sidebar-nav";

export default function SidebarNavLayout({
  children,
}: React.ComponentProps<"div">) {
  return (
    <Sidebar.Provider className="font-Poppins">
      {/* Sidebar */}
      <AppSidebar
        className="border-[#191C21]"
        intent="sidebar"
        collapsible="offcanvas"
      />
      <Sidebar.Inset className="bg-dapp-content">
        {/* Navbar */}
        <div className="sticky top-0 z-50 shadow-lg">
          <AppSidebarNav />
        </div>

        {/* Content */}
        <div className="p-6 lg:p-8">{children}</div>
      </Sidebar.Inset>
    </Sidebar.Provider>
  );
}
