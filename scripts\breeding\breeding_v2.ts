import { ethers } from "hardhat";
import fs from "fs";
import path from "path";
const signer = "******************************************";
const legacyChickenAddress = "******************************************";
const devAddress = "******************************************";

import abi from "./abi.json";

async function main() {
  // await mine();

  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);

  const accountBalance = await deployer.provider.getBalance(deployer.address);

  console.log("Account balance:", accountBalance.toString());

  const ERC1155Common = await ethers.getContractFactory("ERC1155Common");
  const resources = await ERC1155Common.deploy(
    "******************************************",
    "Sabong Saga Resources",
    "SSRESOURCES",
    "https://item-api-beta.vercel.app/api/resources/"
  );

  await resources.waitForDeployment();
  const resourcesAddress = await resources.getAddress();

  console.log("resourcesAddress:", resourcesAddress);

  const SabongSagaBreeding = await ethers.getContractFactory(
    "SabongSagaBreeding"
  );
  const sabongSagaBreeding = await SabongSagaBreeding.deploy(
    abi.sabong_saga_cock_address,
    abi.sabong_saga_genesis_address,
    abi.sabong_saga_legacy_address,
    abi.sabong_saga_items_address,
    resourcesAddress,
    deployer.address,
    signer
  );

  await sabongSagaBreeding.waitForDeployment();
  const sabongSagaBreedingAddress = await sabongSagaBreeding.getAddress();
  console.log("sabongSagaBreedingAddress:", sabongSagaBreedingAddress);

  const legacyContract = new ethers.Contract(
    legacyChickenAddress,
    abi.sabong_saga_legacy_chickens,
    deployer
  );

  let tx = await legacyContract.grantRole(
    ethers.keccak256(ethers.toUtf8Bytes("MINTER_ROLE")),
    sabongSagaBreedingAddress
  );

  await tx.wait();

  tx = await resources.mint(devAddress, 2, 1000000, ethers.toUtf8Bytes(""));

  await tx.wait();

  tx = await resources.mint(devAddress, 3, 1000000, ethers.toUtf8Bytes(""));

  await tx.wait();

  const updatedAbi = {
    ...abi,
    sabong_saga_breeding_address: await sabongSagaBreeding.getAddress(),
    sabong_saga_resources_address: resourcesAddress,
  };

  // Write updated ABI to file
  const abiPath = path.join(__dirname, "abi.json");
  fs.writeFileSync(abiPath, JSON.stringify(updatedAbi, null, 2), "utf8");
  console.log("\nUpdated abi.json with new addresses");
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
