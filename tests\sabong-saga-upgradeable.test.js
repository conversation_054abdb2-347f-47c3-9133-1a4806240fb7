const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");
const abi = require("../scripts/breeding/abi.json");

describe("SabongSagaBreedingUpgradeable Contract", function () {
    let Breeding, breeding;
    let MockERC20, cock;
    let genesis, legacy;
    let feathers, resources;
    let owner, player, treasury, signer;
    let devWallet

    const cooldown = 3600; // 1 hour cooldown
  
    before(async function () {
        [owner, player, treasury, signer] = await ethers.getSigners();

        let devAddress = "******************************************";
        await hre.network.provider.request({
            method: "hardhat_impersonateAccount",
            params: [devAddress],
        });

        devWallet = await ethers.getSigner(devAddress);

        // Deploy Mock ERC20 (COCK Token)
        MockERC20 = await ethers.getContractFactory("MockERC20");
        cock = await MockERC20.deploy();
        await cock.waitForDeployment();

        // Deploy Mock ERC721 (Genesis and Legacy Chickens)
        
        genesis = new ethers.Contract(
            abi.sabong_saga_genesis_address,
            abi.sabong_saga_genesis_chickens,
            owner
        );

        legacy = new ethers.Contract(
            abi.sabong_saga_legacy_address,
            abi.sabong_saga_legacy_chickens,
            owner
        );

        // Deploy Mock ERC1155 (Feathers and Resources)
        feathers = new ethers.Contract(
            abi.sabong_saga_items_address,
            abi.erc1155_common,
            owner
        );

        resources = new ethers.Contract(
            abi.sabong_saga_resources_address,
            abi.erc1155_common,
            owner
        );

        // Deploy Breeding Contract using upgrades plugin
        Breeding = await ethers.getContractFactory("SabongSagaBreedingUpgradeable");
        breeding = await upgrades.deployProxy(Breeding, [
            await cock.getAddress(),
            await genesis.getAddress(),
            await legacy.getAddress(),
            await feathers.getAddress(),
            await resources.getAddress(),
            treasury.address,
            signer.address
        ], {
            initializer: 'initialize',
            kind: 'transparent'
        });
        await breeding.waitForDeployment();
    });

    async function getSignature(
        userAddress, 
        parent1, 
        parent2, 
        totalAmount, 
        amountToVault, 
        amountToNinuno,
        breedingCooldownTime,
        feathersData,  
        resourcesData
    ) {
        // Hash the resources data separately
        const resourcesHash = ethers.keccak256(
            ethers.AbiCoder.defaultAbiCoder().encode(
                ["uint256[][]"],
                [resourcesData]
            )
        );
        const feathersHash = ethers.keccak256(
            ethers.AbiCoder.defaultAbiCoder().encode(
                ["uint256[][]"],
                [feathersData]
            )
        );

        const messageHash = ethers.solidityPackedKeccak256(
            ["address", "uint256", "uint256", "uint256", "uint256", "uint256", "uint256", "bytes32", "bytes32"],
            [userAddress, parent1, parent2, totalAmount, amountToVault, amountToNinuno, breedingCooldownTime, feathersHash, resourcesHash]
        );

        return signer.signMessage(ethers.getBytes(messageHash));
    }

    it("Should make breeding contract get MINTER_ROLE of legacy", async function() {
        const minterRole = ethers.keccak256(ethers.toUtf8Bytes("MINTER_ROLE"));
        await legacy.grantRole(minterRole, await breeding.getAddress());
        expect(await legacy.hasRole(minterRole, await breeding.getAddress())).to.be.true;
    });

    it("Should update Referral Address", async function() {
        const referralAddress = "******************************************";
        await breeding.connect(owner).updateReferral(referralAddress);
        expect(await breeding.referral()).to.equal(referralAddress);
    });

    it("Should approve breeding contract on ERC1155 contracts", async function() {
        await feathers.connect(player).setApprovalForAll(await breeding.getAddress(), true);
        await resources.connect(player).setApprovalForAll(await breeding.getAddress(), true);
    });
    
    it("Should mint assets", async function(){
        await genesis.mint(player.address);
        await legacy.mint(player.address);
        await cock.mint(player.address, ethers.parseEther("1000000"));
        await cock.connect(player).approve(await breeding.getAddress(), ethers.parseEther("1000000"));
    });

    it("Should give player $COCK tokens", async function () {
        await cock.mint(owner.address, ethers.parseEther("1000000"));
        await cock.transfer(player.address, ethers.parseEther("100"));

        expect(await cock.balanceOf(player.address)).to.equal(ethers.parseEther("1000100"));
    });

    it("Should allow player to breed chickens with valid signature", async function () {


        await cock.mint(devWallet.address, ethers.parseEther("1000000"));
        await cock.connect(devWallet).approve(await breeding.getAddress(), ethers.parseEther("1000000"));
        await feathers.connect(devWallet).setApprovalForAll(await breeding.getAddress(), true);
        await resources.connect(devWallet).setApprovalForAll(await breeding.getAddress(), true);


        const params = {
            chickenLeftTokenId: 100n,
            chickenRightTokenId: 10001n,
            totalAmount: ethers.parseEther("10"),
            amountToVault: ethers.parseEther("3"),
            amountToNinuno: ethers.parseEther("3.5"),
            breedingCooldownTime: BigInt(cooldown),
            feathersData: [],
            resourcesData: []
        };

        const signature = await getSignature(
            devWallet.address,
            params.chickenLeftTokenId,
            params.chickenRightTokenId,
            params.totalAmount,
            params.amountToVault,
            params.amountToNinuno,
            params.breedingCooldownTime,
            params.feathersData,
            params.resourcesData
        );

        await expect(
            breeding.connect(devWallet).breed(
                params,
                signature,
                "exidz" // referralCode
            )
        ).to.emit(breeding, "Breed");
    });

    it("Should initially track breed count and time", async function () {
        const unusedTokenId = 10000;
        
        const breedCount = await breeding.chickenBreedCount(unusedTokenId);
        const breedTime = await breeding.chickenBreedTime(unusedTokenId);

        expect(breedCount).to.equal(0n);
        expect(breedTime).to.equal(0n);
    });

    it("Should handle multiple breeding operations", async function() {
        await time.increase(cooldown + 1);

        await genesis.mint(devWallet.address);
        await legacy.mint(devWallet.address);

        // Perform two separate breeding operations instead of batch
        const breedingParams = [
            {
                chickenLeftTokenId: 100n,
                chickenRightTokenId: 10001n,
                totalAmount: ethers.parseEther("10"),
                amountToVault: ethers.parseEther("3"),
                amountToNinuno: ethers.parseEther("3.5"),
                breedingCooldownTime: BigInt(cooldown),
                feathersData: [],
                resourcesData: []
            },
            {
                chickenLeftTokenId: 1006n,
                chickenRightTokenId: 10008n,
                totalAmount: ethers.parseEther("10"),
                amountToVault: ethers.parseEther("3"),
                amountToNinuno: ethers.parseEther("3.5"),
                breedingCooldownTime: BigInt(cooldown),
                feathersData: [],
                resourcesData: []
            }
        ];

        for(const params of breedingParams) {
            const signature = await getSignature(
                devWallet.address,
                params.chickenLeftTokenId,
                params.chickenRightTokenId,
                params.totalAmount,
                params.amountToVault,
                params.amountToNinuno,
                params.breedingCooldownTime,
                params.feathersData,
                params.resourcesData
            );

            await expect(
                breeding.connect(devWallet).breed(params, signature, "") // Empty referralCode
            ).to.emit(breeding, "Breed");
        }
    });

    it("Should revert batch breeding with invalid array lengths", async function() {
        const params = {
            chickenLeftTokenIds: [1],
            chickenRightTokenIds: [2223, 2224], // Mismatched length
            totalAmounts: [ethers.parseEther("10")],
            amountsToVault: [ethers.parseEther("3")],
            amountsToNinuno: [ethers.parseEther("3.5")],
            breedingCooldownTimes: [cooldown],
            feathersData: [[]],
            resourcesData: [[]],
            signatures: []
        };

        await expect(breeding.connect(player).breedBatch(params, ""))
            .to.be.revertedWithCustomError(breeding, "InvalidArrayLength");
    });

    describe("Genesis Identification", function() {
        it("Should correctly identify Genesis tokens", async function() {
            const tokenIds = [1, 2222, 2223, 3000];
            const results = await breeding.isGenesisBatch(tokenIds);
            
            expect(results[0]).to.be.true;  // 1 is Genesis
            expect(results[1]).to.be.true;  // 2222 is Genesis
            expect(results[2]).to.be.false; // 2223 is Legacy
            expect(results[3]).to.be.false; // 3000 is Legacy
        });
    });

    describe("Breeding Time Tracking", function() {
        it("Should track breeding times correctly", async function() {
            await time.increase(cooldown + 1);

            const params = {
                chickenLeftTokenId: 101n,
                chickenRightTokenId: 10008n,
                totalAmount: ethers.parseEther("10"),
                amountToVault: ethers.parseEther("3"),
                amountToNinuno: ethers.parseEther("3.5"),
                breedingCooldownTime: BigInt(cooldown),
                feathersData: [],
                resourcesData: []
            };

            const signature = await getSignature(
                devWallet.address,
                params.chickenLeftTokenId,
                params.chickenRightTokenId,
                params.totalAmount,
                params.amountToVault,
                params.amountToNinuno,
                params.breedingCooldownTime,
                params.feathersData,
                params.resourcesData
            );

            await breeding.connect(devWallet).breed(params, signature, "");

            const breedTime = await breeding.chickenBreedTime(params.chickenLeftTokenId);
            expect(breedTime).to.be.gt(0n);
        });
    });

    it("Should breed with feathers and resources", async function() {
        await time.increase(cooldown + 1);

        // Mint feathers to player using bulk mint
        await feathers.bulkMint(
            1n, // tokenId
            [devWallet.address], // recipients
            [2n], // amounts
            ["0x"] // data
        );
        await feathers.bulkMint(
            3n, // tokenId
            [devWallet.address], // recipients
            [1n], // amounts
            ["0x"] // data
        );
        
        // Mint resources to player using bulk mint
        await resources.bulkMint(
            1n, // tokenId
            [devWallet.address], // recipients
            [2n], // amounts
            ["0x"] // data
        );
        await resources.bulkMint(
            2n, // tokenId
            [devWallet.address], // recipients
            [1n], // amounts
            ["0x"] // data
        );

        const params = {
            chickenLeftTokenId: 1002n,
            chickenRightTokenId: 10004n,
            totalAmount: ethers.parseEther("2479.08"),
            amountToVault: ethers.parseEther("2231.172"),
            amountToNinuno: ethers.parseEther("247.908"),
            breedingCooldownTime: 172800n,
            feathersData: [[1n, 2n]],  // [tokenId, amount]
            resourcesData: []  // Empty resources data
        };

        const signature = await getSignature(
            devWallet.address,
            params.chickenLeftTokenId,
            params.chickenRightTokenId,
            params.totalAmount,
            params.amountToVault,
            params.amountToNinuno,
            params.breedingCooldownTime,
            params.feathersData,
            params.resourcesData
        );

        await expect(
            breeding.connect(devWallet).breed(params, signature, "exidz")
        ).to.emit(breeding, "Breed");
    });

    it("Should impersonate account and test breeding", async function() {
        let devAddress = "******************************************";
        await hre.network.provider.request({
            method: "hardhat_impersonateAccount",
            params: [devAddress],
        });

        const devSigner = await ethers.getSigner(devAddress);

        // Mint COCK tokens to devAddress
        await cock.mint(devAddress, ethers.parseEther("10000")); // Mint enough tokens
        await cock.connect(devSigner).approve(await breeding.getAddress(), ethers.parseEther("10000"));

        // Mint NFTs to devAddress
        await genesis.mint(devAddress); // This will mint token ID 20
        await legacy.mint(devAddress); // This will mint token ID 2240

        const params = {
            chickenLeftTokenId: BigInt(100),
            chickenRightTokenId: BigInt(10003),
            totalAmount: ethers.parseEther("2479.08"),
            amountToVault: ethers.parseEther("2231.172"),
            amountToNinuno: ethers.parseEther("247.908"),
            breedingCooldownTime: BigInt(172800),
            feathersData: [[BigInt(1), BigInt(60)]],
            resourcesData: []
        };

        const signature = await getSignature(
            devAddress,
            params.chickenLeftTokenId,
            params.chickenRightTokenId,
            params.totalAmount,
            params.amountToVault,
            params.amountToNinuno,
            params.breedingCooldownTime,
            params.feathersData,
            params.resourcesData
        );
        
        await expect(
            breeding.connect(devSigner).breed(params, signature, "exidz")
        ).to.emit(breeding, "Breed");
    });

    describe("Ninuno Balance Claiming", function() {
        let withdrawalRequestId;
        let claimAmount;
        let signature;

        beforeEach(async function() {
            withdrawalRequestId = 1n;
            claimAmount = ethers.parseEther("100");

            // Mint COCK tokens to the breeding contract
            await cock.mint(await breeding.getAddress(), ethers.parseEther("1000"));

            // Generate signature for claiming
            const messageHash = ethers.solidityPackedKeccak256(
                ["address", "uint256", "uint256"],
                [player.address, withdrawalRequestId, claimAmount]
            );
            signature = await signer.signMessage(ethers.getBytes(messageHash));
        });

        it("Should allow claiming Ninuno balance with valid signature", async function() {
            const initialBalance = await cock.balanceOf(player.address);

            await expect(
                breeding.connect(player).claimNinunoBalance(
                    withdrawalRequestId,
                    claimAmount,
                    signature
                )
            )
                .to.emit(breeding, "NinunoBalanceClaimed")
                .withArgs(player.address, withdrawalRequestId, claimAmount);

            const finalBalance = await cock.balanceOf(player.address);
            expect(finalBalance - initialBalance).to.equal(claimAmount);
        });

        it("Should revert when claiming with invalid signature", async function() {
            // Generate invalid signature using different signer
            const [_, invalidSigner] = await ethers.getSigners();
            const messageHash = ethers.solidityPackedKeccak256(
                ["address", "uint256", "uint256"],
                [player.address, withdrawalRequestId, claimAmount]
            );
            const invalidSignature = await invalidSigner.signMessage(ethers.getBytes(messageHash));

            await expect(
                breeding.connect(player).claimNinunoBalance(
                    withdrawalRequestId,
                    claimAmount,
                    invalidSignature
                )
            ).to.be.revertedWithCustomError(breeding, "InvalidSignature");
        });

        it("Should revert when claiming with insufficient contract balance", async function() {
            const largeAmount = await cock.balanceOf(await breeding.getAddress()) + 1n; // More than contract's balance
            const newWithdrawalRequestId = 999n; // Use a different withdrawal request ID
            
            const messageHash = ethers.solidityPackedKeccak256(
                ["address", "uint256", "uint256"],
                [player.address, newWithdrawalRequestId, largeAmount]
            );
            const largeAmountSignature = await signer.signMessage(ethers.getBytes(messageHash));

            await expect(
                breeding.connect(player).claimNinunoBalance(
                    newWithdrawalRequestId,
                    largeAmount,
                    largeAmountSignature
                )
            ).to.be.revertedWithCustomError(breeding, "InsufficientNinunoBalance");
        });

        it("Should revert when using already used withdrawal request ID", async function() {
            // Attempt to claim again with same withdrawal request ID
            await expect(
                breeding.connect(player).claimNinunoBalance(
                    withdrawalRequestId,
                    claimAmount,
                    signature
                )
            ).to.be.revertedWithCustomError(breeding, "WithdrawalRequestIdAlreadyUsed");
        });

        it("Should allow multiple users to claim with different request IDs", async function() {
            const [_, otherPlayer] = await ethers.getSigners();
            const otherRequestId1 = 2n;
            const otherRequestId2 = 3n;

            // Generate signature for other player
            const otherMessageHash1 = ethers.solidityPackedKeccak256(
                ["address", "uint256", "uint256"],
                [otherPlayer.address, otherRequestId1, claimAmount]
            );
            const otherSignature1 = await signer.signMessage(ethers.getBytes(otherMessageHash1));
            
            const otherMessageHash2 = ethers.solidityPackedKeccak256(
                ["address", "uint256", "uint256"],
                [player.address, otherRequestId2, claimAmount]
            );
            const otherSignature2 = await signer.signMessage(ethers.getBytes(otherMessageHash2));

            // First player claims
            await expect(
                breeding.connect(player).claimNinunoBalance(
                    otherRequestId2,
                    claimAmount,
                    otherSignature2
                )
            ).to.emit(breeding, "NinunoBalanceClaimed");

            // Second player claims
            await expect(
                breeding.connect(otherPlayer).claimNinunoBalance(
                    otherRequestId1,
                    claimAmount,
                    otherSignature1
                )
            ).to.emit(breeding, "NinunoBalanceClaimed");
        });

        it("Should track used withdrawal request IDs correctly", async function() {
            expect(await breeding.usedWithdrawalRequestIds(10n))
                .to.be.false;

            const messageHash = ethers.solidityPackedKeccak256(
                ["address", "uint256", "uint256"],
                [player.address, 10n, claimAmount]
            );
            const signature = await signer.signMessage(ethers.getBytes(messageHash));

           const tx = await breeding.connect(player).claimNinunoBalance(
                10n,
                claimAmount,
                signature
            );

            await tx.wait();

            expect(await breeding.usedWithdrawalRequestIds(10n))
                .to.be.true;
        });
    });
});
