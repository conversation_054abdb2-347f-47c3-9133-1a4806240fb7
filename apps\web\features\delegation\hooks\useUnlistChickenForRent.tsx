"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { chain } from "@/providers/web3/web3-provider";
import { rentalAbi } from "@/providers/web3/abi/rental-abi";
import { DelegationAPI } from "../api/delegation.api";

/**
 * Hook for handling the complete chicken unlisting process
 * Follows the pattern from useRentChicken for blockchain interactions
 */
export const useUnlistChickenForRent = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const queryClient = useQueryClient();

  const [isUnlisting, setIsUnlisting] = useState(false);

  // Get rental contract address from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the complete unlisting process:
   * 1. Call API to get signature for cancellation
   * 2. Execute blockchain transaction
   * 3. Handle success/error states
   */
  const executeUnlistChickenForRent = async (rentalId: number) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot unlist chicken", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return;
      }

      if (!rentalAddress) {
        toast.error("Cannot unlist chicken", {
          description: "Rental contract not configured",
          position: "top-right",
        });
        return;
      }

      setIsUnlisting(true);

      // Step 1: Get signature from API for cancellation
      toast.info("Preparing to unlist chicken...", {
        description: "Getting signature from server",
        position: "top-center",
      });

      const cancelResponse = await DelegationAPI.cancelRental(rentalId);

      if (cancelResponse.status !== 1 || !cancelResponse.data) {
        throw new Error("Failed to get unlisting signature from server");
      }

      const unlistingData = cancelResponse.data;

      if (!unlistingData.signature) {
        throw new Error("No signature received from server");
      }

      const {
        rentalId: contractRentalId,
        chickenTokenId,
        signature,
      } = unlistingData;

      // Step 2: Simulate the contract call
      toast.info("Simulating transaction...", {
        description: "Checking transaction validity",
        position: "top-center",
      });

      console.log("Unlisting parameters:", {
        rentId: BigInt(contractRentalId),
        chickenId: BigInt(chickenTokenId),
        signature: signature as `0x${string}`,
      });

      await publicClient.simulateContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRent",
        args: [
          BigInt(contractRentalId),
          BigInt(chickenTokenId),
          signature as `0x${string}`,
        ],
        chain,
        account: address,
      });

      // Step 3: Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRent",
        args: [
          BigInt(contractRentalId),
          BigInt(chickenTokenId),
          signature as `0x${string}`,
        ],
        account: address,
      });

      // Step 4: Execute the transaction
      toast.info("Unlisting chicken...", {
        description: "Please confirm the transaction in your wallet",
        position: "top-center",
      });

      const hash = await walletClient.writeContract({
        address: rentalAddress,
        abi: rentalAbi,
        functionName: "unlistChickenForRent",
        args: [
          BigInt(contractRentalId),
          BigInt(chickenTokenId),
          signature as `0x${string}`,
        ],
        gas: gasEstimate + BigInt(50000), // Add buffer
        chain,
        account: address,
      });

      // Step 5: Wait for transaction confirmation
      toast.info("Confirming transaction...", {
        description: "Waiting for blockchain confirmation",
        position: "top-center",
      });

      const receipt = await publicClient.waitForTransactionReceipt({
        hash,
        confirmations: 1,
      });

      if (receipt.status === "success") {
        toast.success("Chicken unlisted successfully!", {
          description: "Your chicken has been removed from the marketplace",
          position: "top-center",
        });

        // Invalidate relevant queries to refresh data
        setTimeout(() => {
          queryClient.invalidateQueries({ queryKey: ["available-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
          queryClient.invalidateQueries({ queryKey: ["chickens"] });
          queryClient.invalidateQueries({ queryKey: ["my-rentals", address] });
          queryClient.invalidateQueries({ queryKey: ["chickens", address] });
          queryClient.invalidateQueries({
            queryKey: ["chickenRentalStatuses"],
          });
        }, 500);

        return {
          success: true,
          hash,
          receipt,
          rentalId: contractRentalId,
        };
      } else {
        throw new Error("Transaction failed");
      }
    } catch (error) {
      console.error("Unlist chicken failed:", error);

      let errorMessage = "Failed to unlist chicken";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.message.includes("User rejected")) {
          errorDescription = "Transaction was cancelled by user";
        } else if (error.message.includes("insufficient funds")) {
          errorDescription = "Insufficient funds for gas fees";
        } else if (error.message.includes("already rented")) {
          errorDescription = "Cannot unlist an active rental";
        } else if (error.message.includes("not owned")) {
          errorDescription = "You don't own this chicken";
        } else if (error.message.includes("not found")) {
          errorDescription = "Rental not found";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error };
    } finally {
      setIsUnlisting(false);
    }
  };

  // Mutation for React Query integration
  const unlistChickenMutation = useMutation({
    mutationFn: executeUnlistChickenForRent,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Unlist chicken mutation error:", error);
    },
  });

  return {
    executeUnlistChickenForRent,
    unlistChickenMutation,
    isUnlisting,
    rentalAddress,
  };
};
