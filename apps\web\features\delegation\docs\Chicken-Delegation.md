## **Chicken Delegation**

## **Core Mechanics**

The Delegation System allows chicken owners to assign specific roles to other players. Each chicken can be delegated for one or, two of the following activities:

* **Daily Rub Delegation**  
  Delegate your chicken to another player for daily rubbing and Feather generation. Delegators choose the Feather distribution and who receives the rubbing streak.

* **Gameplay Delegation**  
  Assign a player to use your chicken in battles. Delegators decide who keeps the rewards from battles. Delegatees will receive the wins, leaderboard progression, and any leaderboard rewards (if applicable).  
---

## **General Rules**

* You can delegate one or more activities (Daily Rub, Gameplay) to a single person, or even all three activities.  
* But take note: each chicken can only be delegated to one person at a time, regardless of how many activities you enable.  
* Owners can still perform any of the three activities (Daily Rub, Gameplay) that are not delegated. Only the delegated activity is restricted and will be locked from the owner's access during the active delegation.  
* Only the chicken owner (delegator) can customize the terms for each delegated activity.  
* All permissions, durations, and reward-sharing settings are defined before delegation starts and cannot be changed mid-session.  
* All delegatee actions on a delegated chicken will be tracked to help owners monitor activity and performance more easily.  
  * Rubs Done 🪶  
  * Matches Played / Winrate ⚔️  
* A mass delegation feature will be available to allow chicken owners to delegate multiple chickens more efficiently.  
* Mass undelegation will also be supported for quick revoking of multiple delegations in one go.

## **Daily Rub Delegation**

### **Key Features**

1. **Feather Distribution**  
   * When chickens are delegated for rubbing, the delegator will have the option to choose how the Feathers earned from the rubs are distributed between themselves and the delegatee.  
   * The distribution options (e.g. Genesis) will be:  
     * **Option 1:** **Delegator Only** (15 Feathers go to the delegator).  
     * **Option 2:** **Delegatee Only** (15 Feathers go to the delegatee).  
2. **Delegation Duration**  
   * The delegator can choose the **number of days** they wish to delegate their chicken. During this time, the delegator cannot modify any of the rules or settings of the delegation.  
   * Alternatively, the delegator can choose to **delegate indefinitely**. In this case, the delegation will continue until the delegator decides to revoke it.  
   * The delegator cannot revoke the delegation if a specific number of days has been set. Any changes to the delegation terms can only be made once the current delegation period ends.  
   * If set to indefinite, a 24-hour grace period starts once the delegator initiates a revocation before it officially ends.  
3. **Daily Rub**  
   * The delegatee will perform the daily rub on the delegated chicken to generate Feathers. The amount of Feathers generated depends on the chicken's rarity.  
4. **Rubbing Streak**  
   * The delegator will get the rubbing streak.  
5. **Feather Claiming**  
   * Both the delegator and delegatee can claim their earned Feathers at any time after the rub. Feathers will be credited to their accounts based on the agreed distribution.  
6. **Delegation Notifications**  
   * Both the delegator and delegatee will receive notifications when:  
     * A new rub is completed  
     * The delegation ends  
     * The delegation is revoked  
     * 24 hours before delegation ends

---

**Battle Delegation**

### **Key Features**

1. **Battle Access**  
   * Delegatees will be able to use the delegated chicken in **all eligible battle modes**.  
2. **Reward Distribution & Win Tracking**  
   * The **delegator** can choose who receives the **battle rewards** (e.g., Feathers, $COCK) earned from individual matches.  
   * **Delegatee** receives the **win record**, **match history**, and **leaderboard progression** for any wins or losses incurred during the delegation.  
   * If other future competitive systems offer rewards (e.g., Feather, $COCK, Badges), they will be awarded to the delegatee unless otherwise specified in future delegation updates.  
3. **Match History & Tracking**  
   * The following will be tracked and visible to the delegator:  
     * Total Matches Played  
     * Win/Loss Record  
     * Winrate %  
     * Materials and tokens earned by the chicken  
     * Critical Events (e.g., disconnections, forfeits)  
4. **Delegation Duration**  
   * The delegator can select a **specific number of days** for battle delegation or set it to **indefinite**.  
   * During an active session, **delegation settings cannot be altered**.  
   * If set to indefinite, the delegation can be revoked at any time by the delegator.  
   * If set to indefinite, a 24-hour grace period starts once the delegator initiates a revocation before it officially ends.  
5. **Chicken Record**  
   * Wins and losses by the delegatee will be **permanently recorded** in the chicken’s official battle log.  
   * The chicken’s overall stats and performance profile will reflect all battles, regardless of who played them.

---

## **Optional Add-ons**

* Leaderboards for top-performing delegates.

