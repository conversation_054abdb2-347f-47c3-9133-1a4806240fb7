import { ethers } from "hardhat";
import { HardhatRuntimeEnvironment } from "hardhat/types";
import TransparentUpgradeableProxy from "hardhat-deploy/extendedArtifacts/TransparentUpgradeableProxy.json";
import { SabongSagaRentalUpgradeable__factory } from "../../typechain-types";
import abi from "./abi.json";

const rentalInterface = SabongSagaRentalUpgradeable__factory.createInterface();

const deploy = async ({
  getNamedAccounts,
  deployments,
  network,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();

  // Encode initialization data
  // Replace with actual fee wallet address and signer address for production
  const feeWallet = "******************************************";
  const feePercentage = 250; // 2.5%
  const signerAddress = "******************************************"; // Add signer address

  // Deploy Logic Contract
  await deploy("SabongSagaRentalLogic", {
    contract: "SabongSagaRentalUpgradeable",
    from: deployer,
    log: true,
  });

  const proxyAdmin = await deployments.get("SabongSagaRentalProxyAdmin");
  const logicContract = await deployments.get("SabongSagaRentalLogic");

  // Get initialization parameters from abi.json
  const initializeData = rentalInterface.encodeFunctionData("initialize", [
    feeWallet,
    feePercentage,
    deployer,
  ]);

  // Deploy Proxy
  const proxy = await deploy("SabongSagaRentalProxy", {
    contract: TransparentUpgradeableProxy,
    from: deployer,
    log: true,
    args: [logicContract.address, proxyAdmin.address, initializeData],
  });

  // Set the signer address after deployment
  // We need to create a contract instance to call setSigner
  if (network.name !== "hardhat") {
    console.log("Setting signer address...");
    const rentalContract = await ethers.getContractAt(
      "SabongSagaRentalUpgradeable",
      proxy.address
    );

    // Set the signer address
    const tx = await rentalContract.setSigner(signerAddress);
    await tx.wait();
    console.log(`Signer address set to: ${signerAddress}`);
  }
};

deploy.tags = ["SabongSagaRentalProxy"];
deploy.dependencies = [
  "VerifyContracts",
  "SabongSagaRentalProxyAdmin",
  "SabongSagaRentalLogic",
];

export default deploy;
