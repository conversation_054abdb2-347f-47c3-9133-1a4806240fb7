"use client";

import { cn } from "@/components/ui";
import Loading from ".";
import LoadingDots2 from "./loading-dots";

interface ILoadingMessageProps extends React.HTMLAttributes<HTMLDivElement> {
  message?: string;
  className?: string;
}

export default function LoadingMessage({
  message = "Loading",
  className,
  ...props
}: ILoadingMessageProps) {
  return (
    <div
      className={cn("grid place-items-center gap-4 py-8", className)}
      {...props}
    >
      <Loading className="text-primary" />
      <LoadingDots2 message={message} dots="." />
    </div>
  );
}
