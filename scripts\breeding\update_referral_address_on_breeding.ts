import { ethers } from "hardhat";
import abi from "./abi.json";
const signer = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Executing script with the account:", deployer.address);

  const breeding = new ethers.Contract(
    abi.sabong_saga_breeding_address,
    abi.sabong_saga_breeding,
    deployer
  );

  let tx = await breeding.updateReferral(abi.sabong_saga_referral_address);
  await tx.wait();
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
