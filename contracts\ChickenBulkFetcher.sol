// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

import "./interfaces/IERC721Fetcher.sol";

contract ChickenBulkFetcher {
    IERC721Fetcher public legacyChickens;
    IERC721Fetcher public genesisChickens;

    constructor(address _legacyChickenAddress, address _genesisChickenAddress) {
        legacyChickens = IERC721Fetcher(_legacyChickenAddress);
        genesisChickens = IERC721Fetcher(_genesisChickenAddress);
    }

    function getLegacyChickenTokenIdsOfAddress(
        address player
    ) public view returns (uint256[] memory) {
        uint256 currentBalance = legacyChickens.balanceOf(player);
        uint256[] memory tokens = new uint256[](currentBalance);
        for (uint256 i = 0; i < tokens.length; i++) {
            tokens[i] = legacyChickens.tokenOfOwnerByIndex(player, i);
        }
        return (tokens);
    }

    function getGenesisChickenTokenIdsOfAddress(
        address player
    ) public view returns (uint256[] memory) {
        uint256 currentBalance = genesisChickens.balanceOf(player);
        uint256[] memory tokens = new uint256[](currentBalance);
        for (uint256 i = 0; i < tokens.length; i++) {
            tokens[i] = genesisChickens.tokenOfOwnerByIndex(player, i);
        }
        return (tokens);
    }
}
