# Delegation & Rentals API Documentation

This document provides comprehensive documentation for all APIs related to delegation and rentals in the breeding-backend system.

## Table of Contents

1. [Authentication](#authentication)
2. [Rental APIs](#rental-apis)
3. [Data Models](#data-models)
4. [Validation Schemas](#validation-schemas)
5. [<PERSON>rro<PERSON> Handling](#error-handling)
6. [Blockchain Integration](#blockchain-integration)

## Authentication

All protected endpoints require authentication using the `sabongauth` middleware. Include the JWT token in the Authorization header:

```
Authorization: Bearer <jwt_token>
```

## Rental APIs

### Protected Endpoints (Require Authentication)

#### 1. Create Rental/Delegation

**Endpoint:** `POST /rentals/create`
**Middleware:** `sabongauth`
**Controller:** `RentalsController.createRental`
**Validator:** `CreateRentalValidator`

Creates a new rental listing or direct delegation.

**Request Body:**

```json
{
  "chickenTokenId": 123,
  "roninPrice": "1000000000000000000",
  "rentalPeriod": 86400,
  "rewardDistribution": 1,
  "delegatedTask": 3,
  "renterAddress": "0x...",
  "sharedRewardAmount": 50
}
```

**Parameters:**

- `chickenTokenId` (number, required): Token ID of the chicken to rent/delegate
- `roninPrice` (string, required): Price in wei (use "0" for free delegation)
- `rentalPeriod` (number, required): Duration in seconds
- `rewardDistribution` (number, optional): Reward distribution type (1-3, default: 1)
- `delegatedTask` (number, optional): Task type (1-3, default: 3)
- `renterAddress` (string, optional): Direct delegation address (required when roninPrice is "0")
- `sharedRewardAmount` (number, optional): Percentage for shared rewards (1-100, required when rewardDistribution is 3)

**Response (Direct Delegation):**

```json
{
  "status": 1,
  "message": "Chicken delegated successfully"
}
```

**Response (Rental Listing):**

```json
{
  "status": 1,
  "message": "Rental listing created successfully",
  "data": {
    "id": 1,
    "chickenTokenId": 123,
    "ownerAddress": "0x...",
    "roninPrice": "1000000000000000000",
    "rentalPeriod": 86400,
    "status": 0,
    "rewardDistribution": 1,
    "delegatedTask": 3,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 2. List Available Rentals

**Endpoint:** `GET /rentals/available`
**Middleware:** `sabongauth`
**Controller:** `RentalsController.listAvailableRentals`

Retrieves paginated list of available rental listings.

**Query Parameters:**

- `page` (number, optional): Page number (default: 1)
- `pageSize` (number, optional): Items per page (default: 10)

**Response:**

```json
{
  "status": 1,
  "data": {
    "meta": {
      "total": 50,
      "perPage": 10,
      "currentPage": 1,
      "lastPage": 5,
      "firstPage": 1,
      "firstPageUrl": "/?page=1",
      "lastPageUrl": "/?page=5",
      "nextPageUrl": "/?page=2",
      "previousPageUrl": null
    },
    "data": [
      {
        "id": 1,
        "chickenTokenId": 123,
        "ownerAddress": "0x...",
        "roninPrice": "1000000000000000000",
        "rentalPeriod": 86400,
        "status": 0,
        "rewardDistribution": 1,
        "delegatedTask": 3,
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ]
  }
}
```

#### 3. Rent Chicken

**Endpoint:** `POST /rentals/rent`
**Middleware:** `sabongauth`
**Controller:** `RentalsController.rentChicken`
**Validator:** `RentChickenValidator`

Generates signature for renting a chicken.

**Request Body:**

```json
{
  "rentalId": 1
}
```

**Response:**

```json
{
  "status": 1,
  "data": {
    "rentalId": 1,
    "renterAddress": "0x...",
    "roninPrice": "1000000000000000000",
    "signature": "0x..."
  }
}
```

#### 4. My Rentals

**Endpoint:** `GET /rentals/my-rentals`
**Middleware:** `sabongauth`
**Controller:** `RentalsController.myRentals`

Retrieves user's rental history and active rentals.

**Response:**

```json
{
  "status": 1,
  "data": [
    {
      "id": 1,
      "chickenTokenId": 123,
      "ownerAddress": "0x...",
      "renterAddress": "0x...",
      "roninPrice": "1000000000000000000",
      "rentalPeriod": 86400,
      "rentedAt": "2024-01-01T00:00:00.000Z",
      "expiresAt": "2024-01-02T00:00:00.000Z",
      "status": 1,
      "rewardDistribution": 1,
      "delegatedTask": 3
    }
  ]
}
```

#### 5. Cancel Rental

**Endpoint:** `POST /rentals/cancel`
**Middleware:** `sabongauth`
**Controller:** `RentalsController.cancelRental`

Cancels an active rental or listing.

**Request Body:**

```json
{
  "rentalId": 1
}
```

**Response:**

```json
{
  "status": 1,
  "message": "Rental cancelled successfully"
}
```

### Public Endpoints (No Authentication Required)

#### 1. Get Chicken Rental

**Endpoint:** `GET /rentals/chicken/:chickenTokenId`
**Controller:** `RentalsController.getChickenRental`

Retrieves active rental information for a specific chicken.

**Parameters:**

- `chickenTokenId` (path parameter): Token ID of the chicken

**Response (Rental Found):**

```json
{
  "status": 1,
  "data": {
    "id": 1,
    "chickenTokenId": 123,
    "ownerAddress": "0x...",
    "roninPrice": "1000000000000000000",
    "rentalPeriod": 86400,
    "status": 0,
    "rewardDistribution": 1,
    "delegatedTask": 3,
    "createdAt": "2024-01-01T00:00:00.000Z"
  }
}
```

**Response (No Rental):**

```json
{
  "status": 0,
  "data": null
}
```

#### 2. Get Chicken Rentals Bulk

**Endpoint:** `POST /rentals/chickens-info-bulk`
**Controller:** `RentalsController.getChickenRentalsBulk`

Retrieves active rental information for multiple chickens in a single request.

**Request Body:**

```json
{
  "chickenTokenIds": [123, 456, 789]
}
```

**Response:**

```json
{
  "status": 1,
  "data": {
    "123": {
      "id": 1,
      "chickenTokenId": 123,
      "ownerAddress": "0x...",
      "roninPrice": "1000000000000000000",
      "rentalPeriod": 86400,
      "status": 0,
      "rewardDistribution": 1,
      "delegatedTask": 3,
      "createdAt": "2024-01-01T00:00:00.000Z"
    },
    "456": null,
    "789": {
      "id": 2,
      "chickenTokenId": 789,
      "ownerAddress": "0x...",
      "roninPrice": "2000000000000000000",
      "rentalPeriod": 172800,
      "status": 1,
      "rewardDistribution": 2,
      "delegatedTask": 1,
      "createdAt": "2024-01-01T12:00:00.000Z"
    }
  }
}
```

**Notes:**

- Returns a mapping of chickenTokenId -> rental data
- Chickens with no active rental are mapped to `null`
- More efficient than making individual requests for each chicken

#### 3. Get Chickens by Wallet

**Endpoint:** `GET /rentals/chickens-by-wallet`
**Controller:** `RentalsController.getChickenRentalsByWallet`

Retrieves rental information for chickens owned by a specific wallet.

**Query Parameters:**

- `walletAddress` (string, required): Wallet address to query

**Response:**

```json
{
  "status": 1,
  "data": [
    {
      "delegatedTask": 3,
      "rewardDistribution": 1,
      "sharedRewardAmount": null,
      "renterAddress": "0x...",
      "ownerAddress": "0x...",
      "tokenId": 123,
      "image": "https://...",
      "dailyFeathers": 15,
      "legendaryCount": 2
    }
  ]
}
```

## Data Models

### Rental Model

```typescript
interface IRental {
  id: number;
  chickenTokenId: number;
  ownerAddress: string;
  renterAddress: string | null;
  roninPrice: bigint;
  rentalPeriod: number;
  rentedAt: DateTime | null;
  expiresAt: DateTime | null;
  status: RentalStatus;
  signature: string | null;
  rewardDistribution: RewardDistributionType;
  delegatedTask: DelegatedTaskType;
  sharedRewardAmount: number | null;
  createdAt: DateTime;
  updatedAt: DateTime;
}
```

### Enums

#### RentalStatus

```typescript
enum RentalStatus {
  AVAILABLE = 0,
  RENTED = 1,
  EXPIRED = 2,
  CANCELLED = 3,
}
```

#### RewardDistributionType

```typescript
enum RewardDistributionType {
  DELEGATOR_ONLY = 1, // All rewards go to delegator
  DELEGATEE_ONLY = 2, // All rewards go to delegatee
  SHARED = 3, // Rewards shared based on sharedRewardAmount
}
```

#### DelegatedTaskType

```typescript
enum DelegatedTaskType {
  DAILY_RUB = 1, // Only daily rub delegation
  GAMEPLAY = 2, // Only gameplay delegation
  BOTH = 3, // Both daily rub and gameplay
}
```

## Validation Schemas

### CreateRentalValidator

```typescript
{
  chickenTokenId: number;                    // Required
  roninPrice: string;                        // Required (wei format)
  rentalPeriod: number;                      // Required (seconds)
  rewardDistribution?: number;               // Optional (1-3, default: 1)
  delegatedTask?: number;                    // Optional (1-3, default: 3)
  renterAddress?: string;                    // Required when roninPrice is "0"
  sharedRewardAmount?: number;               // Required when rewardDistribution is 3, daily feathers for delegatee
}
```

### RentChickenValidator

```typescript
{
  rentalId: number; // Required
}
```

## Error Handling

### Common Error Responses

#### 401 Unauthorized

```json
{
  "status": 0,
  "message": "Unauthorized"
}
```

#### 400 Bad Request

```json
{
  "status": 0,
  "message": "Chicken not found"
}
```

```json
{
  "status": 0,
  "message": "Rental not found"
}
```

```json
{
  "status": 0,
  "message": "Rental is not available"
}
```

```json
{
  "status": 0,
  "message": "You cannot rent your own chicken"
}
```

#### Validation Errors

```json
{
  "status": 0,
  "errors": [
    {
      "field": "chickenTokenId",
      "rule": "required",
      "message": "chickenTokenId is required"
    }
  ]
}
```

## Blockchain Integration

### Smart Contract Integration

The rental system integrates with blockchain smart contracts for:

1. **Signature Generation**: Uses `keccak256` and `encodePacked` for creating rental signatures
2. **Contract Reading**: Utilizes `contractReader.ts` for reading blockchain state
3. **ABI Integration**: Uses `rentalAbi` from contract configurations

### Signature Process

When a user wants to rent a chicken, the system:

1. Validates the rental request
2. Generates a hash using `keccak256(encodePacked([address, uint256, uint256], [renterAddress, rentalId, roninPrice]))`
3. Signs the hash using the system's private key
4. Returns the signature for blockchain transaction

### Contract Configuration

Rental contract configuration is stored in `config/sabong.ts`:

```typescript
import { rentalAbi } from "./abi/rental-abi";

export default {
  // ... other config
  RENTAL_CONTRACT_ADDRESS: process.env.RENTAL_CONTRACT_ADDRESS,
  RENTAL_ABI: rentalAbi,
  SIGNER_KEY: process.env.SIGNER_PRIVATE_KEY,
};
```

## Related APIs

### Daily Rub APIs (apps/api)

For delegation-related daily rub functionality:

- `POST /chickens/daily-rub` - Perform daily rub (requires auth)
- `GET /chickens/able-to-rub` - Check if able to rub (requires auth)

### Frontend Integration

The frontend integrates with these APIs through:

- `apps/web/lib/api/index.ts` - Main API client

## Notes

1. **Delegation vs Rental**: When `roninPrice` is "0" and `renterAddress` is provided, it creates a direct delegation. Otherwise, it creates a rental listing.

2. **Reward Distribution**: The `rewardDistribution` field determines how rewards are split between delegator and delegatee.

3. **Task Types**: The `delegatedTask` field specifies which activities are delegated (daily rub, gameplay, or both).

4. **Expiration**: Rentals automatically expire based on the `rentalPeriod` and `rentedAt` timestamp.

5. **Metadata Integration**: The system fetches chicken metadata from external APIs to enrich rental information.
