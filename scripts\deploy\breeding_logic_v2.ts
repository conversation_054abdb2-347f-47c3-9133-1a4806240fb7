import { HardhatRuntimeEnvironment } from "hardhat/types";

const deploy = async ({
  getNamedAccounts,
  deployments,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  
  await deploy("SabongSagaBreedingLogicV2", {
    contract: "SabongSagaBreedingUpgradeable",
    from: deployer,
    log: true,
  });
};

deploy.tags = ["SabongSagaBreedingLogicV2"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;