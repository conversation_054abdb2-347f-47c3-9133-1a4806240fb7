import erc1155Abi from "@/abi/Erc1155.abi.json";
import { api, API_ENDPOINTS } from "@/lib/api/api-helper";
import { toast } from "sonner";
import { Address, erc20Abi, PublicClient } from "viem";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import useChickenStore from "./chicken";

// Constants
const STORAGE_KEY = "auth-state";
const FEATHERS_TOKEN_ID = 1;
const LEGENDARY_FEATHER_TOKEN_ID = 2;

// Contract addresses
const ADDRESSES = {
  COCK_TOKEN: process.env.NEXT_PUBLIC_COCK_CONTRACT as Address,
  FEATHERS_TOKEN: process.env.NEXT_PUBLIC_FEATHERS_CONTRACT as Address,
  FOOD_CRAFTING: process.env.NEXT_PUBLIC_FOODCRAFTING_CONTRACT as Address,
  RESOURCES_ADDRESS: process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address,
};

interface AuthState {
  address?: Address;
  streak: number;
  ableToClaim: boolean;
  legendaryClaimableFeathers: number;
  feathers: bigint;
  claimableFeathers: number;
  ronin: bigint;
  cock: bigint;
  ************************: boolean;
  isResourcesApprovedForAll: boolean;
  legendaryFeathers: bigint;
  cockAllowance: bigint;
  rns: string | null;
}

interface PublicActions {
  getMe: () => Promise<void>;
  getRNS: () => Promise<void>;
  reset: () => void;
  fetchBalances: (
    isConnected: boolean,
    publicClient: PublicClient,
    address: Address | undefined
  ) => Promise<void>;
  checkApprovals: (
    isConnected: boolean,
    publicClient: PublicClient,
    address: Address | undefined
  ) => Promise<void>;
  onInit: (
    isConnected: boolean,
    publicClient: PublicClient,
    address: Address | undefined
  ) => Promise<void>;
}

type StoreState = AuthState & PublicActions;

// Initial state
const initialState: AuthState = {
  claimableFeathers: 0,
  legendaryClaimableFeathers: 0,
  feathers: 0n,
  ableToClaim: false,
  streak: 0,
  ronin: 0n,
  cock: 0n,
  cockAllowance: 0n,
  ************************: false,
  rns: null,
  isResourcesApprovedForAll: false,
  legendaryFeathers: 0n,
};

// Store implementation
const useAuthStore = create<StoreState>()(
  persist(
    (set, get) => {
      return {
        ...initialState,

        onInit: async (
          isConnected: boolean,
          publicClient: PublicClient,
          address: Address | undefined
        ) => {
          set({ address });
          const { getChickens } = useChickenStore.getState();
          await Promise.all([
            get().getMe(),
            get().getRNS(),
            get().checkApprovals(isConnected, publicClient, address),
            get().fetchBalances(isConnected, publicClient, address),
            getChickens(isConnected),
          ]);
        },

        fetchBalances: async (
          isConnected: boolean,
          publicClient: PublicClient,
          address: Address | undefined
        ) => {
          if (!isConnected) {
            toast.error("Cannot fetch balances", {
              description: "Wallet not connected",
              position: "top-right",
            });
            return;
          }

          try {
            const [roninBalance, cockBalance, feathersBalance, legendaryBal] =
              await Promise.all([
                // Fetch native RON balance
                publicClient?.getBalance({ address: address as Address }),

                // Fetch COCK token balance (ERC20)
                publicClient?.readContract({
                  address: ADDRESSES.COCK_TOKEN,
                  abi: erc20Abi,
                  functionName: "balanceOf",
                  args: [address as Address],
                }) as Promise<bigint>,

                // Fetch Feathers balance (ERC1155)
                publicClient?.readContract({
                  address: ADDRESSES.FEATHERS_TOKEN,
                  abi: erc1155Abi,
                  functionName: "balanceOf",
                  args: [address as Address, FEATHERS_TOKEN_ID],
                }) as Promise<bigint>,
                publicClient?.readContract({
                  address: ADDRESSES.FEATHERS_TOKEN,
                  abi: erc1155Abi,
                  functionName: "balanceOf",
                  args: [address as Address, LEGENDARY_FEATHER_TOKEN_ID],
                }) as Promise<bigint>,
              ]);

            set({
              ronin: roninBalance,
              cock: cockBalance,
              feathers: feathersBalance,
              legendaryFeathers: legendaryBal,
            });
          } catch (error) {
            console.error("fetchBalance: ", error);
          }
        },

        checkApprovals: async (
          isConnected: boolean,
          publicClient: PublicClient,
          address: Address | undefined
        ) => {
          if (!isConnected || !ADDRESSES.FOOD_CRAFTING) {
            toast.error("Cannot check approvals", {
              description: "Wallet not connected or spender address not set",
              position: "top-right",
            });
            return;
          }

          try {
            const [cockAllowance, isApprovedForAll, isResourcesApprovedForAll] =
              await Promise.all([
                // Check ERC20 allowance for COCK token
                publicClient?.readContract({
                  address: ADDRESSES.COCK_TOKEN,
                  abi: erc20Abi,
                  functionName: "allowance",
                  args: [address as Address, ADDRESSES.FOOD_CRAFTING],
                }) as Promise<bigint>,

                // Check if ERC1155 is approved for all
                publicClient?.readContract({
                  address: ADDRESSES.FEATHERS_TOKEN,
                  abi: erc1155Abi,
                  functionName: "isApprovedForAll",
                  args: [address as Address, ADDRESSES.FOOD_CRAFTING],
                }) as Promise<boolean>,
                // Check if ERC1155 is approved for all
                publicClient?.readContract({
                  address: ADDRESSES.RESOURCES_ADDRESS,
                  abi: erc1155Abi,
                  functionName: "isApprovedForAll",
                  args: [address as Address, ADDRESSES.FOOD_CRAFTING],
                }) as Promise<boolean>,
              ]);

            set({
              cockAllowance,
              ************************: isApprovedForAll,
              isResourcesApprovedForAll: isResourcesApprovedForAll,
            });
          } catch (error) {
            console.error("checkApprovals: ", error);
          }
        },
        getRNS: async () => {
          try {
            const { address } = get();
            const response = await api.call(`/api/rns/${address}`);
            const data = response;

            set({
              rns: data.rns,
            });
          } catch (error) {
            set({
              rns: null,
            });
          }
        },

        getMe: async () => {
          try {
            const response = await api.call(API_ENDPOINTS.me);
            const data = response.data.data;

            set({
              ableToClaim: data.ableToClaim,
              streak: data.streak,
              claimableFeathers: data.feathers,
              legendaryClaimableFeathers: data.legendaryClaimableFeathers,
            });
          } catch (error) {
            console.error("getMe:", error);
            window.stateContext?.Disconnect();
          }
        },

        reset: () => {
          set({ ...initialState });
        },
      };
    },
    {
      name: STORAGE_KEY,
      partialize: () => ({}),
    }
  )
);

export default useAuthStore;
