# Rub Results Delegation Distribution

## Overview

This feature enhances the rub results dialog to show how feathers are distributed between chicken owners and renters/delegated users when delegated chickens are involved in the daily rub. The system supports complex multi-party scenarios where users can have multiple delegatees/renters or multiple owners.

## How It Works

### 1. Regular Rub Results (No Delegation)

When no chickens are delegated, the rub results dialog shows the standard view:

- Total feathers earned
- Total legendary feathers earned
- Simple close button

### 2. Multi-Party Delegation Distribution View

When delegated chickens are involved, the dialog shows:

- **User Section**: Feathers that go to the current user (with role indicator)
- **Other Parties Section**: Feathers distributed to all other parties involved
- **Party Details**: Each party shows their address, role (Owner/Renter), and feather amount
- **Party Count**: Summary of how many other parties are involved
- **Distribution Logic**: Based on the delegation terms set when creating each delegation

### 3. Dual Perspective Support

The system works from both perspectives:

- **Owner Perspective**: When you rub chickens you own and have delegated to others
- **Renter Perspective**: When you rub chickens you've rented from others

## Distribution Logic

The feather distribution follows the delegation settings:

### 1. Delegator Only (`ERewardDistributionType.DELEGATOR_ONLY`)

- **Owner gets**: 100% of feathers
- **<PERSON><PERSON> gets**: 0% of feathers
- This is the default setting for most delegations

### 2. Delegatee Only (`ERewardDistributionType.DELEGATEE_ONLY`)

- **Owner gets**: 0% of feathers
- **Renter gets**: 100% of feathers
- Used when fully delegating rewards

### 3. Shared Distribution (`ERewardDistributionType.SHARED`)

- **Owner gets**: (100 - sharedRewardAmount)% of feathers
- **Renter gets**: sharedRewardAmount% of feathers
- The `sharedRewardAmount` is set when creating the delegation (1-99%)

## Multi-Party Distribution Algorithm

### Party Aggregation Logic

The system uses a sophisticated algorithm to handle multiple parties:

1. **Party Mapping**: Creates a `Map<address, distribution>` to track each unique party
2. **Per-Chicken Processing**: Processes each delegated chicken individually
3. **Distribution Calculation**: Applies delegation terms per chicken
4. **Party Aggregation**: Combines feathers for the same party address
5. **Result Filtering**: Only shows parties with non-zero feather amounts

### Example Algorithm Flow

```typescript
// For each delegated chicken
delegatedChickens.forEach((chicken) => {
  const otherPartyAddress =
    userRole === "owner" ? chicken.renterAddress : chicken.ownerAddress;

  // Apply delegation terms
  switch (chicken.rewardDistribution) {
    case SHARED:
      const renterShare = chickenFeathers * (sharedRewardAmount / 100);
      // Add to party's total
      partyDistributions.get(otherPartyAddress).feathers += renterShare;
      break;
    // ... other cases
  }
});
```

### Perspective Handling

- **Owner Perspective**: Aggregates distributions to multiple renters
- **Renter Perspective**: Aggregates distributions from multiple owners
- **Role Detection**: Automatically determines user role based on delegation data
- **Address Mapping**: Maps chicken addresses to determine other parties

## Technical Implementation

### New Interface Design

**Multi-Party Distribution Interface:**

```typescript
interface IPartyDistribution {
  address: string;
  feathers: number;
  legendaryFeathers: number;
  role: "owner" | "renter";
}

interface IDelegationDistribution {
  userFeathers: number; // What the current user gets
  userLegendaryFeathers: number;
  userRole: "owner" | "renter"; // Current user's role
  otherParties: IPartyDistribution[]; // All other parties involved
  totalFeathers: number;
  totalLegendaryFeathers: number;
}
```

### Components Modified

1. **RubResult Component** (`apps/web/components/pages/home/<USER>/rub-result.tsx`)

   - **New Interface**: Updated `IDelegationDistribution` to support multiple parties
   - **New Component**: `MultiPartyDelegationDistribution` for showing multi-party distributions
   - **Perspective Aware**: Displays appropriate labels based on user role (Owner/Renter)
   - **Party Aggregation**: Shows all parties involved with their individual distributions

2. **RubContent Component** (`apps/web/components/pages/home/<USER>/rubContent.tsx`)

   - Integrated `useDelegationDistribution` hook
   - Calculates delegation distribution when showing results
   - Passes distribution data to RubResult component
   - Supports both owner and renter perspectives

3. **useDelegationDistribution Hook** (`apps/web/features/delegation/hooks/useDelegationDistribution.tsx`)
   - **Dual Perspective**: Handles both owner and renter perspectives
   - **Multi-Party Logic**: Aggregates distributions by party address
   - **Smart Detection**: Automatically determines user role and active delegations
   - **Complex Calculations**: Supports mixed delegation terms per chicken

### Key Features

- **Multi-Party Support**: Handles multiple delegatees/renters and multiple owners
- **Automatic Detection**: Detects delegated chickens from both owner and renter perspectives
- **Party Aggregation**: Groups feathers by party address for clean display
- **Accurate Distribution**: Calculates exact feather splits based on individual delegation settings
- **Visual Distinction**: Uses different colors (purple) for other parties' portions
- **Address Display**: Shows truncated addresses for all parties involved
- **Role Indicators**: Clear labeling of Owner/Renter roles for each party
- **Party Count**: Shows summary of how many other parties are involved
- **Responsive Design**: Works on both desktop and mobile devices

## Example Scenarios

### Scenario 1: Owner with Multiple Delegatees

**Setup:**

- User has 6 chickens total
- Chicken 1 & 2: Delegated to Renter A with "Shared 30%"
- Chicken 3: Delegated to Renter B with "Shared 40%"
- Chicken 4: Delegated to Renter A with "Delegatee Only"
- Chicken 5 & 6: Owned normally
- Total earned: 60 feathers (10 per chicken)

**Result Display:**

```
Feather Distribution
2 other parties involved

You (Owner): 37 feathers
- Chicken 1: 7 feathers (70% of 10)
- Chicken 2: 7 feathers (70% of 10)
- Chicken 3: 6 feathers (60% of 10)
- Chicken 4: 0 feathers (0% of 10)
- Chicken 5 & 6: 20 feathers (100% of 20)

Renter A (0x1111...): 13 feathers
- Chicken 1: 3 feathers (30% of 10)
- Chicken 2: 3 feathers (30% of 10)
- Chicken 4: 10 feathers (100% of 10)

Renter B (0x2222...): 4 feathers
- Chicken 3: 4 feathers (40% of 10)
```

### Scenario 2: Renter with Multiple Owners

**Setup:**

- User has rented 4 chickens from different owners
- Chicken A & B: From Owner X with "Shared 30%" (user gets 30%)
- Chicken C: From Owner Y with "Shared 50%" (user gets 50%)
- Chicken D: From Owner X with "Delegatee Only" (user gets 100%)
- Total earned: 40 feathers (10 per chicken)

**Result Display:**

```
Feather Distribution
2 other parties involved

You (Renter): 23 feathers
- Chicken A: 3 feathers (30% of 10)
- Chicken B: 3 feathers (30% of 10)
- Chicken C: 5 feathers (50% of 10)
- Chicken D: 10 feathers (100% of 10)

Owner X (0x3333...): 17 feathers
- Chicken A: 7 feathers (70% of 10)
- Chicken B: 7 feathers (70% of 10)
- Chicken D: 0 feathers (0% of 10)

Owner Y (0x4444...): 5 feathers
- Chicken C: 5 feathers (50% of 10)
```

### Scenario 3: Simple Single Party Delegation

**Setup:**

- User has 3 chickens delegated with "Delegatee Only" to single Renter
- Total earned: 30 feathers

**Result Display:**

```
Feather Distribution
1 other party involved

You (Owner): 0 feathers

Renter (0x5555...): 30 feathers
```

## Future Enhancements

1. **Individual Chicken Metadata**: Fetch actual daily feather values for more precise calculations instead of estimated distribution
2. **Historical Tracking**: Track delegation earnings over time for both owners and renters
3. **Notification System**: Notify renters when they receive feathers from delegated chickens
4. **Detailed Breakdown**: Show per-chicken breakdown in the UI for transparency
5. **Export Functionality**: Allow users to export delegation distribution reports
6. **Real-time Updates**: Live updates when delegation terms change during active rentals

## Dependencies

- React Query for data fetching
- Delegation API for rental information
- Existing rub result infrastructure
- TailwindCSS for styling
