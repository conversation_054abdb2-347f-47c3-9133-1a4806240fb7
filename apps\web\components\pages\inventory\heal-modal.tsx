import {
  useState,
  useEffect,
  useMemo,
  useCallback,
  Dispatch,
  SetStateAction,
} from "react";
import Image from "next/image";
import { X, Heart, Swords } from "lucide-react";
import useIapHealStore from "@/store/iap-heal";
import { useIapHealPrices } from "@/hooks/use-iap-prices";
import { formatEther } from "ethers";
import IAP_ITEMS from "@/data/iap_data.json";
import { QueryObserverResult } from "@tanstack/react-query";
import useAuthStore from "@/store/auth";

interface HealModalProps {
  isOpen: boolean;
  onClose: () => void;
  chickenId: string;
  chickenImage?: string;
  currentHp?: number;
  maxHp?: number;
  onHeal: (itemId?: number) => Promise<boolean>;
  healInfo: {
    healsRemaining: number;
    maxHeals: number;
    resetTime: string;
  } | null;
  isLoading: boolean;
  onBattle?: () => void;
  refetch: () => Promise<void | QueryObserverResult<any, Error>>;
}

export default function HealModal({
  isOpen,
  onClose,
  chickenId,
  chickenImage,
  currentHp = 0,
  maxHp = 100,
  onHeal,
  healInfo,
  isLoading,
  onBattle,
  refetch,
}: HealModalProps) {
  const { ronin } = useAuthStore();
  const [healing, setHealing] = useState(false);
  const [healSuccess, setHealSuccess] = useState<boolean | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
  const { isPending, getSignature, verifyStatus, processPurchase } =
    useIapHealStore();
  const {
    data: prices,
    isPending: fetchPricePending,
    isError: errorIapPrice,
  } = useIapHealPrices();

  const priceMap = useMemo(() => {
    if (!prices?.data) return new Map<number, string>();
    const map = new Map<number, string>();
    prices.data.forEach((p: { itemId: number; priceInRon: string }) => {
      map.set(p.itemId, p.priceInRon);
    });
    return map;
  }, [prices?.data]);

  useEffect(() => {
    if (!isOpen) {
      setHealing(false);
      setHealSuccess(null);
      setErrorMessage(null);
      setSelectedItemId(null);
    }
  }, [isOpen]);

  const handleHeal = async () => {
    if (healing) return;
    if (selectedItemId === null) {
      setErrorMessage("Please select an item to heal.");
      return;
    }

    setHealing(true);
    setHealSuccess(null);
    setErrorMessage(null);

    try {
      const success = await onHeal(selectedItemId);
      setHealSuccess(success);
      if (!success) {
        setErrorMessage("Healing failed. Please try again.");
      }
    } catch (error) {
      setHealSuccess(false);
      setErrorMessage(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
    } finally {
      setHealing(false);
    }
  };

  const handleBattle = () => {
    if (onBattle) {
      onClose();
      onBattle();
    }
  };

  if (!isOpen) return null;

  const healHandler = async (itemId: number) => {
    if (itemId === 0) {
      console.log("ehre");

      await handleHeal();
    } else {
      try {
        const purchaseData = await getSignature(itemId, chickenId);
        const txhash = await processPurchase(purchaseData);
        const status = await verifyStatus(txhash);

        if (status.isCalled === false) {
          await refetch();
          setHealSuccess(true);
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const hpPercentage = maxHp > 0 ? (currentHp / maxHp) * 100 : 0;
  const needsHealing = hpPercentage < 100;
  const canHeal = needsHealing && healInfo && healInfo.healsRemaining > 0;
  const isFullHealth = hpPercentage >= 100;
  const canBattle = isFullHealth && currentHp >= 50;

  const hasInsufficientBalance = (
    ronBalance: bigint | undefined,
    priceInWei: string
  ): boolean => {
    if (!ronBalance) return true;
    try {
      const priceAsBigInt = BigInt(priceInWei);
      return ronBalance < priceAsBigInt;
    } catch {
      return true; // If conversion fails, assume insufficient balance
    }
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 overflow-y-auto">
      <div className="bg-stone-900 border border-stone-700 rounded-xl w-full max-w-lg overflow-hidden shadow-lg">
        {/* Header */}
        <div className="flex justify-between items-center p-5 border-b border-stone-700">
          <h3 className="text-2xl font-extrabold text-white tracking-wide">
            {isFullHealth
              ? `Chicken #${chickenId} - Ready!`
              : `Heal Chicken #${chickenId}`}
          </h3>
          <button
            onClick={onClose}
            className="text-stone-400 hover:text-white transition-colors"
            disabled={healing}
            aria-label="Close modal"
          >
            <X className="h-7 w-7" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Chicken Image and HP */}
          <div className="flex flex-col items-center mb-8">
            <div className="w-36 h-36 relative mb-5 rounded-xl overflow-hidden border border-stone-700 shadow-inner bg-stone-800">
              {chickenImage ? (
                <Image
                  src={chickenImage}
                  alt={`Chicken #${chickenId}`}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="w-full h-full bg-stone-700 animate-pulse rounded-xl"></div>
              )}
            </div>

            {/* HP Bar */}
            <div className="w-full bg-stone-700 rounded-full h-7 mb-3 relative shadow-inner">
              <div
                className={`h-7 rounded-full transition-all duration-300 ${
                  hpPercentage > 70
                    ? "bg-green-500"
                    : hpPercentage > 30
                      ? "bg-yellow-500"
                      : "bg-red-600"
                }`}
                style={{ width: `${hpPercentage}%` }}
              />
              <div className="absolute inset-0 flex justify-center items-center text-white font-semibold text-sm select-none">
                HP: {Math.round(currentHp)}/{Math.round(maxHp)} (
                {Math.round(hpPercentage)}%)
              </div>
            </div>
          </div>

          {/* Health Status Message */}
          {isFullHealth && (
            <div className="bg-green-900/60 border border-green-500 text-green-300 p-4 rounded-xl mb-6 text-center font-semibold shadow-md">
              🎉 Perfect Health! Your chicken is ready for battle!
            </div>
          )}

          {/* Heal Info & Item Selection */}
          {!isFullHealth && (
            <>
              {isLoading || fetchPricePending ? (
                <div className="text-center py-6">
                  <div className="animate-spin h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-3"></div>
                  <p className="text-white font-medium">
                    Loading heal information...
                  </p>
                </div>
              ) : healInfo && !errorIapPrice ? (
                <>
                  <div className="flex mb-4">
                    <p className="font-Poppins text-xl font-medium">
                      Select heal item
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4 font-Poppins">
                    {IAP_ITEMS.map((item) => {
                      const priceInwie = item.iapId
                        ? (priceMap.get(item.iapId) ?? "0")
                        : "0";
                      const etherVal = parseFloat(formatEther(priceInwie));
                      const roundedVal = Math.ceil(etherVal * 1000) / 1000;
                      const isSelected = selectedItemId === item.itemId;
                      const isDisabled =
                        (item.itemId === 0 && healInfo.healsRemaining === 0) ||
                        (item.itemId !== 0 &&
                          hasInsufficientBalance(ronin, priceInwie));

                      return (
                        <button
                          disabled={isDisabled}
                          key={item.itemId}
                          type="button"
                          onClick={() => setSelectedItemId(item.itemId)}
                          className={`flex flex-col items-center p-4 rounded-xl border-2 transition-transform duration-300 ease-in-out focus:outline-none
          ${
            isSelected
              ? "border-green-500 bg-green-900 shadow-lg scale-105"
              : "border-transparent bg-stone-900 hover:border-primary hover:bg-stone-800"
          }
          ${isDisabled ? "opacity-50 cursor-not-allowed hover:border-transparent hover:bg-stone-900 scale-100 shadow-none" : ""}
        `}
                          aria-pressed={isSelected}
                        >
                          <img
                            src={item.image}
                            alt={item.itemName}
                            className="w-16 h-16 object-contain rounded-lg mb-3"
                            draggable={false}
                          />
                          <div
                            className={`font-medium ${
                              isSelected ? "text-green-400" : "text-primary/90"
                            }`}
                          >
                            {item.itemName}
                          </div>
                          {item.itemId === 0 ? (
                            <div className="flex flex-col items-center gap-1 mt-1 text-center">
                              <p className="text-sm text-white/80">
                                {healInfo?.healsRemaining} /{" "}
                                {healInfo?.maxHeals}
                              </p>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 mt-1">
                              <img
                                src="/images/ron.webp"
                                alt="Ron currency"
                                className="w-auto h-5"
                                draggable={false}
                              />
                              <p className="text-white font-medium">
                                {roundedVal.toFixed(3)}
                              </p>
                            </div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </>
              ) : (
                <div className="text-center py-6 text-red-400 font-semibold">
                  Could not load heal information
                </div>
              )}
            </>
          )}

          {/* Status Messages */}
          {healSuccess === true && (
            <div className="bg-green-900/70 border border-green-500 text-green-300 p-4 rounded-xl mt-6 mb-4 text-center font-semibold shadow-md">
              Chicken healed successfully! HP restored.
            </div>
          )}

          {healSuccess === false && (
            <div className="bg-red-900/70 border border-red-500 text-red-300 p-4 rounded-xl mt-6 mb-4 text-center font-semibold shadow-md">
              {errorMessage || "Failed to heal chicken. Please try again."}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-center gap-6 mt-4">
            <button
              onClick={() => {
                onClose();
              }}
              className="px-6 py-3 bg-stone-700 text-white rounded-xl hover:bg-stone-600 transition-colors font-semibold shadow-md"
              disabled={healing}
            >
              Close
            </button>

            {isFullHealth ? (
              <button
                onClick={handleBattle}
                disabled={!canBattle || !onBattle}
                className={`px-6 py-3 rounded-xl flex items-center gap-3 font-semibold transition-colors shadow-md ${
                  canBattle && onBattle
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : "bg-stone-700 text-stone-400 cursor-not-allowed"
                }`}
              >
                <Swords className="h-6 w-6" />
                Start Battle
              </button>
            ) : (
              <button
                onClick={
                  selectedItemId != null && !isPending
                    ? () => healHandler(selectedItemId)
                    : () => {}
                }
                disabled={
                  healing ||
                  !canHeal ||
                  healSuccess === true ||
                  selectedItemId === null ||
                  isPending
                }
                className={`px-6 py-3 rounded-xl flex items-center gap-3 font-semibold transition-colors shadow-md ${
                  canHeal && !isPending
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-stone-700 text-stone-400 cursor-not-allowed"
                }`}
              >
                {healing || isPending ? (
                  <>
                    <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                    Healing...
                  </>
                ) : (
                  <>
                    <Heart className="h-6 w-6" />
                    Heal Chicken
                  </>
                )}
              </button>
            )}
          </div>

          {/* Explanation text */}
          {/* {needsHealing && healInfo && healInfo.healsRemaining <= 0 && (
            <p className="text-yellow-400 text-center mt-5 font-medium">
              No free heals remaining today. Check back after reset.
            </p>
          )} */}

          {isFullHealth && !onBattle && (
            <p className="text-stone-400 text-center mt-5 text-sm italic">
              Battle functionality not available
            </p>
          )}
        </div>
      </div>
    </div>
  );
}
