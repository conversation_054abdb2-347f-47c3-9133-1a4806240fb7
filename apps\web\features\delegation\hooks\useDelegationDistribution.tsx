"use client";

import { useQuery } from "@tanstack/react-query";
import { useStateContext } from "@/providers/app/state";
import { DelegationAPI } from "../api/delegation.api";
import {
  ERewardDistributionType,
  EDelegatedTaskType,
} from "../types/delegation.types";
import {
  IDelegationDistribution,
  IPartyDistribution,
} from "@/components/pages/home/<USER>/rub-result";

interface IDelegatedChickenInfo {
  tokenId: number;
  dailyFeathers: number;
  legendaryCount: number;
  rewardDistribution: ERewardDistributionType;
  sharedRewardAmount?: number;
  renterAddress: string;
  ownerAddress: string;
  delegatedTask: EDelegatedTaskType;
}

/**
 * Hook to calculate delegation distribution for rub results
 * This fetches the user's delegated chickens and calculates how feathers should be distributed
 */
export function useDelegationDistribution() {
  const { address, isConnected } = useStateContext();

  // Fetch user's rental information (chickens they've delegated to others)
  const myRentalsQuery = useQuery({
    queryKey: ["my-rentals-for-distribution", address],
    queryFn: async () => {
      if (!address) return null;
      const response = await DelegationAPI.getMyRentals();
      return response.data;
    },
    enabled: !!address && isConnected,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  /**
   * Calculate delegation distribution based on delegation settings
   * Now supports multiple parties (multiple renters for owner, multiple owners for renter)
   */
  const calculateDelegationDistribution = (
    totalFeathers: number,
    totalLegendaryFeathers: number,
    delegatedChickens: IDelegatedChickenInfo[],
    currentUserAddress: string,
    userRole: "owner" | "renter"
  ): IDelegationDistribution | null => {
    if (!delegatedChickens.length) {
      return null;
    }

    // Track distributions per party
    const partyDistributions = new Map<
      string,
      { feathers: number; legendaryFeathers: number; role: "owner" | "renter" }
    >();
    let userFeathers = 0;
    let userLegendaryFeathers = 0;

    // Initialize user's share with total feathers (will be reduced as we distribute to others)
    if (userRole === "owner") {
      userFeathers = totalFeathers;
      userLegendaryFeathers = totalLegendaryFeathers;
    }

    // Calculate distribution for each delegated chicken
    delegatedChickens.forEach((chicken) => {
      // Only process chickens delegated for daily rub
      if (
        chicken.delegatedTask === EDelegatedTaskType.DAILY_RUB ||
        chicken.delegatedTask === EDelegatedTaskType.BOTH
      ) {
        const chickenFeathers = chicken.dailyFeathers || 0;
        const chickenLegendaryFeathers = chicken.legendaryCount || 0;

        // Determine the other party's address and role
        const otherPartyAddress =
          userRole === "owner" ? chicken.renterAddress : chicken.ownerAddress;
        const otherPartyRole = userRole === "owner" ? "renter" : "owner";

        // Initialize other party if not exists
        if (!partyDistributions.has(otherPartyAddress)) {
          partyDistributions.set(otherPartyAddress, {
            feathers: 0,
            legendaryFeathers: 0,
            role: otherPartyRole,
          });
        }

        const otherParty = partyDistributions.get(otherPartyAddress)!;

        switch (chicken.rewardDistribution) {
          case ERewardDistributionType.DELEGATOR_ONLY:
            // All feathers go to owner (delegator)
            if (userRole === "owner") {
              // User keeps all feathers - no change needed
            } else {
              // User is renter but gets nothing, owner gets all
              otherParty.feathers += chickenFeathers;
              otherParty.legendaryFeathers += chickenLegendaryFeathers;
            }
            break;

          case ERewardDistributionType.DELEGATEE_ONLY:
            // All feathers go to renter (delegatee)
            if (userRole === "renter") {
              // User is renter and gets all feathers
              userFeathers += chickenFeathers;
              userLegendaryFeathers += chickenLegendaryFeathers;
            } else {
              // User is owner but gives all to renter
              userFeathers -= chickenFeathers;
              userLegendaryFeathers -= chickenLegendaryFeathers;
              otherParty.feathers += chickenFeathers;
              otherParty.legendaryFeathers += chickenLegendaryFeathers;
            }
            break;

          case ERewardDistributionType.SHARED:
            // Split based on sharedRewardAmount (actual feather amount for delegatee)
            if (chicken.sharedRewardAmount) {
              const renterShare = Math.min(
                chicken.sharedRewardAmount,
                chickenFeathers - 1
              ); // Ensure owner gets at least 1
              const renterLegendaryShare = Math.floor(
                (chickenLegendaryFeathers * renterShare) / chickenFeathers
              ); // Proportional legendary feathers
              const ownerShare = chickenFeathers - renterShare;
              const ownerLegendaryShare =
                chickenLegendaryFeathers - renterLegendaryShare;

              if (userRole === "owner") {
                // User is owner, keeps owner share, gives renter share to other party
                userFeathers = userFeathers - renterShare;
                userLegendaryFeathers =
                  userLegendaryFeathers - renterLegendaryShare;
                otherParty.feathers += renterShare;
                otherParty.legendaryFeathers += renterLegendaryShare;
              } else {
                // User is renter, gets renter share, owner gets owner share
                userFeathers += renterShare;
                userLegendaryFeathers += renterLegendaryShare;
                otherParty.feathers += ownerShare;
                otherParty.legendaryFeathers += ownerLegendaryShare;
              }
            }
            break;
        }
      }
    });

    // Ensure no negative values
    userFeathers = Math.max(0, userFeathers);
    userLegendaryFeathers = Math.max(0, userLegendaryFeathers);

    // Convert party distributions to array and filter out parties with zero feathers
    const otherParties: IPartyDistribution[] = Array.from(
      partyDistributions.entries()
    )
      .filter(
        ([, distribution]) =>
          distribution.feathers > 0 || distribution.legendaryFeathers > 0
      )
      .map(([address, distribution]) => ({
        address,
        feathers: Math.max(0, distribution.feathers),
        legendaryFeathers: Math.max(0, distribution.legendaryFeathers),
        role: distribution.role,
      }));

    // Only return distribution if there are other parties involved
    if (otherParties.length > 0) {
      return {
        userFeathers,
        userLegendaryFeathers,
        userRole,
        otherParties,
        totalFeathers,
        totalLegendaryFeathers,
      };
    }

    return null;
  };

  /**
   * Get delegation distribution for current rub results
   * Note: The API already handles the actual distribution of feathers.
   * This function only determines if delegation info should be shown in the UI.
   */
  const getDelegationDistribution = (
    totalFeathers: number,
    totalLegendaryFeathers: number
  ): IDelegationDistribution | null => {
    if (!myRentalsQuery.data || !address) {
      return null;
    }

    // Check if user is rubbing chickens they own and have delegated to others (owner perspective)
    const ownedDelegations =
      myRentalsQuery.data.ownedRentals?.filter(
        (rental) =>
          rental.status === 1 && // RENTED status
          rental.renterAddress && // Has a renter
          (rental.delegatedTask === EDelegatedTaskType.DAILY_RUB ||
            rental.delegatedTask === EDelegatedTaskType.BOTH)
      ) || [];

    // Check if user is rubbing chickens they rented from others (renter perspective)
    const rentedDelegations =
      myRentalsQuery.data.rentedChickens?.filter(
        (rental) =>
          rental.status === 1 && // RENTED status
          rental.renterAddress?.toLowerCase() === address?.toLowerCase() && // User is the renter (case-insensitive)
          (rental.delegatedTask === EDelegatedTaskType.DAILY_RUB ||
            rental.delegatedTask === EDelegatedTaskType.BOTH)
      ) || [];

    // If no delegations from either perspective, return null (no delegation info to show)
    if (!ownedDelegations.length && !rentedDelegations.length) {
      return null;
    }

    // Determine user role and active delegations
    const userRole: "owner" | "renter" =
      ownedDelegations.length > 0 ? "owner" : "renter";
    const activeDelegations =
      userRole === "owner" ? ownedDelegations : rentedDelegations;

    // Since the API already handled the distribution, we just show informational data
    // The totalFeathers the user received is what they actually got after delegation
    const otherParties: IPartyDistribution[] = activeDelegations.map(
      (rental) => ({
        address:
          userRole === "owner" ? rental.renterAddress! : rental.ownerAddress,
        feathers: 0, // We don't know the exact amount the other party got
        legendaryFeathers: 0,
        role: userRole === "owner" ? "renter" : "owner",
      })
    );

    // Remove duplicates by address
    const uniqueOtherParties = otherParties.filter(
      (party, index, self) =>
        index === self.findIndex((p) => p.address === party.address)
    );

    return {
      userFeathers: totalFeathers, // User received this amount (already distributed by API)
      userLegendaryFeathers: totalLegendaryFeathers,
      userRole,
      otherParties: uniqueOtherParties,
      totalFeathers,
      totalLegendaryFeathers,
    };
  };

  return {
    isLoading: myRentalsQuery.isLoading,
    error: myRentalsQuery.error,
    getDelegationDistribution,
    refetch: myRentalsQuery.refetch,
  };
}
