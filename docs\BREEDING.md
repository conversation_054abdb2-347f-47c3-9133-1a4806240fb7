# 🐓 SabongSagaBreeding Smart Contract

---

## ⚙️ Features

- ✅ **Secure NFT Breeding** between Genesis and Legacy chickens
- ✅ **Dynamic Breeding Cooldown** with per-operation configurable times
- ✅ **Cock Token (ERC20) payment system** with vault and ninuno allocation
- ✅ **Feathers burning (ERC1155)** required for breeding
- ✅ **Resources burning (ERC1155)** required for breeding
- ✅ **Signature-based authorization** for controlled breeding operations
- ✅ **Flexible fee distribution** between vault and ninuno
- ✅ **Event emissions** for tracking and indexing off-chain
- ✅ **Batch breeding support** for multiple pairs

---

## 📜 Contract Details

| Feature                  | Description                                              |
| ------------------------ | -------------------------------------------------------- |
| **ERC721 NFTs**          | Genesis & Legacy Chickens                                |
| **ERC20 Token**          | $COCK Token (payment for breeding)                       |
| **ERC1155 Feathers**     | Feathers used and burned during breeding                 |
| **ERC1155 Resources**    | Resources used and burned during breeding                |
| **Breeding Cooldown**    | Dynamic time delay between breedings (set per operation) |
| **Fee Distribution**     | Configurable split between vault and ninuno              |
| **Signature Validation** | Ensures breeding requests are authorized                 |
| **Batch Operations**     | Support for breeding up to 10 pairs in one transaction   |

---

## 🚀 How Breeding Works

### Requirements:

- Two distinct parent chickens (Genesis or Legacy)
- Required $COCK tokens (split between vault and ninuno)
- Required ERC1155 feathers to burn
- Required ERC1155 resources to burn
- Signature authorization from backend/server

### Process:

1. Call `breed()` with correct parameters and signature
2. **Signature validation** ensures authorized breeding
3. **Parent validation** ensures distinct chickens
4. **Cooldown check and update** with operation-specific cooldown time
5. **Token transfers** (split between vault and ninuno)
6. **Feathers burning** of required ERC1155 tokens
7. **Resources burning** of required ERC1155 tokens
8. **New Legacy NFT** minted to user
9. Emit `Breed` event for tracking

---

## 💰 Breeding Payment Distribution

- **Breeding Fee** is split between:
  - **Vault**: Receives configured portion per breeding
  - **Ninuno**: Receives remaining portion per breeding

---

## ↺ Batch Query Functions

The contract provides several batch query functions for efficient data retrieval:

| Function                      | Description                                  | Returns     |
| ----------------------------- | -------------------------------------------- | ----------- |
| `isGenesisBatch()`            | Check if tokens are Genesis NFTs             | `bool[]`    |
| `isNinunoEligibleBatch()`     | Check if tokens are eligible for Ninuno      | `bool[]`    |
| `getChickenBreedTimeBatch()`  | Get last breeding time for multiple chickens | `uint256[]` |
| `getChickenBreedCountBatch()` | Get breed count for multiple chickens        | `uint256[]` |

---

## 🔄 Batch Operations

The contract supports batch breeding through `breedBatch()`:

- Process up to 10 breeding pairs in one transaction
- Each pair supports unique parameters:
  - Parent chickens
  - Fee amounts
  - Cooldown times
  - Required feathers
  - Required resources
- More gas efficient than multiple single operations

---

## 🔑 Important Mappings

| Mapping             | Purpose                                      |
| ------------------- | -------------------------------------------- |
| `chickenBreedTime`  | Tracks cooldown period per chicken           |
| `chickenBreedCount` | Tracks number of times each chicken has bred |
| `usedNonces`        | Prevents signature replay attacks            |

---

## 📡 Events

### Breed Event:

```solidity
event Breed(
    uint256 indexed chickenLeftTokenId,
    uint256 indexed chickenRightTokenId,
    uint256 indexed newTokenId,
    uint256 amountToNinuno,
    uint256[][] feathersData, // [[tokenId, amount], ...]
    uint256[][] resourcesData, // [[index, tokenId, amount], ...],
    uint256 breedingCooldownTime
);
```

---

## 🔐 Signature Verification

- Backend/server signs breeding parameters to authorize operations
- On-chain verification ensures authenticity
- Includes nonce tracking to prevent replay attacks
- Signature includes feathers and resources data hashes

---

## ⚠️ Errors & Safeguards

| Error                        | Triggered When                                   |
| ---------------------------- | ------------------------------------------------ |
| `BreedTime`                  | Chicken is still on cooldown                     |
| `InvalidArrayLength`         | Array lengths don't match in batch operations    |
| `ERC1155InsufficientBalance` | User doesn't have enough feathers or resources   |
| `ERC20InsufficientBalance`   | User doesn't have enough $COCK tokens            |
| `InsufficientNinunoBalance`  | Ninuno balance is insufficient for the operation |
| `NonceAlreadyUsed`           | Signature nonce has already been used            |
| `InvalidParents`             | Same chicken used as both parents                |
| `InvalidSignature`           | Signature verification fails                     |
| `UnauthorizedOwner`          | Caller doesn't own one or both parent chickens   |

---

## ⚖️ Admin Functions

| Function                | Description                |
| ----------------------- | -------------------------- |
| `updateSigner(address)` | Update signature validator |

---

## 💡 Example Usage

### Single Breeding:

```solidity
sabongSagaBreeding.breed(
    101,              // chickenLeftTokenId
    202,              // chickenRightTokenId
    1000 ether,       // totalAmount
    800 ether,        // amountToVault
    200 ether,        // amountToNinuno
    3600,             // breedingCooldownTime
    [[1, 5], [2, 10]], // feathersData [[tokenId, amount], ...]
    [[0, 1, 5], [1, 2, 10]], // resourcesData [[index, tokenId, amount], ...]
    signature         // From backend
);
```

### Batch Breeding:

```solidity
sabongSagaBreeding.breedBatch({
    chickenLeftTokenIds: [101, 102],
    chickenRightTokenIds: [201, 202],
    totalAmounts: [1000 ether, 1000 ether],
    amountsToVault: [800 ether, 800 ether],
    amountsToNinuno: [200 ether, 200 ether],
    breedingCooldownTimes: [3600, 3600],
    feathersData: [[[1, 5], [2, 10]], [[1, 5], [2, 10]]], // [[[tokenId, amount], ...], ...]
    resourcesData: [[[0, 1, 5], [1, 2, 10]], [[2, 1, 5]]], // [[[index, tokenId, amount], ...], ...]
    signatures: [sig1, sig2]
});
```
