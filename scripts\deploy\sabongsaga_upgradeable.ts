import { SabongSagaChickens__factory } from "../../typechain-types";
import { HardhatRuntimeEnvironment } from "hardhat/types";
import TransparentUpgradeableProxy from "hardhat-deploy/extendedArtifacts/TransparentUpgradeableProxy.json";

const erc721Interface = SabongSagaChickens__factory.createInterface();

const deploy = async ({
  getNamedAccounts,
  deployments,
  network,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  const proxyAdmin = await deployments.get("SabongSagaChickensProxyAdmin");
  const logicContract = await deployments.get("SabongSagaChickensLogic");

  const data = erc721Interface.encodeFunctionData("initialize", [
    "Sabong Saga Chickens",
    "SSC",
    "https://chicken-api-ivory.vercel.app/api/",
  ]);

  await deploy("SabongSagaChickensProxy", {
    contract: TransparentUpgradeableProxy,
    from: deployer,
    log: true,
    args: [logicContract.address, proxyAdmin.address, data],
  });
};

deploy.tags = ["SabongSagaChickensProxy"];
deploy.dependencies = [
  "VerifyContracts",
  "SabongSagaChickensProxyAdmin",
  "SabongSagaChickensLogic",
];

export default deploy;
