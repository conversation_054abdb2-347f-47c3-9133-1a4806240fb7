"use client";

import { useState } from "react";
import { But<PERSON> } from "ui";
import { RefreshCw, TrendingUp, Clock, Users } from "lucide-react";
import { useRentals } from "../../hooks/useRentals";
import { IRentalWithMetadata } from "../../types/delegation.types";
import { RentalGrid } from "./rental-grid";
import { RentChickenDialog } from "./rent-chicken-dialog";
import { useStateContext } from "@/providers/app/state";

export function RentalMarketplace() {
  const { address } = useStateContext();
  const [selectedRental, setSelectedRental] =
    useState<IRentalWithMetadata | null>(null);
  const [showRentDialog, setShowRentDialog] = useState(false);

  const {
    rentals,
    meta,
    isLoading,
    isRenting,
    error,
    refetch,
    rentChicken,
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    goToNextPage,
    goToPreviousPage,
    goToPage,
  } = useRentals();

  const handleRentChicken = (rental: IRentalWithMetadata) => {
    setSelectedRental(rental);
    setShowRentDialog(true);
  };

  const handleConfirmRent = async (rental: IRentalWithMetadata) => {
    const success = await rentChicken(rental);
    if (success) {
      setShowRentDialog(false);
      setSelectedRental(null);
    }
  };

  // Calculate marketplace stats
  const stats = {
    totalRentals: meta?.total || 0,
    averageDailyRate:
      rentals.length > 0
        ? (
            rentals.reduce((sum, r) => {
              const totalPrice = parseFloat(r.roninPrice) / 1e18;
              const durationInDays = r.rentalPeriod / 86400;
              const dailyRate =
                durationInDays > 0 ? totalPrice / durationInDays : totalPrice;
              return sum + dailyRate;
            }, 0) / rentals.length
          ).toFixed(4)
        : "0.0000",
    averageDuration:
      rentals.length > 0
        ? Math.round(
            rentals.reduce((sum, r) => sum + r.rentalPeriod, 0) /
              rentals.length /
              86400
          )
        : 0,
    activeRentals: rentals.filter((r) => r.status === 0).length,
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">
          Failed to load rental marketplace
        </div>
        <Button onPress={() => refetch()} appearance="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Marketplace Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-stone-800 border border-stone-700 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-5 h-5 text-green-400" />
            <span className="text-gray-400 text-sm">Total Listings</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {stats.totalRentals}
          </div>
        </div>

        <div className="bg-stone-800 border border-stone-700 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-5 h-5 text-yellow-400" />
            <span className="text-gray-400 text-sm">Avg Daily Rate</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {stats.averageDailyRate} RON/day
          </div>
        </div>

        <div className="bg-stone-800 border border-stone-700 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-5 h-5 text-blue-400" />
            <span className="text-gray-400 text-sm">Avg Duration</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {stats.averageDuration} days
          </div>
        </div>

        <div className="bg-stone-800 border border-stone-700 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-5 h-5 text-purple-400" />
            <span className="text-gray-400 text-sm">Available Now</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {stats.activeRentals}
          </div>
        </div>
      </div>

      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-white">
            Available Rentals
          </h2>
          <p className="text-gray-400 text-sm">
            Discover chickens available for rent from other players
          </p>
        </div>
        <Button
          onPress={() => refetch()}
          appearance="outline"
          isDisabled={isLoading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      {/* Rental Grid */}
      <RentalGrid
        rentals={rentals}
        loading={isLoading}
        onRent={handleRentChicken}
        currentUserAddress={address}
      />

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 mt-8">
          <Button
            size="small"
            appearance="outline"
            onPress={goToPreviousPage}
            isDisabled={!hasPreviousPage || isLoading}
          >
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  size="small"
                  appearance={currentPage === page ? "solid" : "outline"}
                  onPress={() => goToPage(page)}
                  isDisabled={isLoading}
                >
                  {page}
                </Button>
              );
            })}
          </div>

          <Button
            size="small"
            appearance="outline"
            onPress={goToNextPage}
            isDisabled={!hasNextPage || isLoading}
          >
            Next
          </Button>
        </div>
      )}

      {/* Rent Chicken Dialog */}
      <RentChickenDialog
        isOpen={showRentDialog}
        onOpenChange={setShowRentDialog}
        rental={selectedRental}
        onConfirmRent={handleConfirmRent}
        loading={isRenting}
      />
    </div>
  );
}
