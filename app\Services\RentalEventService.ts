import SABONG_CONFIG from 'Config/sabong'
import { createPublicClient, http } from 'viem'
import { ronin, saigon } from 'viem/chains'
import Rental, { RentalStatus } from 'App/Models/Rental'
import RentalEvent, { RentalEventProcessedStatus } from 'App/Models/RentalEvent'
import { DateTime } from 'luxon'
import Bull from '@ioc:Rocketseat/Bull'
import ProcessRentalEventJob from 'App/Jobs/ProcessRentalEventJob'

export type ChickenRentedEventType = {
  address: `0x${string}`
  blockHash: `0x${string}`
  blockNumber: bigint
  data: `0x${string}`
  logIndex: number
  transactionHash: `0x${string}`
  transactionIndex: number
  removed: boolean
} & {
  args: {
    rentId?: bigint | undefined
    renter?: `0x${string}` | undefined
    price?: bigint | undefined
    fee?: bigint | undefined
  }
  eventName: string
}

export default class RentalEventService {
  public static async watchRentalEvents() {
    const client = createPublicClient({
      chain: SABONG_CONFIG.ENV === 'production' ? ronin : saigon,
      transport: http(SABONG_CONFIG.RONIN_RPC),
    })

    const getUnProcessedEvents = await RentalEvent.query()
      .where('processed', RentalEventProcessedStatus.PENDING)
      .orderBy('blockNumber', 'asc')
      .first()

    const getLastProcessedEvents = await RentalEvent.query()
      .where('processed', RentalEventProcessedStatus.PROCESSED)
      .orderBy('blockNumber', 'desc')
      .first()

    // Fetch past events
    const latestBlock = await client.getBlockNumber()

    /**
     * Determines the starting block number for event fetching:
     * - If there are unprocessed events, starts from the earliest unprocessed event's block
     * - If there are only processed events, starts from the latest processed event's block
     * - If no events exist, starts from the contract creation block
     * @returns {bigint} The block number to start fetching events from
     */
    const fromBlock = getUnProcessedEvents
      ? getUnProcessedEvents.blockNumber
      : getLastProcessedEvents
      ? getLastProcessedEvents.blockNumber + 1n //skip last success event
      : SABONG_CONFIG.RENTAL_CREATION_BLOCKNUMBER // Creation block

    const toBlock = latestBlock // Get up to the latest block

    console.log(`Fetching past rental events from block ${fromBlock} to ${toBlock}...`)

    const pastEvents = await client.getContractEvents({
      address: SABONG_CONFIG.CONTRACTS.RENTAL_ADDRESS,
      abi: SABONG_CONFIG.ABIS.RENTAL_ABI,
      fromBlock,
      toBlock,
    })

    await processRentalEvent(pastEvents.filter((event) => event.eventName === 'ChickenRented'))

    // Watch new events from the next block
    console.log(`Watching new rental events from block ${toBlock + 1n}...`)

    const unwatch = client.watchContractEvent({
      address: SABONG_CONFIG.CONTRACTS.RENTAL_ADDRESS,
      abi: SABONG_CONFIG.ABIS.RENTAL_ABI,
      fromBlock: toBlock + 1n,
      onLogs: async (logs) => {
        try {
          processRentalEvent(
            logs.filter((event) => event.eventName === 'ChickenRented') as ChickenRentedEventType[]
          )
        } catch (error) {
          console.log(`Can't process new rental event: ${error}`)
        }
      },
      onError: (error) => {
        console.log(`Can't watch rental event: ${error}`)
      },
    })

    return unwatch
  }

  public static async confirmRental(rentalId: number, renterAddress: string, signature: string) {
    // Find the rental
    const rental = await Rental.find(rentalId)
    if (!rental) {
      throw new Error('Rental not found')
    }

    // Check if rental is available
    if (rental.status !== RentalStatus.AVAILABLE) {
      throw new Error('Rental is not available')
    }

    // Update rental status
    rental.renterAddress = renterAddress
    rental.status = RentalStatus.RENTED
    rental.rentedAt = DateTime.now()
    rental.expiresAt = DateTime.now().plus({ seconds: rental.rentalPeriod })
    rental.signature = signature
    await rental.save()

    return rental
  }
}

export const processRentalEvent = async (rentalEvents: ChickenRentedEventType[]) => {
  for (const rentalEvent of rentalEvents) {
    await Bull.add(
      new ProcessRentalEventJob().key,
      JSON.parse(
        JSON.stringify(rentalEvent, (_, value) => {
          if (typeof value === 'bigint') {
            return value.toString()
          } else {
            return value
          }
        })
      ),
      {
        attempts: 1,
      }
    )
  }
}
