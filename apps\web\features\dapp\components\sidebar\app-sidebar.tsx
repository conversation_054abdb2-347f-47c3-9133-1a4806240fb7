"use client";

import { Sidebar } from "@/components/ui";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import { ChickenIcon } from "../icon/chicken.icon";
import { CraftIcon } from "../icon/craft.icon";
import { HomeIcon } from "../icon/home.icon";
import { Star } from "../icon/star.icon";
import { TokenZap } from "../icon/token-zap.icon";
import { SidebarItem } from "./sidebar-item";

export default function AppSidebar(
  props: React.ComponentProps<typeof Sidebar>
) {
  const pathname = usePathname();

  return (
    <Sidebar {...props}>
      <Sidebar.Content className="bg-dapp-sidebar">
        <Sidebar.Section className="p-4">
          <Link className="" href="/dapp/">
            <img src="/images/sabong-saga-logo.png" alt="" />
          </Link>
        </Sidebar.Section>

        <Sidebar.Section className="mt-6 grid gap-4">
          <SidebarItem href="/dapp" isCurrent={pathname === "/dapp"}>
            <HomeIcon className="size-4" />
            <Sidebar.Label>Home</Sidebar.Label>
          </SidebarItem>

          <SidebarItem
            href="/dapp/activities"
            isCurrent={pathname === "/dapp/activities"}
          >
            <TokenZap className="size-4" />
            <Sidebar.Label>Activities</Sidebar.Label>
          </SidebarItem>

          <SidebarItem
            href="/dapp/collection"
            isCurrent={pathname === "/dapp/collection"}
          >
            <Star className="size-4" />
            <Sidebar.Label>Collection</Sidebar.Label>
          </SidebarItem>

          <SidebarItem
            href="/dapp/crafting"
            isCurrent={pathname === "/dapp/crafting"}
          >
            <CraftIcon className="size-4" />
            <Sidebar.Label>Crafting</Sidebar.Label>
          </SidebarItem>

          <SidebarItem
            href="/dapp/breeding"
            isCurrent={pathname === "/dapp/breeding"}
          >
            <ChickenIcon className="size-4" />
            <Sidebar.Label>Breeding</Sidebar.Label>
          </SidebarItem>
        </Sidebar.Section>
      </Sidebar.Content>
    </Sidebar>
  );
}
