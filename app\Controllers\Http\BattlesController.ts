import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { initializeContracts } from 'App/Helper/contracts'
import Rental, { RentalStatus } from 'App/Models/Rental'
import SABONG_CONFIG from 'Config/sabong'
import { recoverMessageAddress } from 'viem'

export default class BattlesController {
  public async request({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { chickenTokenId } = request.body()

    const { chickenLegacyContract, chickenGenesisContract } = initializeContracts()

    const chickenOwner =
      Number(chickenTokenId) > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
        ? await chickenLegacyContract.read.ownerOf([BigInt(chickenTokenId)])
        : await chickenGenesisContract.read.ownerOf([BigInt(chickenTokenId)])

    if (chickenOwner.toLowerCase() !== auth.user.blockchainAddress.toLowerCase()) {
      const rental = await Rental.query()
        .where('chickenTokenId', chickenTokenId)
        .where('status', RentalStatus.RENTED)
        .whereRaw('expires_at > NOW()')
        .first()

      if (!rental) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Chicken is not owned by the user and is not rented',
        })
      }
    }

    const nonce = Math.floor(Date.now() / 1000)

    const { user } = auth
    user.nonce = nonce
    await user.save()

    const genesisOrLegacy =
      chickenTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD ? 'Genesis' : 'Legacy'

    const message = `I am prepared to fight with ${genesisOrLegacy}#${chickenTokenId} [${nonce}]`

    return response.json({
      status: 1,
      message: 'Waiting for verification...',
      data: message,
    })
  }

  public async verify({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { chickenTokenId, signature } = request.body()

    const { user } = auth

    const genesisOrLegacy =
      chickenTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD ? 'Genesis' : 'Legacy'

    const message = `I am prepared to fight with ${genesisOrLegacy}#${chickenTokenId} [${user.nonce}]`

    const sigAddress = await recoverMessageAddress({
      message: message,
      signature,
    })

    if (sigAddress.toLowerCase() === user.blockchainAddress.toLowerCase()) {
      return response.json({
        status: 1,
        message: 'Ownership has been verified',
        data: {
          chickenTokenId,
          nonce: user.nonce,
          signature,
        },
      })
    }

    response.status(401)
    return response.json({
      status: 0,
      message: 'Invalid signature provided',
    })
  }
}
