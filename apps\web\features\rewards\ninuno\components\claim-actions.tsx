"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON>, Modal } from "ui";
import {
  EProcessed,
  IClaimHistory,
  PROCESSED_STATUS,
} from "../types/claim-request-history";
import { IClaimRequest } from "../types/ninuno.types";
import { useMe } from "../hooks/useMe";
import { useClaimBalance } from "../hooks/useClaimBalance";
import { queryClient } from "@/providers/lib/react-query";

interface IClaimActionsProps {
  claim: IClaimHistory;
}

/**
 * ClaimActions Component
 *
 * Component to handle reinitiating failed claim requests.
 * MVP version with mock data and simplified functionality.
 */
export const ClaimActions: React.FC<IClaimActionsProps> = ({ claim }) => {
  const { meQuery } = useMe();
  const {
    reInitiateClaimRequestMutation,
    loadingClaimBalance,
    executeClaimBalance,
  } = useClaimBalance();
  const [claimRequest, setClaimRequest] = useState<IClaimRequest | null>(null);

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check if claim can be reinitiated
  const canReinitiate =
    claim.processed === EProcessed.PENDING &&
    new Date().getTime() - new Date(claim.updated_at).getTime() > 5 * 60 * 1000;

  // Handle reinitiate button click
  const handleReinitiateClick = () => {
    if (canReinitiate) {
      setIsConfirmDialogOpen(true);
    }
  };

  // Handle confirm reinitiate
  const handleConfirmReinitiate = async () => {
    setIsLoading(true);

    try {
      const response = await reInitiateClaimRequestMutation.mutateAsync(
        claim.id
      );
      setClaimRequest(response);

      setIsConfirmDialogOpen(false);
      setIsSuccessDialogOpen(true);
      setIsLoading(false);

      toast.success("Claim request reinitiated successfully");
    } catch (e) {
      console.error("handleConfirmReinitiate", e);
      toast.error("Failed to reinitiate claim request");
      setIsLoading(false);
    }
  };

  // Format signature for display
  const formatSignature = (signature?: string) => {
    if (!signature) return "";

    const start = signature.substring(0, 10);
    const end = signature.substring(signature.length - 10);

    return `${start}...${end}`;
  };

  const onExecuteTransaction = async () => {
    if (!claimRequest) {
      toast.error("Claim request not found");
      return;
    }

    executeClaimBalance(
      claimRequest.withdrawalRequestId,
      claimRequest.claimAmount,
      claimRequest.signature as `0x${string}`
    ).finally(() => {
      setIsSuccessDialogOpen(false);

      // Update claimable balance
      meQuery.refetch();
      queryClient.invalidateQueries({
        queryKey: ["claimRequestHistory"],
      });
    });
  };

  // If claim cannot be reinitiated, don't render any actions
  if (!canReinitiate) {
    return <span className="text-muted-fg text-sm">No actions available</span>;
  }

  return (
    <>
      <Button
        intent="secondary"
        size="small"
        isDisabled={isLoading}
        onPress={handleReinitiateClick}
      >
        Reinitiate
      </Button>

      {/* Confirm Dialog */}
      <Modal isOpen={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Reinitiate Claim Request</Modal.Title>
            <Modal.Description>
              You are about to reinitiate a failed claim request
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <div className="py-4">
              <div className="rounded-lg bg-secondary p-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-fg">Claim ID:</span>
                  <span className="font-medium font-mono text-xs">
                    {claim.id}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-muted-fg">Amount:</span>
                  <span className="font-medium text-primary">
                    {(parseFloat(claim.amount) / 10 ** 18).toFixed(2)} $COCK
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-muted-fg">Status:</span>
                  <span className="font-medium text-yellow-500">
                    {
                      PROCESSED_STATUS[
                        claim.processed as keyof typeof PROCESSED_STATUS
                      ]
                    }
                  </span>
                </div>
              </div>

              <p className="mt-4 text-sm text-muted-fg">
                Reinitiating this claim will generate a new signature for the
                blockchain transaction. You will need to complete the
                transaction to receive your $COCK tokens.
              </p>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              intent="secondary"
              onPress={() => setIsConfirmDialogOpen(false)}
              isDisabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              intent="primary"
              onPress={handleConfirmReinitiate}
              isDisabled={isLoading}
            >
              {isLoading ? "Processing..." : "Reinitiate Claim"}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>

      {/* Success Dialog */}
      <Modal isOpen={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Claim Request Reinitiated</Modal.Title>
            <Modal.Description>
              Your claim request has been successfully reinitiated
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <div className="py-4">
              <div className="rounded-lg bg-secondary p-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-fg">Amount:</span>
                  <span className="font-medium text-primary">
                    {(
                      parseFloat(claimRequest?.claimAmount || "0") /
                      10 ** 18
                    ).toFixed(2)}{" "}
                    $COCK
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-muted-fg">Withdrawal Request ID:</span>
                  <span className="font-medium text-yellow-500">
                    {claimRequest?.withdrawalRequestId}
                  </span>
                </div>

                {claimRequest?.signature && (
                  <div className="flex justify-between items-center">
                    <span className="text-muted-fg">Signature:</span>
                    <span className="font-medium font-mono text-xs">
                      {formatSignature(claimRequest.signature)}
                    </span>
                  </div>
                )}
              </div>

              <div className="mt-4 text-sm text-muted-fg">
                <p>
                  Your claim request has been reinitiated. You can now proceed
                  with the blockchain transaction to complete the claim process.
                  Follow the instructions provided to sign the transaction.
                </p>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              intent="secondary"
              onPress={() => setIsSuccessDialogOpen(false)}
            >
              Close
            </Button>
            <Button
              intent="primary"
              onPress={onExecuteTransaction}
              isDisabled={loadingClaimBalance.value}
            >
              {loadingClaimBalance.value
                ? "Processing..."
                : "Execute Transaction"}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </>
  );
};
