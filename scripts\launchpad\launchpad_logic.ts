import { HardhatRuntimeEnvironment } from "hardhat/types";

const deploy = async ({
  getNamedAccounts,
  deployments,
  ethers,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  await deploy("SabongSagaLaunchpadLogic", {
    contract: "SabongSagaLaunchpad",
    from: deployer,
    log: true,
  });
};

deploy.tags = ["SabongSagaLaunchpadLogic"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;
