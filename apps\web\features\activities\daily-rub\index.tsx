"use client";

import { InfoIcon } from "@/components/common/icons/info.icon";
import { Button } from "@/components/ui";
import React, { FC } from "react";

export const DailyRub: FC = () => {
  return (
    <div className="mt-4 flex items-start gap-8 font-Poppins">
      <div className="p-6 flex-1 bg-[#1E1E1E] rounded-xl">
        <div className="text-2xl font-semibold flex gap-1">
          <div>Daily Rub</div>
          <InfoIcon className="size-4 text-white/60" />
        </div>
        <div className="text-white/60 mt-1">
          Earn Feathers by rubbing your chickens daily.
        </div>

        <div className="mt-8 grid gap-4 grid-cols-5">
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
          <div className="bg-[#2D2D2D] rounded-xl h-[126px] relative"></div>
        </div>

        <div className="grid place-items-center py-4 mt-8">
          <Button
            appearance="plain"
            className="bg-[#2D2D2D] hover:bg-[#2D2D2D]/60 pressed:bg-[#2D2D2D]/60"
          >
            Rub Chickens
          </Button>
        </div>
      </div>

      <div className="w-1/3 bg-[#1E1E1E] p-6 rounded-xl">
        <div className="text-2xl font-semibold flex gap-1">
          <div>Claim Feathers</div>
          <InfoIcon className="size-4 text-white/60" />
        </div>
        <div className="text-white/60 mt-1">
          Claim your accumulating Feathers.
        </div>

        <div className="grid place-items-center py-12">
          <div className="text-5xl font-semibold">1000</div>
        </div>

        <div className="grid place-items-center pb-4">
          <Button
            appearance="plain"
            className="bg-[#2D2D2D] hover:bg-[#2D2D2D]/60 pressed:bg-[#2D2D2D]/60"
          >
            Claim Feathers
          </Button>
        </div>
      </div>
    </div>
  );
};
