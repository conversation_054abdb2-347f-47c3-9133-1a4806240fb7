// app/leaderboard/page.tsx
import CardsLoaders from "@/components/shared/cards-loaders";
import AppNavbar from "@/components/shared/navbar";
import AppInitializer from "@/providers/app/initializer";
import dynamic from "next/dynamic";

const DynamicLeaderboard = dynamic(
  () => import("@/components/pages/leaderboard"),
  {
    ssr: true,
    loading: () => <CardsLoaders />,
  }
);

export default async function LeaderboardPage() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        <div className="relative z-10">
          <AppNavbar />
        </div>

        <div className="container mx-auto px-4 py-8">
          <h1 className="text-3xl font-bold text-center font-Arcadia text-primary mt-1 mb-8">
            PreAlpha Battle Leaderboard
          </h1>
          <DynamicLeaderboard />
        </div>
      </div>
    </AppInitializer>
  );
}
