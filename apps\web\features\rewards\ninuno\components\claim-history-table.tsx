"use client";

import React from "react";
import { <PERSON><PERSON>, Card, Select, Table } from "ui";
import { useClaimBalance } from "../hooks/useClaimBalance";
import { EProcessed, PROCESSED_STATUS } from "../types/claim-request-history";
import { ClaimActions } from "./claim-actions";

interface IClaimHistoryTableProps {
  className?: string;
}

/**
 * ClaimHistoryTable Component
 *
 * Displays the history of claim requests with filtering and pagination.
 * MVP version with mock data and simplified functionality.
 */
export const ClaimHistoryTable: React.FC<IClaimHistoryTableProps> = ({
  className,
}) => {
  const { claimRequestHistoryQuery, page, statusFilter } = useClaimBalance();

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get status badge class based on status
  const getStatusBadgeClass = (status: EProcessed) => {
    switch (status) {
      case EProcessed.COMPLETED:
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case EProcessed.PENDING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  // Render loading state
  if (claimRequestHistoryQuery.isPending) {
    return (
      <Card className={className}>
        <Card.Header>
          <Card.Title>Claim History</Card.Title>
          <Card.Description>Loading your claim history...</Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="h-12 bg-gray-200 dark:bg-gray-700 rounded"
              ></div>
            ))}
          </div>
        </Card.Content>
      </Card>
    );
  }

  // Render claim history table
  return (
    <Card className={className}>
      <Card.Header>
        <Card.Title>Claim History</Card.Title>
        <Card.Description>Your history of $COCK reward claims</Card.Description>
      </Card.Header>
      <Card.Content>
        {/* Filters */}
        <div className="mb-4">
          <div className="flex mx-4 mt-4 flex-col sm:flex-row gap-2 sm:items-end">
            <div className="w-full sm:w-48">
              <Select
                label="Status Filter"
                selectedKey={statusFilter.value}
                onSelectionChange={(key) => {
                  statusFilter.set(key as EProcessed);
                  page.set(1);
                }}
              >
                <Select.Trigger>
                  <span>{PROCESSED_STATUS[statusFilter.value]}</span>
                </Select.Trigger>
                <Select.List>
                  <Select.Option id="ALL">All Statuses</Select.Option>
                  <Select.Option id={EProcessed.PENDING}>Pending</Select.Option>
                  <Select.Option id={EProcessed.COMPLETED}>
                    Completed
                  </Select.Option>
                </Select.List>
              </Select>
            </div>

            <Button
              intent="secondary"
              size="small"
              className="sm:mb-1"
              onPress={() => {
                statusFilter.set("ALL");
                page.set(1);
              }}
            >
              Reset Filters
            </Button>
          </div>
        </div>

        {/* Empty state */}
        {claimRequestHistoryQuery.isSuccess &&
          claimRequestHistoryQuery.data.data.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-fg">No claim history found.</p>
              <p className="text-sm text-muted-fg mt-2">
                When you claim your $COCK rewards, the transactions will appear
                here.
              </p>
            </div>
          )}

        {/* Table */}
        {claimRequestHistoryQuery.isSuccess &&
          claimRequestHistoryQuery.data.data.length > 0 && (
            <>
              <Table aria-label="Claim history table">
                <Table.Header>
                  <Table.Column isRowHeader={true}>Date</Table.Column>
                  <Table.Column>Amount</Table.Column>
                  <Table.Column>Status</Table.Column>
                  <Table.Column>Actions</Table.Column>
                </Table.Header>
                <Table.Body>
                  {claimRequestHistoryQuery.isSuccess &&
                    claimRequestHistoryQuery.data.data.map((claim) => (
                      <Table.Row key={claim.id}>
                        <Table.Cell>{formatDate(claim.updated_at)}</Table.Cell>
                        <Table.Cell>
                          <span className="font-medium text-primary">
                            {(parseFloat(claim.amount) / 10 ** 18).toFixed(2)}{" "}
                            $COCK
                          </span>
                        </Table.Cell>
                        <Table.Cell>
                          <span
                            className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(claim.processed)}`}
                          >
                            {
                              PROCESSED_STATUS[
                                `${claim.processed}` as keyof typeof PROCESSED_STATUS
                              ]
                            }
                          </span>
                        </Table.Cell>
                        <Table.Cell>
                          <ClaimActions claim={claim} />
                        </Table.Cell>
                      </Table.Row>
                    ))}
                </Table.Body>
              </Table>

              {/* Pagination */}
              {claimRequestHistoryQuery.isSuccess && (
                <div className="flex items-center justify-center gap-4 my-4">
                  <Button
                    intent="secondary"
                    size="small"
                    isDisabled={page.value <= 1}
                    onPress={() => page.set(page.value - 1)}
                  >
                    Previous
                  </Button>

                  <span className="text-sm text-muted-fg">
                    Page {page.value} of{" "}
                    {claimRequestHistoryQuery.data.meta.last_page || 1}
                  </span>

                  <Button
                    intent="secondary"
                    size="small"
                    isDisabled={
                      page.value >= claimRequestHistoryQuery.data.meta.last_page
                    }
                    onPress={() => page.set(page.value + 1)}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
      </Card.Content>
    </Card>
  );
};
