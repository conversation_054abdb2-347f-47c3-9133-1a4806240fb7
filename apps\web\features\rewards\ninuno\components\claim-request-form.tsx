"use client";

import { queryClient } from "@/providers/lib/react-query";
import React, { useState } from "react";
import { toast } from "sonner";
import { <PERSON>ton, Card, Form, Modal, TextField } from "ui";
import { useClaimBalance } from "../hooks/useClaimBalance";
import { useMe } from "../hooks/useMe";
import { IClaimRequest } from "../types/ninuno.types";

interface IClaimRequestFormProps {
  className?: string;
}

/**
 * ClaimRequestForm Component
 *
 * A form or modal for initiating a new claim request.
 * MVP version with mock data and simplified functionality.
 */
export const ClaimRequestForm: React.FC<IClaimRequestFormProps> = ({
  className,
}) => {
  const { meQuery } = useMe();
  const {
    initiateClaimRequestMutation,
    loadingClaimBalance,
    executeClaimBalance,
  } = useClaimBalance();
  const claimableBal = meQuery.isSuccess
    ? parseFloat(meQuery.data?.claimable_balance as string) / 10 ** 18
    : 0;

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [amount, setAmount] = useState("");
  const [amountError, setAmountError] = useState("");
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [claimRequest, setClaimRequest] = useState<IClaimRequest | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Validate amount
  const validateAmount = (value: string) => {
    const numValue = parseFloat(value);

    if (!value.trim()) {
      setAmountError("Amount is required");
      return false;
    }

    if (isNaN(numValue)) {
      setAmountError("Amount must be a valid number");
      return false;
    }

    if (numValue <= 0) {
      setAmountError("Amount must be greater than zero");
      return false;
    }

    if (numValue > claimableBal) {
      setAmountError("Amount cannot exceed your claimable balance");
      return false;
    }

    setAmountError("");
    return true;
  };

  // Handle amount change
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // amount should not exceed claimable
    if (parseFloat(value) > claimableBal) {
      return;
    }

    // amount should not have more than 2 decimal places
    if (value.includes(".")) {
      const decimalPart = value.split(".")[1];
      if (decimalPart && decimalPart.length > 2) {
        return;
      }
    }

    setAmount(value);

    if (value) {
      validateAmount(value);
    } else {
      setAmountError("");
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateAmount(amount)) {
      return;
    }

    setIsLoading(true);
    const numAmount = parseFloat(amount);

    try {
      const response =
        await initiateClaimRequestMutation.mutateAsync(numAmount);

      setClaimRequest(response);
      setIsDialogOpen(false);
      setSuccessDialogOpen(true);
      setAmount("");
      setIsLoading(false);

      // Update claimable balance
      meQuery.refetch();
      queryClient.invalidateQueries({ queryKey: ["claimRequestHistory"] });

      // Show success toast
      toast.success("Claim request initiated successfully");
    } catch (error: any) {
      setIsLoading(false);
      toast.error(
        error.response?.data?.message ||
          "Failed to initiate claim request. Please try again later."
      );
    }
  };

  // Handle dialog open
  const handleOpenDialog = () => {
    setAmount("");
    setAmountError("");
    setIsDialogOpen(true);
  };

  // Format signature for display
  const formatSignature = (signature?: string) => {
    if (!signature) return "";

    const start = signature.substring(0, 10);
    const end = signature.substring(signature.length - 10);

    return `${start}...${end}`;
  };

  const onExecuteTransaction = async () => {
    if (!claimRequest) {
      toast.error("Claim request not found");
      return;
    }

    executeClaimBalance(
      claimRequest.withdrawalRequestId,
      claimRequest.claimAmount,
      claimRequest.signature as `0x${string}`
    ).finally(() => {
      setSuccessDialogOpen(false);

      // Update claimable balance
      meQuery.refetch();
      queryClient.invalidateQueries({
        queryKey: ["claimRequestHistory"],
      });
    });
  };

  return (
    <>
      <Card className={className}>
        <Card.Header>
          <Card.Title>Claim $COCK Rewards</Card.Title>
          <Card.Description>
            Claim your accumulated $COCK rewards as tokens
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div className="rounded-lg bg-secondary p-4">
              <div className="flex justify-between">
                <span className="text-muted-fg">Claimable Balance:</span>
                <span className="font-medium text-primary">
                  {claimableBal.toFixed(2)} $COCK
                </span>
              </div>
            </div>

            <Button
              intent="primary"
              className="w-full"
              isDisabled={claimableBal <= 0}
              onPress={handleOpenDialog}
            >
              Claim Rewards
            </Button>

            <p className="text-xs text-muted-fg text-center">
              Claiming rewards will initiate a blockchain transaction to
              transfer $COCK tokens to your wallet.
            </p>
          </div>
        </Card.Content>
      </Card>

      {/* Claim Dialog */}
      <Modal isOpen={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Claim $COCK Rewards</Modal.Title>
            <Modal.Description>
              Enter the amount of $COCK rewards you want to claim
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <Form onSubmit={handleSubmit} className="py-4">
              <div className="relative">
                <TextField
                  label="Amount to Claim"
                  type="number"
                  value={amount}
                  onChange={(value) =>
                    handleAmountChange({
                      target: { value },
                    } as React.ChangeEvent<HTMLInputElement>)
                  }
                  placeholder="Enter amount"
                  errorMessage={amountError}
                  description={`Available: ${claimableBal.toFixed(2)} $COCK`}
                  autoFocus
                />
                <Button
                  className="absolute right-0 top-0 mt-7 mr-8 text-xs h-8 px-2"
                  intent="secondary"
                  appearance="plain"
                  onPress={() => {
                    // Define a small threshold below which we consider the balance to be effectively zero
                    const EPSILON = 0.01;

                    // If balance is below threshold, treat as zero, otherwise format properly
                    const maxValue =
                      claimableBal < EPSILON ? "0" : claimableBal.toFixed(2);

                    setAmount(maxValue);
                    validateAmount(maxValue);
                  }}
                >
                  Max
                </Button>
              </div>

              <div className="mt-4 text-sm text-muted-fg">
                <p>
                  Claiming rewards will initiate a blockchain transaction to
                  transfer $COCK tokens to your wallet. Gas fees may apply.
                </p>
              </div>

              <Modal.Footer className="mt-4">
                <Button
                  intent="secondary"
                  onPress={() => setIsDialogOpen(false)}
                  isDisabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  intent="primary"
                  type="submit"
                  isDisabled={isLoading || !amount || !!amountError}
                >
                  {isLoading ? "Processing..." : "Claim Rewards"}
                </Button>
              </Modal.Footer>
            </Form>
          </Modal.Body>
        </Modal.Content>
      </Modal>

      {/* Success Dialog */}
      <Modal isOpen={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Claim Request Initiated</Modal.Title>
            <Modal.Description>
              Your claim request has been successfully initiated.
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <div className="py-4">
              <div className="rounded-lg bg-secondary p-4 space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-fg">Amount:</span>
                  <span className="font-medium text-primary">
                    {(
                      parseFloat(claimRequest?.claimAmount || "0") /
                      10 ** 18
                    ).toFixed(2)}{" "}
                    $COCK
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-muted-fg">Withdrawal Request ID:</span>
                  <span className="font-medium text-yellow-500">
                    {claimRequest?.withdrawalRequestId}
                  </span>
                </div>

                {claimRequest?.signature && (
                  <div className="flex justify-between items-center">
                    <span className="text-muted-fg">Signature:</span>
                    <span className="font-medium font-mono text-xs">
                      {formatSignature(claimRequest.signature)}
                    </span>
                  </div>
                )}
              </div>

              <div className="mt-4 text-sm text-muted-fg">
                <p>
                  Your claim request has been initiated. You can now proceed
                  with the blockchain transaction to complete the claim process.
                  Follow the instructions provided to sign the transaction.
                </p>
              </div>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              intent="secondary"
              onPress={() => setSuccessDialogOpen(false)}
            >
              Close
            </Button>
            <Button
              intent="primary"
              onPress={onExecuteTransaction}
              isDisabled={loadingClaimBalance.value}
            >
              {loadingClaimBalance.value
                ? "Processing..."
                : "Execute Transaction"}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </>
  );
};
