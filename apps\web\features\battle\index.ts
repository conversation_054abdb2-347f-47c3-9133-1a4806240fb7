// Main exports for the Battle API feature

// API functions
export {
  requestBattle,
  verifyBattle,
  completeBattleFlow,
  canChickenBattle,
  formatBattleDataForGame,
  generateGameUrl,
} from "./api/battle-api";

// React hooks
export { useBattleApi, useBattleApiCalls } from "./hooks/useBattleApi";

export {
  useBattleStats,
  useBulkBattleStats,
  fetchChickenBattleStats,
} from "./hooks/useBattleStats";

// Components
// EnhancedBattleModal removed - using original BattleModal with integrated API

// Types
export type {
  IBattleRequestPayload,
  IBattleRequestResponse,
  IBattleVerifyPayload,
  IBattleVerifyResponse,
  IBattleStats,
  BattleFlowState,
  IGameServerConfig,
  IMatchmakingState,
  IBattleHistoryEntry,
  IBattleError,
} from "./types/battle.types";
