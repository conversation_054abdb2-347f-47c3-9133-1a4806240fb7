"use client";

import { useQuery } from "@tanstack/react-query";
import { IBattleStats } from "../types/battle.types";

/**
 * Fetch battle stats for a single chicken
 */
const fetchChickenBattleStats = async (tokenId: number): Promise<IBattleStats> => {
  try {
    const response = await fetch(`/api/proxy/game?tokenId=${tokenId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch battle stats: ${response.status}`);
    }
    const data = await response.json();
    return {
      wins: data.wins || 0,
      losses: data.losses || 0,
      draws: data.draws || 0,
      level: data.level,
      state: data.state || "normal",
      recoverDate: data.recoverDate,
      stats: data.stats,
    };
  } catch (error) {
    console.warn(`Failed to fetch battle stats for chicken ${tokenId}:`, error);
    return {
      wins: 0,
      losses: 0,
      draws: 0,
      state: "normal",
    };
  }
};

/**
 * Hook to fetch battle stats for a single chicken
 */
export const useBattleStats = (tokenId: number | null) => {
  return useQuery({
    queryKey: ["battleStats", tokenId],
    queryFn: () => fetchChickenBattleStats(tokenId!),
    enabled: !!tokenId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook to fetch battle stats for multiple chickens
 */
export const useBulkBattleStats = (tokenIds: number[]) => {
  return useQuery({
    queryKey: ["bulkBattleStats", tokenIds],
    queryFn: async () => {
      if (tokenIds.length === 0) return {};

      const statsPromises = tokenIds.map(async (tokenId) => ({
        tokenId,
        stats: await fetchChickenBattleStats(tokenId),
      }));

      const results = await Promise.allSettled(statsPromises);
      const statsMap: Record<number, IBattleStats> = {};

      results.forEach((result, index) => {
        const tokenId = tokenIds[index];
        if (tokenId !== undefined) {
          if (result.status === "fulfilled") {
            statsMap[tokenId] = result.value.stats;
          } else {
            // Default stats if fetch failed
            statsMap[tokenId] = {
              wins: 0,
              losses: 0,
              draws: 0,
              state: "normal",
            };
          }
        }
      });

      return statsMap;
    },
    enabled: tokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Export the fetch function for direct use
 */
export { fetchChickenBattleStats };
