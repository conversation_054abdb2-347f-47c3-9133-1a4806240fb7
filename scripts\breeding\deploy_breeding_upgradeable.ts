import { ethers, upgrades, network } from "hardhat";
// import * as hre from "hardhat";
import fs from "fs";
import path from "path";
import chalk from "chalk";
// import { HardhatRuntimeEnvironment } from "hardhat/types";

const TREASURY_ADDRESS = "******************************************";
const SIGNER_ADDRESS = "******************************************";

import abi from "./abi.json";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with the account:", deployer.address);

  const accountBalance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", ethers.formatEther(accountBalance));

  // Get contract factory
  const SabongSagaBreeding = await ethers.getContractFactory(
    "SabongSagaBreedingUpgradeable"
  );

  // Deploy using upgrades plugin
  console.log("Deploying SabongSagaBreeding...");
  const breeding = await upgrades.deployProxy(
    SabongSagaBreeding,
    [
      abi.sabong_saga_cock_address, // cock address
      abi.sabong_saga_genesis_address, // genesis address
      abi.sabong_saga_legacy_address, // legacy address
      abi.sabong_saga_items_address, // feathers address
      abi.sabong_saga_resources_address, // resources address
      TREASURY_ADDRESS,
      SIGNER_ADDRESS,
    ],
    {
      initializer: "initialize",
      kind: "transparent",
    }
  );

  await breeding.waitForDeployment();

  const breedingAddress = await breeding.getAddress();
  console.log("SabongSagaBreeding proxy deployed to:", breedingAddress);

  // Get implementation address
  const implementationAddress = await upgrades.erc1967.getImplementationAddress(
    breedingAddress
  );
  console.log(
    "SabongSagaBreeding implementation deployed to:",
    implementationAddress
  );

  // Get admin address
  const adminAddress = await upgrades.erc1967.getAdminAddress(breedingAddress);
  console.log("ProxyAdmin deployed to:", adminAddress);

  // Save deployment info
  const deploymentInfo = {
    proxy: breedingAddress,
    implementation: implementationAddress,
    admin: adminAddress,
    network: network.name,
    timestamp: new Date().toISOString(),
  };

  // Create deployments directory if it doesn't exist
  const deploymentsDir = path.join(__dirname, "../../deployments");
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  // Save deployment info to file
  const deploymentFile = path.join(
    deploymentsDir,
    `breeding_${network.name}.json`
  );
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`Deployment info saved to ${deploymentFile}`);

  // Get the ABI from the artifacts
  const artifactPath = path.join(
    __dirname,
    "../../artifacts/contracts/upgradeable/SabongSagaBreedingUpgradeable.sol/SabongSagaBreedingUpgradeable.json"
  );
  const artifactJson = JSON.parse(fs.readFileSync(artifactPath, "utf8"));

  // Update abi.json with new breeding address and ABI
  const updatedAbi = {
    ...abi,
    sabong_saga_breeding_address: breedingAddress,
    sabong_saga_breeding: artifactJson.abi,
  };

  // Write updated ABI to file
  const abiPath = path.join(__dirname, "abi.json");
  fs.writeFileSync(abiPath, JSON.stringify(updatedAbi, null, 2), "utf8");
  console.log("\nUpdated abi.json with new breeding address and ABI");

  // Verify contracts if on a supported network -- disabled for now
  // if (network.name !== "hardhat" && network.name !== "localhost") {
  //   console.log("\nVerifying contracts...");
  //   try {
  //     // Verify implementation
  //     await hre.run("verify:verify", {
  //       address: implementationAddress,
  //       contract:
  //         "contracts/upgradeable/SabongSagaBreedingUpgradeable.sol:SabongSagaBreedingUpgradeable",
  //     });
  //     console.log("Implementation contract verified");

  //     // Verify proxy admin
  //     await hre.run("verify:verify", {
  //       address: adminAddress,
  //       contract:
  //         "@openzeppelin/contracts/proxy/transparent/ProxyAdmin.sol:ProxyAdmin",
  //     });
  //     console.log("Proxy admin contract verified");
  //   } catch (error) {
  //     console.log("Error verifying contracts:", error);
  //   }
  // }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("❌ Error:"), error);
    process.exit(1);
  });
