import { Address } from "viem";

// API endpoints
export const API_ENDPOINTS = {
  csrf: "/csrf-token",
  auth: "/api/auth/local",
  me: "/api/auth/me",
  logout: "/api/auth/logout",
} as const;

export const api = {
  async call(url: string, options?: RequestInit) {
    const response = await fetch(url, options);
    if (!response.ok)
      throw new Error(`API call failed: ${response.statusText}`);
    return response.json();
  },

  async getCsrfToken() {
    const { csrfToken } = await this.call(API_ENDPOINTS.csrf);
    return csrfToken;
  },

  async authenticate(address: Address, signature: string, issuedAt: string) {
    const csrfToken = await this.getCsrfToken();
    return this.call(API_ENDPOINTS.auth, {
      method: "POST",
      headers: {
        "X-CSRF-Token": csrfToken,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ address, signature, issuedAt }),
    });
  },

  async logout() {
    const csrfToken = await this.getCsrfToken();
    return this.call(API_ENDPOINTS.logout, {
      method: "POST",
      headers: {
        "X-CSRF-Token": csrfToken,
        "Content-Type": "application/json",
      },
    });
  },
};
