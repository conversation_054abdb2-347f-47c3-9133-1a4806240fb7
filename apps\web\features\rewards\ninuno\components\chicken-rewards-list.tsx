"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, Card, Checkbox, Table } from "ui";
import Image from "next/image";
import { IChickenInfo, IChickenSelection } from "../types/ninuno.types";
import LoadingFull from "@/components/common/loading/loading-full";

interface IChickenRewardsListProps {
  className?: string;
  chickens: IChickenInfo[];
  selectedChickens: IChickenSelection;
  toggleChickenSelection: (tokenId: string) => void;
  selectAllChickens: () => void;
  deselectAllChickens: () => void;
  paginationLimit?: number;
  isLoading?: boolean;
}

/**
 * ChickenRewardsList Component
 *
 * Displays the list of owned chickens with their accumulated rewards.
 * Allows selection of chickens for transfer.
 * MVP version with mock data and simplified functionality.
 */
export const ChickenRewardsList: React.FC<IChickenRewardsListProps> = ({
  className,
  chickens,
  selectedChickens,
  toggleChickenSelection,
  selectAllChickens,
  deselectAllChickens,
  paginationLimit = 5,
  isLoading = false,
}) => {
  // State for loading simulation and pagination
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate pagination values
  const totalPages = Math.ceil(chickens.length / paginationLimit);

  // Calculate total selected rewards
  const totalSelectedRewards = chickens
    .filter((chicken) => selectedChickens[chicken.tokenId])
    .reduce((sum, chicken) => sum + chicken.accumulatedRewards, 0);

  // Check if all chickens are selected
  const allSelected =
    chickens.length > 0 &&
    chickens.every((chicken) => selectedChickens[chicken.tokenId]);

  // Check if some chickens are selected
  const someSelected = chickens.some(
    (chicken) => selectedChickens[chicken.tokenId]
  );

  // Get paginated chickens
  const getPaginatedChickens = () => {
    // Sort chickens by accumulatedRewards in descending order
    const sortedChickens = [...chickens].sort(
      (a, b) => b.accumulatedRewards - a.accumulatedRewards
    );

    // Get paginated chickens
    const startIndex = (currentPage - 1) * paginationLimit;
    const endIndex = startIndex + paginationLimit;
    return sortedChickens.slice(startIndex, endIndex);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle select all checkbox change
  const handleSelectAllChange = () => {
    if (allSelected) {
      deselectAllChickens();
    } else {
      selectAllChickens();
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  // Render loading state
  if (isLoading) {
    return (
      <Card className={className}>
        <Card.Header>
          <Card.Title>Your Chickens</Card.Title>
          <Card.Description>
            Loading your chickens with accumulated $COCK rewards...
          </Card.Description>
        </Card.Header>
        <Card.Content>
          {/* <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="h-16 bg-gray-200 dark:bg-gray-700 rounded"
              ></div>
            ))}
          </div> */}
          <div className="grid place-items-center py-8">
            <LoadingFull
              classNameSpinner="text-primary"
              message="Loading chickens"
            />
          </div>
        </Card.Content>
      </Card>
    );
  }

  // Render empty state
  if (chickens.length === 0) {
    return (
      <Card className={className}>
        <Card.Header>
          <Card.Title>Your Chickens</Card.Title>
          <Card.Description>
            You don&apos;t have any chickens with accumulated $COCK rewards
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="text-center py-8">
            <p className="text-muted-fg">
              No chickens found with accumulated rewards.
            </p>
          </div>
        </Card.Content>
      </Card>
    );
  }

  // Render chicken list
  return (
    <Card className={className}>
      <Card.Header>
        <Card.Title>Your Chickens</Card.Title>
        <Card.Description>
          Select chickens to transfer their accumulated $COCK rewards to your
          claimable balance
        </Card.Description>
      </Card.Header>
      <Card.Content>
        <div className="mb-6 mt-2 px-4 py-3 flex items-center justify-between rounded-lg">
          <div className="flex items-center space-x-3">
            <Checkbox
              isSelected={allSelected}
              isIndeterminate={!allSelected && someSelected}
              onChange={handleSelectAllChange}
              aria-label="Select all chickens"
            />
            <span className="text-sm">
              {allSelected ? "Deselect all" : "Select all"}
            </span>
          </div>

          <div className="text-sm">
            <span className="font-medium">Total Selected: </span>
            <span className="text-primary font-bold">
              {totalSelectedRewards.toFixed(2)} $COCK
            </span>
          </div>
        </div>

        <Table aria-label="Chicken rewards table">
          <Table.Header>
            <Table.Column>Select</Table.Column>
            <Table.Column isRowHeader={true}>Chicken</Table.Column>
            <Table.Column>Accumulated Rewards</Table.Column>
            <Table.Column>Last Updated</Table.Column>
          </Table.Header>
          <Table.Body>
            {getPaginatedChickens().map((chicken) => (
              <Table.Row key={chicken.tokenId}>
                <Table.Cell>
                  <Checkbox
                    isSelected={!!selectedChickens[chicken.tokenId]}
                    onChange={() => toggleChickenSelection(chicken.tokenId)}
                    aria-label={`Select chicken #${chicken.tokenId}`}
                  />
                </Table.Cell>
                <Table.Cell>
                  <div className="flex items-center space-x-3">
                    <div className="h-10 w-10 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700">
                      {chicken.image && (
                        <Image
                          src={chicken.image}
                          alt={chicken.name || `Chicken #${chicken.tokenId}`}
                          width={40}
                          height={40}
                          className="object-cover"
                        />
                      )}
                    </div>
                    <span className="font-medium">
                      {chicken.name || `Chicken #${chicken.tokenId}`}
                    </span>
                  </div>
                </Table.Cell>
                <Table.Cell>
                  <span className="font-medium text-primary">
                    {chicken.accumulatedRewards.toFixed(2)} $COCK
                  </span>
                </Table.Cell>
                <Table.Cell>{formatDate(chicken.lastUpdated)}</Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>

        {/* Pagination */}
        {chickens.length > paginationLimit && (
          <div className="flex items-center justify-center gap-4 my-4">
            <Button
              intent="secondary"
              size="small"
              isDisabled={currentPage <= 1 || isLoading}
              onPress={() => handlePageChange(currentPage - 1)}
            >
              Previous
            </Button>

            <span className="text-sm text-muted-fg">
              Page {currentPage} of {totalPages || 1}
            </span>

            <Button
              intent="secondary"
              size="small"
              isDisabled={currentPage >= totalPages || isLoading}
              onPress={() => handlePageChange(currentPage + 1)}
            >
              Next
            </Button>
          </div>
        )}
      </Card.Content>
    </Card>
  );
};
