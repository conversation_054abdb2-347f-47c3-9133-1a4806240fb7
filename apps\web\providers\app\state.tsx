"use client";

import LoadingScreen from "@/components/common/loading/loading-screen";
import LoadingScreenBreeding from "@/components/common/loading/loading-screen-breeding";
import { api, API_ENDPOINTS } from "@/lib/api/api-helper";
import { useGlobalState } from "@/lib/store";
import { useGlobalStatePersist } from "@/lib/store/persist";
import { extractIssuedAt, prepareSiwe } from "@/services/prepare-siwe";
import { State, useHookstate } from "@hookstate/core";
import { useRouter } from "next/navigation";
import React, { createContext, useContext, useMemo } from "react";
import { Address, createWalletClient, custom } from "viem";
import {
  Connector,
  useAccount,
  useAccountEffect,
  useConnect,
  useDisconnect,
  usePublicClient,
  useSignMessage,
  useWalletClient,
} from "wagmi";
import { ronin, saigon } from "wagmi/chains";
import { CHAIN_ID, PublicClient, WalletClient } from "../web3/viem-client";
import { config } from "../web3/web3-provider";
import { toast } from "sonner";

interface IStateContext {
  loading: State<boolean>;
  loadingMessage: State<string>;
  loadingBreeding: State<boolean>;
  loadingBreedingMessage: State<string>;
  address: Address | undefined;
  Disconnect: () => void;
  isConnected: boolean;
  currentChain: typeof ronin | typeof saigon;
  publicClient: PublicClient;
  walletClient: WalletClient | undefined;
  ConnectRecentWallet: () => Promise<void>;
}

export const StateContext = createContext<IStateContext>({} as IStateContext);
export const useStateContext = (): IStateContext => useContext(StateContext);

interface IStateProviderProps {
  children: React.ReactNode;
}

export function StateProvider({ children }: IStateProviderProps) {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { signMessage } = useSignMessage();
  const { disconnect } = useDisconnect();

  const publicClient = usePublicClient();
  const walletClient = useWalletClient();

  const { connectAsync } = useConnect();
  const account = useAccount();

  // Replace the Wagmi hooks with direct Viem clients
  const viemPublicClient = publicClient;
  const viemWalletClient = useMemo(() => {
    if (!walletClient.data) return null; // Return null if no wallet client

    return createWalletClient({
      chain: CHAIN_ID === 2020 ? ronin : saigon,
      transport: custom(walletClient.data),
    });
  }, [walletClient.data]);

  const loading = useHookstate(gState.loading);
  const loadingMessage = useHookstate(gState.loadingMessage);

  const loadingBreeding = useHookstate(gStateP.breeding.loading);
  const loadingBreedingMessage = useHookstate(gStateP.breeding.message);

  const Disconnect = () => {
    disconnect();
    onDisconnect();
  };

  const ConnectRecentWallet = async () => {
    if (account.isConnected) return;

    try {
      const recent = window.localStorage.getItem("wagmi.recentConnectorId");
      console.log("recent: ", recent);

      const connector = config.connectors.find(
        (v) => v.id === recent || recent?.includes(v.id)
      ) as Connector;

      if (!connector) {
        throw new Error("No recent connector found");
      }

      await connectAsync({ connector });

      // Wait for state changes using polling with timeout
      const waitForStates = () => {
        return new Promise<void>((resolve, reject) => {
          let attempts = 0;
          // 2 minutes = 120 seconds = 120,000ms
          // With 500ms intervals, we need 240 attempts
          const maxAttempts = 240; // 2 minutes total (240 * 500ms)

          const checkStates = () => {
            attempts++;

            // Case 1: Success - both conditions met
            if (gStateP.address.value && !loading.value) {
              resolve();
              return;
            }

            // Case 2: Failed - loading finished but no address
            if (!loading.value && !gStateP.address.value) {
              reject(new Error("Connection failed - no address received"));
              return;
            }

            // Case 3: Timeout after max attempts
            if (attempts >= maxAttempts) {
              reject(new Error("Connection timed out"));
              return;
            }

            // Continue polling
            setTimeout(checkStates, 500);
          };

          checkStates();
        });
      };
      await waitForStates();

      if (gStateP.address.value && !loading.value) {
        console.log("RECONNECTION: Successful");
      }
    } catch (e) {
      console.error("RECONNECTION: Failed", e);

      // Don't show error toast for "Connector already connected" - just handle it silently
      const errorMessage = (e as Error).message || "Failed to reconnect";
      if (!errorMessage.includes("Connector already connected")) {
        toast.error(errorMessage);
      }
    }
  };

  // Events -----------------------------------------

  useAccountEffect({
    onConnect(data) {
      console.log("Account: ", data);
      if (data.isReconnected) {
        refreshAuth(data.address);
        return;
      }
      authRequestSign(data.address);
    },
  });

  const onVerified = () => {
    loading.set(false);
  };

  const onDisconnect = async () => {
    loadingMessage.set("Disconnecting");
    await api.logout();

    localStorage.removeItem("breeding_token");
    gStateP.merge({
      verify: null,
      address: "" as Address,
      isConnected: false,
      refreshToken: "",
      token: "",
    });

    window.stateContext = {
      address: "" as Address,
      isConnected: false,
      publicClient: viemPublicClient!,
      walletClient: viemWalletClient || undefined,
      Disconnect,
      ConnectRecentWallet,
    };

    router.replace("/");
    loading.set(false);
  };

  // Auth Request and Verify -----------------------------------------

  const authRequestSign = async (_address: `0x${string}`) => {
    try {
      // Display Loading
      loadingMessage.set("Requesting to sign message");
      loading.set(true);

      const siweMessage = await prepareSiwe(_address);
      console.log("SIWE Message: ", siweMessage);
      const issuedAt = extractIssuedAt(String(siweMessage));
      console.log("issuedAt: ", issuedAt);

      // Sign Message
      signMessage(
        {
          message: String(siweMessage),
          account: _address,
        },
        {
          async onSuccess(data) {
            try {
              loadingMessage.set("Verifying signature");
              const response = await api.authenticate(_address, data, issuedAt);

              // Store token
              window.localStorage.setItem(
                "breeding_token",
                response.data.token
              );
              gStateP.merge({
                address: _address,
                isConnected: true,
                token: response.data.token,
                refreshToken: response.data.refreshToken,
              });

              onVerified();
            } catch (e) {
              console.error("authenticate: ", e);
              loading.set(false);
              Disconnect();
            }
          },
          async onError(e) {
            console.error("Sign Message: ", e);
            loading.set(false);
            Disconnect();
          },
        }
      );
    } catch (e) {
      console.error("Auth Request: ", e);
      loading.set(false);
      Disconnect();
    }
  };

  const refreshAuth = async (_address: Address) => {
    try {
      console.log("Reconnected: ", _address);
      const data = await api.call(API_ENDPOINTS.me);

      if (data.status && data.data?.token) {
        // Update the token in localStorage
        window.localStorage.setItem("breeding_token", data.data.token);
      }

      onVerified();
    } catch (e) {
      console.log("refreshAuth: ", e);

      loading.set(false);
      Disconnect();
    }
  };

  // Create the context value object
  const contextValue = {
    loading,
    loadingMessage,
    loadingBreeding,
    loadingBreedingMessage,
    address: gStateP.address.value.toLowerCase() as Address,
    Disconnect,
    isConnected: !!gStateP.address.value,
    currentChain:
      Number(process.env.NEXT_PUBLIC_CHAINDID) === 2020 ? ronin : saigon,
    publicClient: viemPublicClient!,
    walletClient: viemWalletClient || undefined,
    ConnectRecentWallet,
  };

  // Expose the context to the window object for non-component access
  if (typeof window !== "undefined") {
    window.stateContext = {
      address: gStateP.address.value.toLowerCase() as Address,
      isConnected: !!gStateP.address.value,
      publicClient: viemPublicClient!,
      walletClient: viemWalletClient || undefined,
      Disconnect,
      ConnectRecentWallet,
    };
  }

  return (
    <StateContext.Provider value={contextValue}>
      {children}

      {loading.value && (
        <LoadingScreen
          className="font-inter text-white"
          message={loadingMessage.value}
        />
      )}
      {loadingBreeding.value && (
        <LoadingScreenBreeding
          className="font-inter text-white"
          message={loadingBreedingMessage.value}
        />
      )}
    </StateContext.Provider>
  );
}
