# Battle API Frontend Integration

This directory contains the frontend integration for the Battle API endpoints that are implemented in the breeding-backend.

## Overview

The Battle API provides chicken ownership verification before allowing players to join battles. The flow consists of:

1. **Request Battle** (`POST /battle/request`) - Generates a message to sign for ownership verification
2. **Verify Battle** (`POST /battle/verify`) - Verifies the signed message and confirms ownership

## Files Structure

```
apps/web/features/battle/
├── api/
│   └── battle-api.ts           # Direct API functions and utilities
├── hooks/
│   ├── useBattleApi.tsx        # React hooks for battle API
│   └── useBattleStats.tsx      # Battle stats hooks
├── types/
│   └── battle.types.ts         # TypeScript interfaces
└── README.md                   # This file
```

## Quick Start

### 1. Basic API Usage

```typescript
import { requestBattle, verifyBattle } from "@/features/battle/api/battle-api";
import { useSignMessage } from "wagmi";

const { signMessage } = useSignMessage();

// Request battle
const requestResult = await requestBattle({ chickenTokenId: 123 });

// Sign the message
const signature = await signMessage({ message: requestResult.data });

// Verify ownership
const verifyResult = await verifyBattle({
  chickenTokenId: 123,
  signature,
});
```

### 2. Using the Hook

```typescript
import { useBattleApi } from "@/features/battle/hooks/useBattleApi";

const MyComponent = () => {
  const { initiateBattle, isBattleInProgress } = useBattleApi();

  const handleBattle = async () => {
    const result = await initiateBattle(123); // chickenTokenId
    if (result) {
      console.log("Battle verified:", result.data);
      // Proceed to game server with verification data
    }
  };

  return (
    <button
      onClick={handleBattle}
      disabled={isBattleInProgress}
    >
      {isBattleInProgress ? "Verifying..." : "Start Battle"}
    </button>
  );
};
```

### 3. Integration with Existing Battle Modal

The Battle API is now integrated directly into the existing BattleModal component. No additional imports needed:

```typescript
// The original BattleModal now includes Battle API integration
import BattleModal from "./battle-modal";

const MyComponent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const chickenStats = {
    level: 10,
    attack: 85,
    defense: 70,
    speed: 90,
    hp: 80,
    maxHp: 100,
  };

  return (
    <BattleModal
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      chickenId="123"
      chickenImage="/path/to/chicken.png"
      chickenStats={chickenStats}
    />
  );
};
```

## API Functions

### `requestBattle(payload)`

- **Purpose**: Request battle verification for a chicken
- **Payload**: `{ chickenTokenId: number }`
- **Returns**: `{ status: number, message: string, data: string }`
- **Usage**: Gets a message that needs to be signed by the user

### `verifyBattle(payload)`

- **Purpose**: Verify the signed message and confirm ownership
- **Payload**: `{ chickenTokenId: number, signature: string }`
- **Returns**: `{ status: number, message: string, data: { chickenTokenId, nonce, signature } }`
- **Usage**: Confirms ownership and provides data for game server

### `completeBattleFlow(chickenTokenId, signMessage)`

- **Purpose**: Complete the entire battle verification flow
- **Parameters**:
  - `chickenTokenId: number`
  - `signMessage: (message: string) => Promise<string>`
- **Returns**: Verification result with game server data
- **Usage**: Handles the complete request → sign → verify flow

## Utility Functions

### `canChickenBattle(chickenStats)`

Checks if a chicken is eligible for battle based on state and HP.

```typescript
const { canBattle, reason } = canChickenBattle(chickenStats);
if (!canBattle) {
  toast.error(reason);
  return;
}
```

### `formatBattleDataForGame(verificationData, address)`

Formats verification data for the game server.

### `generateGameUrl(gameUrl, matchCode, verificationData, address)`

Generates a complete game URL with all required parameters.

## Integration with Existing Components

To integrate with existing battle functionality:

1. **No changes needed for existing battle modal usage**:

   ```typescript
   // The original BattleModal now includes Battle API integration
   import BattleModal from "./battle-modal";

   // Usage remains the same - Battle API integration is automatic
   <BattleModal chickenId="123" ... />
   ```

2. **Update battle handler**:

   ```typescript
   import { canChickenBattle } from "@/features/battle/api/battle-api";

   const handleBattle = (tokenId: string) => {
     const { canBattle, reason } = canChickenBattle(chickenStats);
     if (!canBattle) {
       toast.error(reason);
       return;
     }
     setBattleModalChicken(tokenId);
   };
   ```

3. **Convert stats format**:

   ```typescript
   // Convert existing chicken stats to IBattleStats format
   const convertToIBattleStats = (stats: ChickenStats): IBattleStats => {
     return {
       wins: 0, // Fetch from battle API
       losses: 0,
       draws: 0,
       level: stats.level,
       state: stats.state || "normal",
       stats: {
         attack: stats.attack,
         defense: stats.defense,
         speed: stats.speed,
         currentHp: stats.hp,
         hp: stats.maxHp || stats.hp,
         maxHp: stats.maxHp || stats.hp,
       },
     };
   };
   ```

## Game Server Integration

After successful verification, the data can be used with the game server:

```typescript
const { initiateBattle } = useBattleApi();

const result = await initiateBattle(chickenTokenId);
if (result) {
  const gameParams = {
    fighterId: result.data.chickenTokenId.toString(),
    address: userAddress,
    signature: result.data.signature,
    nonce: result.data.nonce.toString(),
  };

  // Use with matchmaking or direct game connection
  connectToGame(gameParams);
}
```

## Error Handling

The API functions include built-in error handling with user-friendly messages:

- Network errors
- Authentication failures
- Invalid chicken ownership
- Insufficient HP
- Invalid chicken state (dead, faint, breeding)

## Dependencies

- `@tanstack/react-query` - For API state management
- `wagmi` - For wallet signature functionality
- `sonner` - For toast notifications
- `axios` - For HTTP requests (via `@/lib/api`)

## Environment Variables

Make sure these are configured in your environment:

- `NEXT_PUBLIC_BREEDING_API_URL` - Backend API URL
- `NEXT_PUBLIC_GAME_SERVER_URL` - Game server WebSocket URL

## Backend API Endpoints

The frontend connects to these breeding-backend endpoints:

- `POST /battle/request` - Request battle verification
- `POST /battle/verify` - Verify chicken ownership

Both endpoints require authentication via JWT token (handled automatically by the axios instance).
