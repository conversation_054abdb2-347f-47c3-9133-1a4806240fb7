#!/bin/bash

# Exit on any error
set -e

# Configuration
DEPLOY_PATH="./deployment"
BACKUP_PATH="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logger function
log() {
    echo -e "${GREEN}[DEPLOY]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Check required commands
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error "$1 is not installed. Please install it first. $2"
    fi
}

# Check all required commands
check_command "pm2" "Install using: npm install -g pm2"
check_command "yarn" "Install using: npm install -g yarn"
check_command "unzip" "Install using: sudo apt-get install unzip"
check_command "tar" "Install using: sudo apt-get install tar"

# Start the application
log "Starting application with PM2..."
cd "$DEPLOY_PATH" || error "Failed to change to deployment directory"
# Already in deployment directory, no need to cd again

# Reload or start the application with PM2
if pm2 describe breeding-api > /dev/null 2>&1; then
    log "Restarting existing PM2 process with updated environment..."
    pm2 restart breeding-api --update-env || error "Failed to restart application"
else
    log "Starting new PM2 process..."
    pm2 start ecosystem.config.js || error "Failed to start application"
fi

# Save PM2 process list
pm2 save || error "Failed to save PM2 process list"

log "Deployment completed successfully!"
log "View logs using: pm2 log breeding-api"