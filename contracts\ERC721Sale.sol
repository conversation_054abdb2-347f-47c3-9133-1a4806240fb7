// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import {ERC721Common} from "./ERC721Common.sol";
import {NFTLaunchpadCommon as ERC721Launchpad} from "./NFTLaunchpadCommon.sol";
import {NFTPresaleCommon as ERC721Presale} from "./NFTPresaleCommon.sol";

/// @custom:security-contact <EMAIL>
contract ERC721Sale is ERC721Common, ERC721Launchpad, ERC721Presale {
    constructor(
        address owner,
        string memory name,
        string memory symbol,
        string memory baseURI
    ) ERC721Common(owner, name, symbol, baseURI) {}

    /// @dev Mint NFTs for the presale.
    function mintPresale(
        address to,
        uint256 quantity,
        bytes calldata /* extraData */
    )
        external
        onlyRole(MINTER_ROLE)
        returns (uint256[] memory tokenIds, uint256[] memory amounts)
    {
        return _mintForSale(to, quantity);
    }

    /// @dev Mint NFTs for the launchpad.
    function mintLaunchpad(
        address to,
        uint256 quantity,
        bytes calldata /* extraData */
    )
        external
        onlyRole(MINTER_ROLE)
        returns (uint256[] memory tokenIds, uint256[] memory amounts)
    {
        return _mintForSale(to, quantity);
    }

    /**
     * @dev See {ERC165-supportsInterface}.
     */
    function supportsInterface(
        bytes4 interfaceId
    )
        public
        view
        virtual
        override(ERC721Common, ERC721Launchpad, ERC721Presale)
        returns (bool)
    {
        return
            ERC721Common.supportsInterface(interfaceId) ||
            ERC721Launchpad.supportsInterface(interfaceId) ||
            ERC721Presale.supportsInterface(interfaceId);
    }

    function _mintForSale(
        address to,
        uint256 quantity
    ) internal returns (uint256[] memory tokenIds, uint256[] memory amounts) {
        tokenIds = new uint256[](quantity);
        amounts = new uint256[](quantity);

        for (uint256 i; i < quantity; ++i) {
            tokenIds[i] = _mintFor(to);
            amounts[i] = 1;
        }
    }
}
