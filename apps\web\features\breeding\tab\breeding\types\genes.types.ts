export interface IGenesResponse {
  success: boolean;
  decodedGene: IDecodedGene;
  chickenId: number;
  originalGene: string;
  actualTraits: IActualTraits;
}

export interface IDecodedGene {
  Feet: IFeet;
  Tail: ITail;
  Body: IBody;
  Wings: IWings;
  Eyes: IEyes;
  Beak: IBeak;
  Comb: IComb;
  Color: IColor;
  "Innate Attack": number;
  "Innate Defense": number;
  "Innate Speed": number;
  "Innate Health": number;
  Instinct: string;
}

export interface IFeet {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface ITail {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IBody {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IWings {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IEyes {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IBeak {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IComb {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IColor {
  p: string;
  h1: string;
  h2: string;
  h3: string;
}

export interface IActualTraits {
  Feet: string;
  Tail: string;
  Body: string;
  Wings: string;
  Eyes: string;
  Beak: string;
  Comb: string;
  Instinct: string;
  Color: string;
}
