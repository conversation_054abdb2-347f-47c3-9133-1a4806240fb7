"use client";

import { queryClient } from "@/providers/lib/react-query";
import { useStateContext } from "@/providers/app/state";
import React, { useEffect, useState } from "react";
import { Tabs } from "ui";
import { Address } from "viem";
import { useChickens } from "../hooks/useChickens";
import { mockSelectedChickens } from "../mock/ninuno-mock-data";
import { IChickenInfo, IChickenSelection } from "../types/ninuno.types";
import { ChickenRewardsList } from "./chicken-rewards-list";
import { ClaimHistoryTable } from "./claim-history-table";
import { ClaimRequestForm } from "./claim-request-form";
import { TransferBalanceSection } from "./transfer-balance-section";
import { TransferLogsTable } from "./transfer-logs-table";

interface INinunoRewardsTabProps {
  className?: string;
}

/**
 * NinunoRewardsTab Component
 *
 * Contains all the Ninuno Rewards functionality extracted from the original dashboard.
 * MVP version with mock data and simplified functionality.
 */
export const NinunoRewardsTab: React.FC<INinunoRewardsTabProps> = ({
  className,
}) => {
  const { address } = useStateContext();
  const { ninunoRewards, isLoading } = useChickens(address as Address);

  // State for the MVP
  const [chickens, setChickens] = useState<IChickenInfo[]>([]);
  const [selectedChickens, setSelectedChickens] =
    useState<IChickenSelection>(mockSelectedChickens);

  // Function to toggle chicken selection
  const toggleChickenSelection = (tokenId: string) => {
    setSelectedChickens((prev) => ({
      ...prev,
      [tokenId]: !prev[tokenId],
    }));
  };

  // Function to select all chickens
  const selectAllChickens = () => {
    const newSelection: IChickenSelection = {};
    chickens.forEach((chicken) => {
      newSelection[chicken.tokenId] = true;
    });
    setSelectedChickens(newSelection);
  };

  // Function to deselect all chickens
  const deselectAllChickens = () => {
    setSelectedChickens({});
  };

  useEffect(() => {
    if (ninunoRewards && ninunoRewards.length > 0) {
      setChickens(
        ninunoRewards.map((chicken) => ({
          tokenId: chicken.token_id.toString(),
          name: `Chicken #${chicken.token_id}`,
          image: `https://chicken-api-ivory.vercel.app/api/image/${chicken.token_id}.png`,
          accumulatedRewards: Number(chicken.balance) / 10 ** 18,
          lastUpdated: new Date(chicken.updated_at).toISOString(),
        }))
      );
    }
  }, [ninunoRewards]);

  return (
    <div className={className}>
      <div className="mt-6">

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <ChickenRewardsList
              chickens={chickens}
              selectedChickens={selectedChickens}
              toggleChickenSelection={toggleChickenSelection}
              selectAllChickens={selectAllChickens}
              deselectAllChickens={deselectAllChickens}
              isLoading={isLoading}
              paginationLimit={5}
            />
          </div>
          <div>
            <TransferBalanceSection
              className="mb-6"
              chickens={chickens}
              selectedChickens={selectedChickens}
              onTransferSuccess={() => {
                // Update claimable balance
                queryClient.invalidateQueries({
                  queryKey: ["me"],
                });
                queryClient.invalidateQueries({
                  queryKey: ["transferHistory"],
                });

                // Clear selections
                deselectAllChickens();
              }}
            />
            <ClaimRequestForm />
          </div>
        </div>

        <div className="mt-12">
          <Tabs aria-label="$COCK Rewards History">
            <Tabs.List>
              <Tabs.Tab id="transfer-history">Transfer History</Tabs.Tab>
              <Tabs.Tab id="claim-history">Claim History</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel id="transfer-history">
              <div className="mt-6">
                <TransferLogsTable />
              </div>
            </Tabs.Panel>

            <Tabs.Panel id="claim-history">
              <div className="mt-6">
                <ClaimHistoryTable />
              </div>
            </Tabs.Panel>
          </Tabs>
        </div>
      </div>
    </div>
  );
};