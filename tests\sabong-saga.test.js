const { expect } = require("chai");
const { ethers } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");
const abi = require("../scripts/breeding/abi.json");

describe("SabongSagaBreeding Contract", function () {
    let Breeding, breeding;
    let MockERC20, cock;
    let MockERC721, genesis, legacy;
    let MockERC1155, feathers, resources;
    let owner, player, treasury, signer;

    const cooldown = 3600; // 1 hour cooldown
  
    before(async function () {
        [owner, player, treasury, signer] = await ethers.getSigners();

        // Deploy Mock ERC20 (COCK Token)
        MockERC20 = await ethers.getContractFactory("MockERC20");
        cock = await MockERC20.deploy();
        await cock.waitForDeployment();

        // Deploy Mock ERC721 (Genesis and Legacy Chickens)
        MockERC721 = await ethers.getContractFactory("MockERC721");
        genesis = await MockERC721.deploy("Genesis Chicken", "GCHICKEN", 0);
        await genesis.waitForDeployment();

        legacy = await MockERC721.deploy("Legacy Chicken", "LCHICKEN", 2222);
        await legacy.waitForDeployment();

        // Deploy Mock ERC1155 (Feathers and Resources)
        MockERC1155 = await ethers.getContractFactory("MockERC1155");
        feathers = await MockERC1155.deploy();
        await feathers.waitForDeployment();

        resources = await MockERC1155.deploy();
        await resources.waitForDeployment();

        // Deploy Breeding Contract
        Breeding = await ethers.getContractFactory("SabongSagaBreeding");
        breeding = await Breeding.deploy(
            await cock.getAddress(),
            await genesis.getAddress(),
            await legacy.getAddress(),
            await feathers.getAddress(),
            await resources.getAddress(),
            treasury.address,
            signer.address
        );
        await breeding.waitForDeployment();
    });

    async function getSignature(
        userAddress, 
        parent1, 
        parent2, 
        totalAmount, 
        amountToVault, 
        amountToNinuno,
        breedingCooldownTime,
        feathersData,  
        resourcesData, 
        ) {
        // Hash the resources data separately
        const resourcesHash = ethers.keccak256(
            ethers.AbiCoder.defaultAbiCoder().encode(
                ["uint256[][]"],
                [resourcesData]
            )
        );
        const feathersHash = ethers.keccak256(
            ethers.AbiCoder.defaultAbiCoder().encode(
                ["uint256[][]"],
                [feathersData]
            )
        );

        const messageHash = ethers.solidityPackedKeccak256(
        ["address", "uint256", "uint256", "uint256", "uint256", "uint256", "uint256", "bytes32", "bytes32"],
        [userAddress, parent1, parent2, totalAmount, amountToVault, amountToNinuno, breedingCooldownTime, feathersHash, resourcesHash]
        );

        return signer.signMessage(ethers.getBytes(messageHash));
    }
    
    it("Should mint assets", async function(){
        await genesis.mint(player.address);
        await legacy.mint(player.address);
        await cock.mint(player.address, ethers.parseEther("1000000"));
        await cock.connect(player).approve(await breeding.getAddress(), ethers.parseEther("1000000"));
    });

    it("Should give player $COCK tokens", async function () {
        await cock.mint(owner.address, ethers.parseEther("1000000"));
        await cock.transfer(player.address, ethers.parseEther("100"));

        expect(await cock.balanceOf(player.address)).to.equal(ethers.parseEther("1000100"));
    });

    it("Should allow player to breed chickens with valid signature", async function () {
        // Approve contract to spend $COCK
        await cock.connect(player).approve(await breeding.getAddress(), ethers.parseEther("100"));

        const totalAmount = ethers.parseEther("10");
        const amountToVault = ethers.parseEther("3");
        const amountToNinuno = ethers.parseEther("3.5");
        const breedingCooldownTime = BigInt(cooldown);
        const feathersData = [];  // Updated to empty nested array
        const resourcesData = [];  // Updated to empty nested array

        const signature = await getSignature(
            player.address,
            BigInt(1),
            BigInt(2223),
            totalAmount,
            amountToVault,
            amountToNinuno,
            breedingCooldownTime,
            feathersData,
            resourcesData
        );

        await expect(
            breeding.connect(player).breed(
                1,
                2223,
                totalAmount,
                amountToVault,
                amountToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData,
                signature
            )
        ).to.emit(breeding, "Breed");
    });

    it("Should initially track breed count and time", async function () {
        // Use a token ID that hasn't been used for breeding yet
        const unusedTokenId = 100;
        
        const breedCounts = await breeding.getChickenBreedCountBatch([unusedTokenId]);
        const breedTimes = await breeding.getChickenBreedTimeBatch([unusedTokenId]);

        expect(breedCounts[0]).to.equal(0n);
        expect(breedTimes[0]).to.equal(0n);
    });

    it("Should handle batch breeding", async function() {
        // Ensure cooldown period has passed
        await time.increase(cooldown + 1);

        // Mint required tokens first
        await genesis.mint(player.address); // This will mint token ID 2
        await legacy.mint(player.address); // This will mint token ID 2224

        const params = {
            chickenLeftTokenIds: [1n, 2n],
            chickenRightTokenIds: [2223n, 2224n],
            totalAmounts: [ethers.parseEther("10"), ethers.parseEther("10")],
            amountsToVault: [ethers.parseEther("3"), ethers.parseEther("3")],
            amountsToNinuno: [ethers.parseEther("3.5"), ethers.parseEther("3.5")],
            breedingCooldownTimes: [BigInt(cooldown), BigInt(cooldown)],
            feathersData: [[], []], // Updated to nested arrays
            resourcesData: [[], []], // Updated to nested arrays
            signatures: []
        };

        // Generate signatures for each breeding pair
        for(let i = 0; i < 2; i++) {
            const signature = await getSignature(
                player.address,
                params.chickenLeftTokenIds[i],
                params.chickenRightTokenIds[i],
                params.totalAmounts[i],
                params.amountsToVault[i],
                params.amountsToNinuno[i],
                params.breedingCooldownTimes[i],
                params.feathersData[i],
                params.resourcesData[i]
            );
            params.signatures.push(signature);
        }

        await expect(breeding.connect(player).breedBatch(params))
            .to.emit(breeding, "Breed");
    });

    it("Should revert batch breeding with invalid array lengths", async function() {
        const params = {
            chickenLeftTokenIds: [1],
            chickenRightTokenIds: [2223, 2224], // Mismatched length
            totalAmounts: [ethers.parseEther("10")],
            amountsToVault: [ethers.parseEther("3")],
            amountsToNinuno: [ethers.parseEther("3.5")],
            breedingCooldownTimes: [cooldown],
            feathersData: [[]], // Updated parameter name and structure
            resourcesData: [[]], // Updated parameter name and structure
            signatures: []
        };

        await expect(breeding.connect(player).breedBatch(params))
            .to.be.revertedWithCustomError(breeding, "InvalidArrayLength");
    });

    describe("Genesis Identification", function() {
        it("Should correctly identify Genesis tokens", async function() {
            const tokenIds = [1, 2222, 2223, 3000];
            const results = await breeding.isGenesisBatch(tokenIds);
            
            expect(results[0]).to.be.true;  // 1 is Genesis
            expect(results[1]).to.be.true;  // 2222 is Genesis
            expect(results[2]).to.be.false; // 2223 is Legacy
            expect(results[3]).to.be.false; // 3000 is Legacy
        });
    });

    describe("Breeding Time Tracking", function() {
        it("Should track breeding times correctly", async function() {
            // Mint new tokens with unique IDs for this test
            const genesisTx = await genesis.mint(player.address);
            await genesisTx.wait();
            const genesisTokenId = 3; // Genesis tokens start from 1

            const legacyTx = await legacy.mint(player.address);
            await legacyTx.wait();
            const legacyTokenId = 2223; // Legacy tokens start from 2223
            
            // Ensure player has enough COCK tokens
            await cock.mint(player.address, ethers.parseEther("1000"));
            await cock.connect(player).approve(await breeding.getAddress(), ethers.parseEther("1000"));

            // Get initial breeding time
            const initialBreedTime = await breeding.getChickenBreedTimeBatch([genesisTokenId]);
            expect(initialBreedTime[0]).to.equal(0n);

            // Wait for any cooldown to pass
            await time.increase(cooldown + 1);

            // Perform breeding
            const totalAmount = ethers.parseEther("10");
            const amountToVault = ethers.parseEther("3");
            const amountToNinuno = ethers.parseEther("3.5");
            const breedingCooldownTime = BigInt(cooldown);
            const feathersData = [];  // Empty nested array for feathers
            const resourcesData = [];  // Empty nested array for resources

            const signature = await getSignature(
                player.address,
                BigInt(genesisTokenId),
                BigInt(legacyTokenId),
                totalAmount,
                amountToVault,
                amountToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData
            );

            await breeding.connect(player).breed(
                genesisTokenId,
                legacyTokenId,
                totalAmount,
                amountToVault,
                amountToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData,
                signature
            );

            // Check updated breeding time
            const updatedBreedTime = await breeding.getChickenBreedTimeBatch([genesisTokenId]);
            expect(updatedBreedTime[0]).to.be.gt(0n);
        });

        it("Should handle multiple tokens in getChickenBreedTimeBatch", async function() {
            // Mint tokens first
            const tokenIds = [];
            for(let i = 0; i < 3; i++) {
                await genesis.connect(player).mint(player.address);
                tokenIds.push(BigInt(await genesis.totalSupply()));
            }
            
            const breedTimes = await breeding.getChickenBreedTimeBatch(tokenIds);
            
            expect(breedTimes.length).to.equal(3);
            breedTimes.forEach(time => {
                expect(time).to.be.a('bigint');
            });
        });
    });

    describe("Breeding Count Tracking", function() {
        it("Should track breeding counts correctly", async function() {
            // Mint new tokens with unique IDs for this test
            const genesisTx = await genesis.mint(player.address);
            await genesisTx.wait();
            const genesisTokenId = 2; // Next genesis token

            const legacyTx = await legacy.mint(player.address);
            await legacyTx.wait();
            const legacyTokenId = 2224; // Next legacy token
            
            // Ensure player has enough COCK tokens
            await cock.mint(player.address, ethers.parseEther("1000"));
            await cock.connect(player).approve(await breeding.getAddress(), ethers.parseEther("1000"));

            // Get initial breed count
            const initialBreedCount = await breeding.getChickenBreedCountBatch([genesisTokenId]);
            expect(initialBreedCount[0]).to.equal(1n);

            // Wait for any cooldown to pass
            await time.increase(cooldown + 1);

            // Perform breeding
            const totalAmount = ethers.parseEther("10");
            const amountToVault = ethers.parseEther("3");
            const amountToNinuno = ethers.parseEther("3.5");
            const breedingCooldownTime = BigInt(cooldown);
            const feathersData = [];  // Empty nested array for feathers
            const resourcesData = [];  // Empty nested array for resources

            const signature = await getSignature(
                player.address,
                BigInt(genesisTokenId),
                BigInt(legacyTokenId),
                totalAmount,
                amountToVault,
                amountToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData
            );

            await breeding.connect(player).breed(
                genesisTokenId,
                legacyTokenId,
                totalAmount,
                amountToVault,
                amountToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData,
                signature
            );

            // Check updated breed count
            const updatedBreedCount = await breeding.getChickenBreedCountBatch([genesisTokenId]);
            expect(updatedBreedCount[0]).to.equal(2n);
        });

        it("Should handle multiple tokens in getChickenBreedCountBatch", async function() {
            // Mint tokens first
            const tokenIds = [];
            for(let i = 0; i < 3; i++) {
                await genesis.connect(player).mint(player.address);
                tokenIds.push(BigInt(await genesis.totalSupply()));
            }
            
            const breedCounts = await breeding.getChickenBreedCountBatch(tokenIds);
            
            expect(breedCounts.length).to.equal(3);
            breedCounts.forEach(count => {
                expect(count).to.be.a('bigint');
            });
        });
    });

    describe("Batch Breeding", function() {
        it("Should enforce maximum batch size", async function() {
            const params = {
                chickenLeftTokenIds: Array(11).fill(1n),  // 11 pairs (exceeds 10 limit)
                chickenRightTokenIds: Array(11).fill(2223n),
                totalAmounts: Array(11).fill(ethers.parseEther("10")),
                amountsToVault: Array(11).fill(ethers.parseEther("3")),
                amountsToNinuno: Array(11).fill(ethers.parseEther("3.5")),
                breedingCooldownTimes: Array(11).fill(BigInt(cooldown)),
                feathersData: Array(11).fill([]),  // Updated from itemsTokenIds
                resourcesData: Array(11).fill([]),  // Updated from itemsAmounts
                signatures: Array(11).fill("0x")
            };

            await expect(breeding.connect(player).breedBatch(params))
                .to.be.revertedWithCustomError(breeding, "InvalidArrayLength");
        });

        it("Should validate array lengths match", async function() {
            const params = {
                chickenLeftTokenIds: [1n],
                chickenRightTokenIds: [2223n, 2224n], // Mismatched length
                totalAmounts: [ethers.parseEther("10")],
                amountsToVault: [ethers.parseEther("3")],
                amountsToNinuno: [ethers.parseEther("3.5")],
                breedingCooldownTimes: [BigInt(cooldown)],
                feathersData: [[]], // Updated from itemsTokenIds
                resourcesData: [[]], // Updated from itemsAmounts
                signatures: ["0x"]
            };

            await expect(breeding.connect(player).breedBatch(params))
                .to.be.revertedWithCustomError(breeding, "InvalidArrayLength");
        });
    });

    it("Should breed with feathers and resources", async function() {
        await time.increase(cooldown + 1);

        const totalAmount = ethers.parseEther("10");
        const amountToVault = ethers.parseEther("3");
        const amountToNinuno = ethers.parseEther("3.5");
        const breedingCooldownTime = BigInt(cooldown);
        
        // Format: [[tokenId, amount], ...]
        const feathersData = [
            [1n, 2n],  // [tokenId, amount]
            [3n, 1n]   // [tokenId, amount]
        ];
        
        // Format: [[index, tokenId, amount], ...]
        const resourcesData = [
            [0n, 1n, 2n],  // [index, tokenId, amount]
            [1n, 2n, 1n]   // [index, tokenId, amount]
        ];

        // Mint required feathers to the player
        for (const [tokenId, amount] of feathersData) {
            await feathers.mint(player.address, tokenId, amount);
            await feathers.connect(player).setApprovalForAll(await breeding.getAddress(), true);
        }
        
        // Mint required resources to the player
        for (const [_, tokenId, amount] of resourcesData) {
            await resources.mint(player.address, tokenId, amount);
            await resources.connect(player).setApprovalForAll(await breeding.getAddress(), true);
        }

        const signature = await getSignature(
            player.address,
            1n,
            2223n,
            totalAmount,
            amountToVault,
            amountToNinuno,
            breedingCooldownTime,
            feathersData,
            resourcesData
        );

        await expect(
            breeding.connect(player).breed(
                1,
                2223,
                totalAmount,
                amountToVault,
                amountToNinuno,
                breedingCooldownTime,
                feathersData,
                resourcesData,
                signature
            )
        ).to.emit(breeding, "Breed");
    });

    it("Should impersonate account and test breeding", async function() {

        let devAddress = "******************************************"
        await hre.network.provider.request({
            method: "hardhat_impersonateAccount",
            params: [devAddress],
        });

        const devSigner = await ethers.getSigner(devAddress);

        let current = {
            chickenLeftTokenId: '20',
            chickenRightTokenId: '2240',
            totalAmount: '2479080000000000000000',
            amountToVault: '2231172000000000000000',
            amountToNinuno: '247908000000000020000',
            feathersData: [ [ '1', '60' ] ],
            resourcesData: [],
            breedingCooldownTime: '172800',
            // signature: '0x4d6bddbf70f22f8f3d181ae2fc2bd1fbb6a86def2a5e7c327de19777ac672c3d1a700c44e187cd856e5fc9c6e6c2fe7044ce3756ec0ab17148e3ea2cb81019ec1b'
        }
        
        const signature = await getSignature(
            devAddress,
            current.chickenLeftTokenId,
            current.chickenRightTokenId,
            current.totalAmount,
            current.amountToVault,
            current.amountToNinuno,
            current.breedingCooldownTime,
            current.feathersData,
            current.resourcesData
        );


        const breedingSaigon = new ethers.Contract(
        abi.sabong_saga_breeding_address,
        abi.sabong_saga_breeding,
        devSigner
        );

        const feathersSaigon = new ethers.Contract(
            abi.sabong_saga_items_address,
            abi.erc1155_common,
            devSigner
        );

        await feathersSaigon.connect(devSigner).setApprovalForAll(abi.sabong_saga_breeding_address, true);

        const resourcesSaigon = new ethers.Contract(
            abi.sabong_saga_resources_address,
            abi.erc1155_common,
            devSigner
        );

        await resourcesSaigon.connect(devSigner).setApprovalForAll(abi.sabong_saga_breeding_address, true);

        await expect(
            breedingSaigon.connect(devSigner).breed(
                current.chickenLeftTokenId,
                current.chickenRightTokenId,
                current.totalAmount,
                current.amountToVault,
                current.amountToNinuno,
                current.breedingCooldownTime,
                current.feathersData,
                current.resourcesData,
                signature
            )
        ).to.emit(breedingSaigon, "Breed");
    })
});
