"use client";

import React from "react";
import { Tabs } from "ui";
import SidebarNavLayout from "../dapp/components/layout/sidebar-nav.layout";
import { DailyRub } from "./daily-rub";
import { TopChichenTenders } from "./top-chicken-tenders";
import { ClaimStatus } from "./claim-status";

export default function Activities() {
  return (
    <SidebarNavLayout>
      <Tabs aria-label="Activities">
        <Tabs.List className="border-none font-Poppins">
          <Tabs.Tab
            id="dailyRub"
            className={({ isSelected }) =>
              isSelected ? "text-primary hover:text-primary" : ""
            }
            indicatorClassName="bg-primary"
          >
            Daily Rub
          </Tabs.Tab>
          <Tabs.Tab
            id="topChickenTenders"
            className={({ isSelected }) =>
              isSelected ? "text-primary hover:text-primary" : ""
            }
            indicatorClassName="bg-primary"
          >
            Top Chichen Tenders
          </Tabs.Tab>
          <Tabs.Tab
            id="claimStatus"
            className={({ isSelected }) =>
              isSelected ? "text-primary hover:text-primary" : ""
            }
            indicatorClassName="bg-primary"
          >
            Claim Status
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel id="dailyRub">
          <DailyRub />
        </Tabs.Panel>
        <Tabs.Panel id="topChickenTenders">
          <TopChichenTenders />
        </Tabs.Panel>
        <Tabs.Panel id="claimStatus">
          <ClaimStatus />
        </Tabs.Panel>
      </Tabs>
    </SidebarNavLayout>
  );
}
