import { test } from '@japa/runner'
import { decodeGene, rerollInnatePoints } from 'App/Helper/chicken'

test.group('Test decode genes', () => {
  // Write your test here
  test('Test decode genes', async ({ assert }) => {
    const genes =
      '13131105050505080300020407060607120c0c08090f091010101010110017030000001f0000001700000003000000280000000a000000000000000000000000'
    const decodedGenes = decodeGene(genes)
    console.log(decodedGenes)
    assert.isNotNull(decodedGenes)
  })

  test('Test reroll', async ({}) => {
    rerollInnatePoints(11111)
  })
})
