import {
  createP<PERSON>licClient,
  createWalletClient,
  http,
  PublicClient,
  WalletClient,
} from "viem";
import { ronin, saigon } from "viem/chains";

export const CHAIN_ID = process.env.NEXT_PUBLIC_CHAINDID
  ? parseInt(process.env.NEXT_PUBLIC_CHAINDID)
  : 2021;

export const publicClient = createPublicClient({
  chain: CHAIN_ID === 2020 ? ronin : saigon,
  transport: http(),
});

export const createViemWalletClient = (transport: any): WalletClient | null => {
  if (!transport || typeof transport !== "function") {
    console.error("Invalid transport provided to createViemWalletClient");
    return null;
  }

  try {
    return createWalletClient({
      chain: CHAIN_ID === 2020 ? ronin : saigon,
      transport,
    });
  } catch (error) {
    console.error("Failed to create wallet client:", error);
    return null;
  }
};

export type { PublicClient, WalletClient };
