import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { initializeContracts } from 'App/Helper/contracts'
import Chicken from 'App/Models/Chicken'
import Rental, {
  LegendaryFeatherBenefactorType,
  RentalStatus,
  RubStreakBenefactorType,
} from 'App/Models/Rental'
import CreateRentalValidator from 'App/Validators/CreateRentalValidator'
import RentChickenValidator from 'App/Validators/RentChickenValidator'
import axios from 'axios'
import SABONG_CONFIG from 'Config/sabong'
import { DateTime } from 'luxon'
import { encodePacked, keccak256 } from 'viem'
import { signMessage } from 'viem/accounts'

export default class RentalsController {
  public async createRental({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const {
      chickenTokenId,
      roninPrice,
      rentalPeriod,
      rewardDistribution,
      delegatedTask,
      sharedRewardAmount,
      renter<PERSON><PERSON><PERSON>,
      rubStreakBenefactor,
      legendaryFeatherBenefactor,
      insurancePrice,
    } = await request.validate(CreateRentalValidator)

    // Check if chicken exists
    const chicken = await Chicken.findBy('tokenId', chickenTokenId)
    if (!chicken) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Chicken not found',
      })
    }

    // Check chicken ownership
    const { chickenLegacyContract, chickenGenesisContract } = initializeContracts()

    try {
      const chickenOwner =
        chickenTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
          ? await chickenLegacyContract.read.ownerOf([BigInt(chickenTokenId)])
          : await chickenGenesisContract.read.ownerOf([BigInt(chickenTokenId)])

      if (chickenOwner.toLowerCase() !== auth.user.blockchainAddress.toLowerCase()) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'You do not own this chicken',
        })
      }
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to verify chicken ownership',
      })
    }

    // Check if chicken is already listed for rental
    const existingRental = await Rental.query()
      .where('chickenTokenId', chickenTokenId)
      .where('status', RentalStatus.AVAILABLE)
      .first()

    if (existingRental) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Chicken is already listed for rental',
      })
    }

    try {
      const metadata = await axios.get(
        `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/${chickenTokenId}`
      )

      const chickenState = metadata.data.attributes.find(
        (attr: any) => attr.trait_type === 'State'
      ).value

      if (chickenState !== 'Normal') {
        response.status(400)
        return response.json({
          status: 0,
          message: `Chicken is in ${chickenState} state`,
        })
      }

      const feathers = Number(
        metadata.data.attributes.find((attr: any) => attr.trait_type === 'Daily Feathers').value
      )

      if (sharedRewardAmount && sharedRewardAmount > feathers) {
        response.status(400)
        return response.json({
          status: 0,
          message: `Shared reward amount cannot be more than ${feathers}`,
        })
      }
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to fetch chicken metadata',
      })
    }

    if (renterAddress && roninPrice === '0') {
      await Rental.create({
        chickenTokenId,
        ownerAddress: auth.user.blockchainAddress,
        renterAddress,
        roninPrice: BigInt(roninPrice),
        rentalPeriod,
        rentedAt: DateTime.now(),
        expiresAt: DateTime.now().plus({ seconds: rentalPeriod }),
        status: RentalStatus.PENDING,
        insurancePrice: BigInt(insurancePrice || 0),
        signature: null,
        rewardDistribution: rewardDistribution || 1, // Default to DELEGATOR_ONLY})
        delegatedTask: delegatedTask || 3, // Default to BOTH
        sharedRewardAmount: rewardDistribution === 3 ? sharedRewardAmount : null,
        rubStreakBenefactor: rubStreakBenefactor || RubStreakBenefactorType.DELEGATOR,
        legendaryFeatherBenefactor:
          legendaryFeatherBenefactor || LegendaryFeatherBenefactorType.DELEGATOR,
      })

      return response.json({
        status: 1,
        message: 'Chicken delegated successfully',
      })
    }

    if (!renterAddress && roninPrice !== '0') {
      // Create rental listing
      const rental = await Rental.create({
        chickenTokenId,
        ownerAddress: auth.user.blockchainAddress,
        renterAddress: null,
        roninPrice: BigInt(roninPrice),
        rentalPeriod,
        rentedAt: null,
        expiresAt: null,
        status: RentalStatus.PENDING,
        insurancePrice: BigInt(insurancePrice || 0),
        signature: null,
        rewardDistribution: rewardDistribution || 1, // Default to DELEGATOR_ONLY
        delegatedTask: delegatedTask || 3, // Default to BOTH
        sharedRewardAmount: rewardDistribution === 3 ? sharedRewardAmount : null,
        rubStreakBenefactor: rubStreakBenefactor || RubStreakBenefactorType.DELEGATOR,
        legendaryFeatherBenefactor:
          legendaryFeatherBenefactor || LegendaryFeatherBenefactorType.DELEGATOR,
      })

      const charHash =
        keccak256(
          encodePacked(
            ['uint256', 'uint256', 'uint256', 'uint256', 'uint256', 'address'],
            [
              BigInt(chickenTokenId),
              BigInt(rental.id),
              BigInt(roninPrice),
              BigInt(insurancePrice || 0),
              BigInt(rentalPeriod),
              auth.user.blockchainAddress as `0x${string}`,
            ]
          )
        ) || ''

      const signature = await signMessage({
        message: { raw: charHash },
        privateKey: SABONG_CONFIG.SIGNER_KEY,
      })

      return response.json({
        status: 1,
        message: 'Rental listing pending verification onchain',
        data: {
          chickenTokenId,
          rentalId: rental.id,
          roninPrice: rental.roninPrice,
          insurancePrice: rental.insurancePrice,
          rentalPeriod: rental.rentalPeriod,
          ownerAddress: auth.user.blockchainAddress,
          signature,
        },
      })
    }
  }

  public async listAvailableRentals({ request, response }: HttpContextContract) {
    const { page = 1, pageSize = 10 } = request.qs()

    const rentals = await Rental.query()
      .where('status', RentalStatus.AVAILABLE)
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: rentals,
    })
  }

  public async rentChicken({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { rentalId } = await request.validate(RentChickenValidator)

    // Find the rental
    const rental = await Rental.find(rentalId)
    if (!rental) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental not found',
      })
    }

    // Check if rental is available
    if (rental.status !== RentalStatus.AVAILABLE) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental is not available',
      })
    }

    // Check if renter is not the owner
    if (rental.ownerAddress.toLowerCase() === auth.user.blockchainAddress.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'You cannot rent your own chicken',
      })
    }

    //rentId, chickenId, ethPrice, insurancePrice, renterAddress, renterWallet, ownerAddress
    // Generate signature for the rental
    const charHash =
      keccak256(
        encodePacked(
          ['uint256', 'uint256', 'uint256', 'uint256', 'address', 'address', 'address'],
          [
            BigInt(rental.id),
            BigInt(rental.chickenTokenId),
            rental.roninPrice,
            rental.insurancePrice,
            auth.user.blockchainAddress as `0x${string}`,
            auth.user.blockchainAddress as `0x${string}`,
            rental.ownerAddress as `0x${string}`,
          ]
        )
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    return response.json({
      status: 1,
      data: {
        rentalId: rental.id,
        chickenTokenId: rental.chickenTokenId,
        roninPrice: rental.roninPrice,
        insurancePrice: rental.insurancePrice,
        renterAddress: auth.user.blockchainAddress,
        renterWallet: auth.user.blockchainAddress,
        ownerAddress: rental.ownerAddress,
        signature,
      },
    })
  }

  public async myRentals({ auth, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    // Get rentals where user is owner
    // For marketplace listings (AVAILABLE), don't check expires_at
    // For rented chickens (RENTED), check expires_at
    const ownedRentals = await Rental.query()
      .where('ownerAddress', auth.user.blockchainAddress)
      .andWhere((query) => {
        query.where('status', RentalStatus.AVAILABLE).orWhere((subQuery) => {
          subQuery.where('status', RentalStatus.RENTED).whereRaw('expires_at > NOW()')
        })
      })

    // Get rentals where user is renter (only rented chickens, so always check expires_at)
    const rentedChickens = await Rental.query()
      .where('renterAddress', auth.user.blockchainAddress)
      .where('status', RentalStatus.RENTED)
      .whereRaw('expires_at > NOW()')

    return response.json({
      status: 1,
      data: {
        ownedRentals,
        rentedChickens,
      },
    })
  }

  public async cancelRental({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { rentalId } = request.body()

    // Find the rental
    const rental = await Rental.find(rentalId)
    if (!rental) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental not found',
      })
    }

    // Check chicken ownership
    const { chickenLegacyContract, chickenGenesisContract } = initializeContracts()
    let chickenOwnerOnchain = auth.user.blockchainAddress.toLowerCase()

    try {
      const chickenOwner =
        rental.chickenTokenId > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
          ? await chickenLegacyContract.read.ownerOf([BigInt(rental.chickenTokenId)])
          : await chickenGenesisContract.read.ownerOf([BigInt(rental.chickenTokenId)])

      chickenOwnerOnchain = chickenOwner.toLowerCase()
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to verify chicken ownership',
      })
    }

    // Check if user is the owner
    if (chickenOwnerOnchain !== auth.user.blockchainAddress.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'You are not the owner of this chicken',
      })
    }

    if (rental.status === RentalStatus.RENTED && rental.roninPrice !== BigInt(0)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Rental is already rented and cannot be cancelled',
      })
    }

    const charHash =
      keccak256(
        encodePacked(['uint256', 'uint256'], [BigInt(rental.id), BigInt(rental.chickenTokenId)])
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    return response.json({
      status: 1,
      message: 'Rental cancelling pending transaction onchain',
      data: {
        rentalId,
        chickenTokenId: rental.chickenTokenId,
        signature,
      },
    })
  }

  public async rentalHistory({ auth, request, response }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { page = 1, pageSize = 10 } = request.qs()

    const rentals = await Rental.query()
      .where((query) => {
        query
          .where('ownerAddress', user.blockchainAddress)
          .orWhere('renterAddress', user.blockchainAddress)
      })
      .andWhere((query) => {
        query.where('status', RentalStatus.RENTED).orWhere('status', RentalStatus.CANCELLED)
      })
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: rentals,
    })
  }

  public async getChickenRental({ request, response }: HttpContextContract) {
    const { chickenTokenId } = request.params()

    // Find active rental for the specific chicken (AVAILABLE or RENTED)
    // For marketplace listings (AVAILABLE), don't check expires_at
    // For rented chickens (RENTED), check expires_at
    const rental = await Rental.query()
      .where('chickenTokenId', chickenTokenId)
      .andWhere((query) => {
        query.where('status', RentalStatus.AVAILABLE).orWhere((subQuery) => {
          subQuery.where('status', RentalStatus.RENTED).whereRaw('expires_at > NOW()')
        })
      })
      .first()

    if (!rental) {
      return response.json({
        status: 0,
        data: null,
      })
    }

    return response.json({
      status: 1,
      data: rental,
    })
  }

  public async getChickenRentalsBulk({ request, response }: HttpContextContract) {
    const { chickenTokenIds } = request.body()

    // Find active rentals for the specific chickens (AVAILABLE or RENTED)
    // For marketplace listings (AVAILABLE), don't check expires_at
    // For rented chickens (RENTED), check expires_at
    const rentals = await Rental.query()
      .whereIn('chickenTokenId', chickenTokenIds)
      .andWhere((query) => {
        query.where('status', RentalStatus.AVAILABLE).orWhere((subQuery) => {
          subQuery.where('status', RentalStatus.RENTED).whereRaw('expires_at > NOW()')
        })
      })

    // Create a mapping of chickenTokenId -> rental data
    const rentalMap: Record<number, any> = {}

    // Initialize all requested chickens with null (no rental)
    chickenTokenIds.forEach((tokenId: number) => {
      rentalMap[tokenId] = null
    })

    // Fill in the actual rental data for chickens that have rentals
    rentals.forEach((rental) => {
      rentalMap[rental.chickenTokenId] = rental
    })

    return response.json({
      status: 1,
      data: rentalMap,
    })
  }

  public async getChickenRentalsByWallet({ request, response }: HttpContextContract) {
    const { walletAddress } = request.qs()

    // Find active rentals for the specific wallet address (as owner)
    const rentals = await Rental.query()
      .where('renterAddress', walletAddress)
      .where('status', RentalStatus.RENTED)
      .whereRaw('expires_at > NOW()')
      .orderBy('expiresAt', 'asc')

    const rentalsMetadata = [] as any[]
    try {
      for (const rental of rentals) {
        const metadata = await axios.get(
          `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/${rental.chickenTokenId}`
        )
        let cleanedMetadata = {
          delegatedTask: rental.delegatedTask,
          rewardDistribution: rental.rewardDistribution,
          sharedRewardAmount: rental.sharedRewardAmount,
          renterAddress: walletAddress,
          ownerAddress: rental.ownerAddress,
          tokenId: metadata.data.edition,
          image: metadata.data.image,
          dailyFeathers:
            Number(
              metadata.data.attributes.find((attr: any) => attr.trait_type === 'Daily Feathers')
                .value
            ) || 0,
          legendaryCount:
            Number(
              metadata.data.attributes.find((attr: any) => attr.trait_type === 'Legendary Count')
                .value
            ) || 0,
        }
        rentalsMetadata.push(cleanedMetadata)
      }

      return response.json({
        status: 1,
        data: rentalsMetadata,
      })
    } catch (error) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to fetch rentals',
      })
    }
  }
}
