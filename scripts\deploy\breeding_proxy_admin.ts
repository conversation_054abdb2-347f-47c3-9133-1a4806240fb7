import { HardhatRuntimeEnvironment } from "hardhat/types";
import ProxyAdmin from "hardhat-deploy/extendedArtifacts/ProxyAdmin.json";

const deploy = async ({
  getNamedAccounts,
  deployments,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  
  await deploy("SabongSagaBreedingProxyAdmin", {
    contract: ProxyAdmin,
    from: deployer,
    log: true,
    args: [deployer],
  });
};

deploy.tags = ["SabongSagaBreedingProxyAdmin"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;