"use client";

import { cn } from "@/components/ui";
import { ReactNode } from "react";

interface WalletBalancesProps {
  children: ReactNode;
  className?: string;
}

export default function WalletBalances({
  children,
  className,
}: WalletBalancesProps) {
  return (
    <div
      className={cn(
        "flex gap-3 w-fit items-center bg-stone-800/60 p-2 rounded-lg border border-primary/10",
        className
      )}
    >
      {children}
    </div>
  );
}
