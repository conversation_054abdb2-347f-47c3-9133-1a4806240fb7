import { SabongSagaLaunchpad__factory } from "../../typechain-types";
import { HardhatRuntimeEnvironment } from "hardhat/types";
import TransparentUpgradeableProxy from "hardhat-deploy/extendedArtifacts/TransparentUpgradeableProxy.json";

const launchpadInterface = SabongSagaLaunchpad__factory.createInterface();

const deploy = async ({
  getNamedAccounts,
  deployments,
  network,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  const proxyAdmin = await deployments.get("SabongSagaLaunchpadProxyAdmin");
  const logicContract = await deployments.get("SabongSagaLaunchpadLogic");

  const data = launchpadInterface.encodeFunctionData("initialize", [
   "0x8fd6b3fa81adf438feeb857e0b8aed5f74f718ad",
   "0xc5da607b372eca2794f5b5452148751c358eb53c",
   "0xee82c4d1b4af0ffdbb3671f0d7a0f342fbc704b0",
   "0x322b3d98ddbd589dc2e8dd83659bb069828231e0"
  ]);

  await deploy("SabongSagaLaunchpadProxy", {
    contract: TransparentUpgradeableProxy,
    from: deployer,
    log: true,
    args: [logicContract.address, proxyAdmin.address, data],
  });
};

deploy.tags = ["SabongSagaLaunchpadProxy"];
deploy.dependencies = [
  "VerifyContracts",
  "SabongSagaLaunchpadProxyAdmin",
  "SabongSagaLaunchpadLogic",
];

export default deploy;
