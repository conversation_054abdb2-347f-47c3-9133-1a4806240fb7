"use client";

import React from "react";
import SidebarNavLayout from "./components/layout/sidebar-nav.layout";
import { Ron<PERSON> } from "@/components/shared/icons";

export default function Dapp() {
  return (
    <SidebarNavLayout>
      <div className="h-[30vh] lg:h-[40vh] bg-[#2D2D2D] rounded-xl"></div>

      <h2 className="text-2xl font-semibold font-Poppins mt-8">Latest Sales</h2>
      <div className="flex flex-wrap items-center gap-6 mt-4">
        <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative">
          <div className="absolute inset-x-0 bottom-0 p-3">
            <div>Chicken #120</div>
            <div className="flex items-center gap-2 mt-1">
              <Ronin className="size-4 text-blue-500" />
              <div>1200</div>
            </div>
          </div>
        </div>

        <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative">
          <div className="absolute inset-x-0 bottom-0 p-3">
            <div>Chicken #120</div>
            <div className="flex items-center gap-2 mt-1">
              <Ronin className="size-4 text-blue-500" />
              <div>1200</div>
            </div>
          </div>
        </div>

        <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative">
          <div className="absolute inset-x-0 bottom-0 p-3">
            <div>Chicken #120</div>
            <div className="flex items-center gap-2 mt-1">
              <Ronin className="size-4 text-blue-500" />
              <div>1200</div>
            </div>
          </div>
        </div>
      </div>

      <h2 className="text-2xl font-semibold font-Poppins mt-8">Activities</h2>
      <div className="flex flex-wrap items-center gap-6 mt-4">
        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">Daily Rub</div>
        </div>

        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">Daily Feed</div>
        </div>
      </div>

      <h2 className="text-2xl font-semibold font-Poppins mt-8">Collections</h2>
      <div className="flex flex-wrap items-center gap-6 mt-4">
        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">Genesis Chicken</div>
        </div>

        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">Legacy Chicken</div>
        </div>

        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">Game Items</div>
        </div>
      </div>

      <h2 className="text-2xl font-semibold font-Poppins mt-8 flex items-center gap-2">
        <span>Latest</span>
        <span className="text-base font-normal text-[#8C8C8C]">
          Take a look at what&apos;s new right now
        </span>
      </h2>
      <div className="flex flex-wrap items-center gap-6 mt-4">
        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">How to rub chickens</div>
        </div>

        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">What is ninuno system</div>
        </div>

        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">Legacy mint collections</div>
        </div>

        <div>
          <div className="bg-[#2D2D2D] rounded-xl h-[250px] w-[200px] relative"></div>
          <div className="text-[#8C8C8C] mt-2">What is breeding system</div>
        </div>
      </div>
    </SidebarNavLayout>
  );
}
