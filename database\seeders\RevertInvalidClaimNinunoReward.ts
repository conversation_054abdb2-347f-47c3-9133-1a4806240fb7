import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import ClaimNinunoReward, { ClaimNinunoRewardProcessedStatus } from 'App/Models/ClaimNinunoReward'
import User from 'App/Models/User'

export default class extends BaseSeeder {
  public async run() {
    const withdrawalRequestIds = [
      209, 210, 217, 219, 220, 221, 224, 225, 226, 228, 229, 230, 232, 233, 234, 235, 236, 237, 238,
      239, 240, 241, 242, 243, 244, 246, 247, 249, 250, 251, 252, 253, 254, 256, 257, 258, 260, 261,
      262, 263, 267, 268, 269, 271, 272, 273, 275, 276, 277, 280, 281, 282, 284, 287, 288, 290, 291,
      292, 293, 294, 295, 296, 297, 308, 310, 311, 312, 313, 314, 315, 316, 318, 319, 320, 321, 322,
      323, 324, 325, 326, 327, 328, 329, 330, 331, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342,
      343, 344, 345, 346, 347, 348, 349, 350, 353, 355, 356, 357, 358, 359, 360, 361, 362, 363, 364,
      365, 366, 367, 368, 369, 370, 372, 373, 374, 375, 376, 377, 378, 379, 382, 383, 384, 385, 386,
      387, 391, 396, 397, 398, 399, 400, 401, 403, 404, 406, 407, 408, 409, 410, 412,
    ]

    const getInvalidClaimNinunoReward = await ClaimNinunoReward.query().whereIn(
      'id',
      withdrawalRequestIds
    )

    for (const invalidClaimNinunoReward of getInvalidClaimNinunoReward) {
      const findUser = await User.findBy('blockchainAddress', invalidClaimNinunoReward.address)

      if (findUser) {
        findUser.claimableBalance += invalidClaimNinunoReward.amount
        await findUser.save()
      }

      invalidClaimNinunoReward.amount = 0n
      invalidClaimNinunoReward.processed = ClaimNinunoRewardProcessedStatus.PROCESSED
      await invalidClaimNinunoReward.save()
    }
  }
}
