import { HardhatRuntimeEnvironment } from "hardhat/types";
import ProxyAdmin from "hardhat-deploy/extendedArtifacts/ProxyAdmin.json";

const deploy = async ({
  getNamedAccounts,
  deployments,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();

  await deploy("SabongSagaRentalProxyAdmin", {
    contract: ProxyAdmin,
    from: deployer,
    log: true,
    args: [deployer],
  });
};

deploy.tags = ["SabongSagaRentalProxyAdmin"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;
