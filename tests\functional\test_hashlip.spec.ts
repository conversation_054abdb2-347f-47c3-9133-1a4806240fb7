import { test } from '@japa/runner'
import {
  buildSetup,
  generate,
  getRandomNonLegendaryTrait,
} from './../../feature/hashlips/generator'
import { decodeGene } from 'App/Helper/chicken'
import { breed, TraitCategory, Traits } from './../../feature/hashlips'
import Chicken from 'App/Models/Chicken'

test.group('Test hashlips', () => {
  test('Test forced hashlips', async ({ assert }) => {
    const forcedDNA = [
      {
        trait: 'Feet',
        value: 'Onyx',
      },
      {
        trait: 'Tail',
        value: 'Starjeatl',
      },
      {
        trait: 'Body',
        value: 'Hoeltaf',
      },
      {
        trait: 'Wings',
        value: 'Awra',
      },
      {
        trait: 'Eyes',
        value: '<PERSON>el<PERSON>',
      },
      {
        trait: 'Beak',
        value: 'Flare',
      },
      {
        trait: 'Comb',
        value: 'Hellboy',
      },
      {
        trait: 'Color',
        value: 'H2',
      },
    ]

    buildSetup()
    const metadata = await generate(11111, forcedDNA, 2222, 8888, 'Gen 1', {}, 'Aggressive', [])
    console.log(metadata)
    const genes = metadata?.attributes.find((attr) => attr.trait_type === 'Genes').value

    const decodedGenes = decodeGene(genes)
    console.log(decodedGenes)

    console.log('----------------ATTRIBUTES-------------------')
    for (const attr of metadata?.attributes) {
      if (!decodedGenes?.[attr.trait_type]) continue
      console.log('Metadata: ', attr.value)
      if (typeof decodedGenes?.[attr.trait_type] === 'object') {
        console.log('Decoded: ', decodedGenes?.[attr.trait_type]['p'])
        assert.equal(decodedGenes?.[attr.trait_type]['p'], attr.value)
      } else {
        console.log('Decoded: ', decodedGenes?.[attr.trait_type])
        assert.equal(decodedGenes?.[attr.trait_type], attr.value)
      }
      console.log('-----------------------------------')
    }
  })

  test('Test combined genes', async ({ assert }) => {
    const chickenLeftTokenId = 9997
    const chickenRightTokenId = 9533

    const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

    if (!findChickenLeft) {
      throw new Error('chickenLeftTokenId not found')
    }

    const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

    if (!findChickenRight) {
      throw new Error('chickenRightTokenId not found')
    }

    const chickenLeft = decodeGene(findChickenLeft.genes) as Traits
    const chickenRight = decodeGene(findChickenRight.genes) as Traits

    const { traits: offspring, metadata } = await breed(
      chickenLeft,
      chickenRight,
      11111,
      chickenLeftTokenId,
      chickenRightTokenId
    )

    console.log(chickenLeft)
    console.log(chickenRight)
    console.log(offspring)

    console.log(metadata)
    const genes = metadata?.attributes.find((attr) => attr.trait_type === 'Genes').value

    const decodedGenes = decodeGene(genes)
    console.log('----------------GENES-------------------')
    console.log('offspring: ', offspring)
    console.log('decodedGenes: ', decodedGenes)

    console.log('----------------ATTRIBUTES-------------------')
    for (const attr of metadata?.attributes) {
      if (!decodedGenes?.[attr.trait_type]) continue
      console.log('Metadata: ', attr.value)
      if (typeof decodedGenes?.[attr.trait_type] === 'object') {
        console.log('Decoded: ', decodedGenes?.[attr.trait_type]['p'])
        assert.equal(decodedGenes?.[attr.trait_type]['p'], attr.value)
      } else {
        console.log('Decoded: ', decodedGenes?.[attr.trait_type])
        assert.equal(decodedGenes?.[attr.trait_type], attr.value)
      }
      console.log('-----------------------------------')
    }
  })

  test('Test combined genes with forced DNA', async ({ assert }) => {
    function getRandomTrait(traits: string[]): string {
      // const weights = [37.5, 37.5, 9.4, 9.4, 2.3, 2.3, 0.8, 0.8]
      const weights = [50, 50, 0, 0, 0, 0, 0, 0]
      const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)
      const random = Math.random() * totalWeight

      let cumulativeWeight = 0
      for (let i = 0; i < traits.length; i++) {
        cumulativeWeight += weights[i]
        if (random <= cumulativeWeight) {
          return traits[i]
        }
      }

      // Fallback to first trait (should never happen with proper weights)
      return traits[0]
    }

    function breed(chickenLeft: Traits, chickenRight: Traits): Traits {
      const offspring: Partial<Traits> = {}

      for (const category of Object.keys(chickenLeft) as TraitCategory[]) {
        if (!['Feet', 'Tail', 'Body', 'Wings', 'Eyes', 'Beak', 'Comb', 'Color'].includes(category))
          continue

        const traitsPool = [
          chickenLeft[category].p,
          chickenRight[category].p,
          chickenLeft[category].h1,
          chickenRight[category].h1,
          chickenLeft[category].h2,
          chickenRight[category].h2,
          chickenLeft[category].h3,
          chickenRight[category].h3,
        ]

        const p = getRandomTrait(traitsPool)
        const h1 = getRandomTrait(traitsPool)
        const h2 = getRandomTrait(traitsPool)
        const h3 = getRandomNonLegendaryTrait(category)
        // const h3 = getRandomTrait(traitsPool)

        offspring[category] = {
          p,
          h1,
          h2,
          h3,
        }

        console.log('-----------------------')
        console.log('trait: ', category)
        console.log('pool', JSON.stringify(traitsPool))
        console.log('selected', p)
        console.log('selected', h1)
        console.log('selected', h2)
        console.log('selected', h3)
        assert.include([traitsPool[0], traitsPool[1]], p) // for 50:50 setup
      }

      return offspring as Traits
    }

    for (let i = 0; i < 100; i++) {
      const chickenLeftTokenId = 2222
      const chickenRightTokenId = 8888

      const findChickenLeft = await Chicken.findBy('tokenId', chickenLeftTokenId)

      if (!findChickenLeft) {
        throw new Error('chickenLeftTokenId not found')
      }

      const findChickenRight = await Chicken.findBy('tokenId', chickenRightTokenId)

      if (!findChickenRight) {
        throw new Error('chickenRightTokenId not found')
      }

      const chickenLeft = decodeGene(findChickenLeft.genes) as Traits
      const chickenRight = decodeGene(findChickenRight.genes) as Traits

      breed(chickenLeft, chickenRight)
    }
  })
})
