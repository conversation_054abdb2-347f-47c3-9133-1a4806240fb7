"use client";

import axios from "@/lib/api";
import { IBreedingCooldown } from "../types/breeding-cooldown.types";
import { useQuery } from "@tanstack/react-query";

export const fetchBreedingCooldowns = async () => {
  const { data } = await axios.get("/breeding-cooldowns");
  return data.data as IBreedingCooldown[];
};

export default function useBreedingCooldown() {
  const breedingCooldownsQuery = useQuery({
    queryKey: ["breedingCooldowns"],
    queryFn: fetchBreedingCooldowns,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  const getCooldown = async (parent1BC: number, parent2BC: number) => {
    if (!breedingCooldownsQuery.isSuccess) return 0;

    const parent1CD = breedingCooldownsQuery.data.find(
      (item) => item.count === parent1BC
    );
    const parent2CD = breedingCooldownsQuery.data.find(
      (item) => item.count === parent2BC
    );

    if (!parent1CD || !parent2CD) return 0;

    const ADDITIONAL_COOLDOWN_DAYS = 5;
    const MAX_STANDARD_BREEDING_COUNT = 7;

    const totalParent1CD =
      parent1BC > MAX_STANDARD_BREEDING_COUNT
        ? parent1CD.cooldown + ADDITIONAL_COOLDOWN_DAYS
        : parent1CD.cooldown;

    const totalParent2CD =
      parent2BC > MAX_STANDARD_BREEDING_COUNT
        ? parent2CD.cooldown + ADDITIONAL_COOLDOWN_DAYS
        : parent2CD.cooldown;

    return totalParent1CD + totalParent2CD;
  };

  return { getCooldown };
}
