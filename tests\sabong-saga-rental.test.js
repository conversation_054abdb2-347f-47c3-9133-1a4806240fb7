const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("SabongSagaRental Contract", function () {
  let rental;
  let owner, admin, renter, renterAddress, feeWallet, signer;
  const feePercentage = 250; // 2.5%

  beforeEach(async function () {
    [owner, admin, renter, renterAddress, feeWallet, signer] = await ethers.getSigners();
    
    // Deploy Rental Contract using upgrades plugin
    const Rental = await ethers.getContractFactory("SabongSagaRental");
    rental = await upgrades.deployProxy(Rental, [
      feeWallet.address,
      feePercentage,
      admin.address
    ], {
      initializer: 'initialize',
      kind: 'transparent'
    });
    await rental.waitForDeployment();
    
    // Set signer address
    await rental.connect(admin).setSigner(signer.address);
  });

  async function getSignature(rentId, ethPrice, renterAddress, renter) {
    const messageHash = ethers.solidityPackedKeccak256(
      ["uint256", "uint256", "address", "address"],
      [rentId, ethPrice, renterAddress, renter]
    );
    
    return signer.signMessage(ethers.getBytes(messageHash));
  }

  describe("Initialization", function() {
    it("Should initialize with correct values", async function() {
      expect(await rental.feeWallet()).to.equal(feeWallet.address);
      expect(await rental.feePercentage()).to.equal(feePercentage);
      expect(await rental.signer()).to.equal(signer.address);
      
      const adminRole = await rental.DEFAULT_ADMIN_ROLE();
      const pauserRole = await rental.PAUSER_ROLE();
      
      expect(await rental.hasRole(adminRole, admin.address)).to.be.true;
      expect(await rental.hasRole(pauserRole, admin.address)).to.be.true;
    });
    
    it("Should revert if fee percentage is greater than 10000", async function() {
      const Rental = await ethers.getContractFactory("SabongSagaRental");
      await expect(
        upgrades.deployProxy(Rental, [
          feeWallet.address,
          10001, // Invalid fee percentage
          admin.address
        ], {
          initializer: 'initialize',
          kind: 'transparent'
        })
      ).to.be.revertedWithCustomError(Rental, "ErrInvalidFeePercentage");
    });
  });

  describe("Rental Functionality", function() {
    it("Should allow renting a chicken with valid signature", async function() {
      const rentId = 1;
      const ethPrice = ethers.parseEther("0.1");
      const signature = await getSignature(rentId, ethPrice, renterAddress.address, renter.address);
      
      await expect(
        rental.connect(renter).rentChicken(
          rentId,
          ethPrice,
          renterAddress.address,
          signature,
          { value: ethPrice }
        )
      ).to.emit(rental, "ChickenRented")
        .withArgs(rentId, renter.address, ethPrice, ethPrice * BigInt(feePercentage) / 10000n);
      
      // Check rental info was stored
      const rentalInfo = await rental.rentals(rentId);
      expect(rentalInfo.rentId).to.equal(rentId);
      expect(rentalInfo.ethPrice).to.equal(ethPrice);
      expect(rentalInfo.renter).to.equal(renter.address);
      
      // Check rent ID is marked as used
      expect(await rental.usedRentIds(rentId)).to.be.true;
    });
    
    it("Should revert when using an already used rent ID", async function() {
      const rentId = 2;
      const ethPrice = ethers.parseEther("0.1");
      const signature = await getSignature(rentId, ethPrice, renterAddress.address, renter.address);
      
      // First rental succeeds
      await rental.connect(renter).rentChicken(
        rentId,
        ethPrice,
        renterAddress.address,
        signature,
        { value: ethPrice }
      );
      
      // Second rental with same ID fails
      await expect(
        rental.connect(renter).rentChicken(
          rentId,
          ethPrice,
          renterAddress.address,
          signature,
          { value: ethPrice }
        )
      ).to.be.revertedWithCustomError(rental, "ErrRentIdAlreadyUsed");
    });
    
    it("Should revert with invalid payment amount", async function() {
      const rentId = 3;
      const ethPrice = ethers.parseEther("0.1");
      const invalidPayment = ethers.parseEther("0.05");
      const signature = await getSignature(rentId, ethPrice, renterAddress.address, renter.address);
      
      await expect(
        rental.connect(renter).rentChicken(
          rentId,
          ethPrice,
          renterAddress.address,
          signature,
          { value: invalidPayment }
        )
      ).to.be.revertedWithCustomError(rental, "ErrInvalidPayment");
    });
    
    it("Should revert with invalid signature", async function() {
      const rentId = 4;
      const ethPrice = ethers.parseEther("0.1");
      
      // Generate signature with wrong parameters
      const invalidSignature = await getSignature(rentId, ethPrice, owner.address, renter.address);
      
      await expect(
        rental.connect(renter).rentChicken(
          rentId,
          ethPrice,
          renterAddress.address,
          invalidSignature,
          { value: ethPrice }
        )
      ).to.be.revertedWithCustomError(rental, "ErrInvalidSignature");
    });
    
    it("Should correctly distribute fees", async function() {
      const rentId = 5;
      const ethPrice = ethers.parseEther("1");
      const signature = await getSignature(rentId, ethPrice, renterAddress.address, renter.address);
      
      const feeAmount = (ethPrice * BigInt(feePercentage)) / 10000n;
      const renterAmount = ethPrice - feeAmount;
      
      const feeWalletBalanceBefore = await ethers.provider.getBalance(feeWallet.address);
      const renterAddressBalanceBefore = await ethers.provider.getBalance(renterAddress.address);
      
      await rental.connect(renter).rentChicken(
        rentId,
        ethPrice,
        renterAddress.address,
        signature,
        { value: ethPrice }
      );
      
      const feeWalletBalanceAfter = await ethers.provider.getBalance(feeWallet.address);
      const renterAddressBalanceAfter = await ethers.provider.getBalance(renterAddress.address);
      
      expect(feeWalletBalanceAfter - feeWalletBalanceBefore).to.equal(feeAmount);
      expect(renterAddressBalanceAfter - renterAddressBalanceBefore).to.equal(renterAmount);
    });
  });

  describe("Admin Functions", function() {
    it("Should allow admin to pause and unpause the contract", async function() {
      await rental.connect(admin).pause();
      expect(await rental.paused()).to.be.true;
      
      // Renting should fail when paused
      const rentId = 6;
      const ethPrice = ethers.parseEther("0.1");
      const signature = await getSignature(rentId, ethPrice, renterAddress.address, renter.address);
      
      await expect(
        rental.connect(renter).rentChicken(
          rentId,
          ethPrice,
          renterAddress.address,
          signature,
          { value: ethPrice }
        )
      ).to.be.revertedWithCustomError(rental, "EnforcedPause");
      
      // Unpause and try again
      await rental.connect(admin).unpause();
      expect(await rental.paused()).to.be.false;
      
      await expect(
        rental.connect(renter).rentChicken(
          rentId,
          ethPrice,
          renterAddress.address,
          signature,
          { value: ethPrice }
        )
      ).to.emit(rental, "ChickenRented");
    });
    
    it("Should allow admin to update fee wallet", async function() {
      const newFeeWallet = owner.address;
      await rental.connect(admin).setFeeWallet(newFeeWallet);
      expect(await rental.feeWallet()).to.equal(newFeeWallet);
    });
    
    it("Should allow admin to update signer", async function() {
      const newSigner = owner.address;
      await rental.connect(admin).setSigner(newSigner);
      expect(await rental.signer()).to.equal(newSigner);
    });
    
    it("Should allow admin to update fee percentage", async function() {
      const newFeePercentage = 500; // 5%
      await expect(
        rental.connect(admin).setFeePercentage(newFeePercentage)
      ).to.emit(rental, "FeePercentageUpdated")
        .withArgs(feePercentage, newFeePercentage);
      
      expect(await rental.feePercentage()).to.equal(newFeePercentage);
    });
    
    it("Should revert when setting invalid fee percentage", async function() {
      await expect(
        rental.connect(admin).setFeePercentage(10001)
      ).to.be.revertedWithCustomError(rental, "ErrInvalidFeePercentage");
    });
    
    it("Should allow admin to release ETH in emergency", async function() {
      // First send some ETH to the contract
      await owner.sendTransaction({
        to: await rental.getAddress(),
        value: ethers.parseEther("1")
      });
      
      const recipientBalanceBefore = await ethers.provider.getBalance(owner.address);
      const contractBalance = await ethers.provider.getBalance(await rental.getAddress());
      
      await expect(
        rental.connect(admin).emergencyReleaseETH(owner.address)
      ).to.emit(rental, "EmergencyETHRelease")
        .withArgs(owner.address, contractBalance);
      
      const recipientBalanceAfter = await ethers.provider.getBalance(owner.address);
      expect(recipientBalanceAfter - recipientBalanceBefore).to.equal(contractBalance);
    });
    
    it("Should revert emergency ETH release when contract has no balance", async function() {
      await expect(
        rental.connect(admin).emergencyReleaseETH(owner.address)
      ).to.be.revertedWith("No ETH to release");
    });
    
    it("Should revert emergency ETH release with zero address", async function() {
      await expect(
        rental.connect(admin).emergencyReleaseETH(ethers.ZeroAddress)
      ).to.be.revertedWith("Invalid address");
    });
  });

  describe("Access Control", function() {
    it("Should revert when non-admin tries to pause", async function() {
      await expect(
        rental.connect(renter).pause()
      ).to.be.revertedWithCustomError(rental, "AccessControlUnauthorizedAccount");
    });
    
    it("Should revert when non-admin tries to update fee wallet", async function() {
      await expect(
        rental.connect(renter).setFeeWallet(renter.address)
      ).to.be.revertedWithCustomError(rental, "AccessControlUnauthorizedAccount");
    });
    
    it("Should revert when non-admin tries to update signer", async function() {
      await expect(
        rental.connect(renter).setSigner(renter.address)
      ).to.be.revertedWithCustomError(rental, "AccessControlUnauthorizedAccount");
    });
    
    it("Should revert when non-admin tries to update fee percentage", async function() {
      await expect(
        rental.connect(renter).setFeePercentage(300)
      ).to.be.revertedWithCustomError(rental, "AccessControlUnauthorizedAccount");
    });
    
    it("Should revert when non-admin tries emergency ETH release", async function() {
      await expect(
        rental.connect(renter).emergencyReleaseETH(renter.address)
      ).to.be.revertedWithCustomError(rental, "AccessControlUnauthorizedAccount");
    });
  });
});