import { useState, useEffect } from "react";
import { Button, ProgressCircle } from "@/components/ui";
import useDailyFeedStore from "@/store/daily-feeding";
import useFoodCraftingStore from "@/store/food-crafting";
import { Dispatch, SetStateAction } from "react";
import { Text } from "react-aria-components";
import items from "@/data/crafting_data.json";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

type Props = {
  show: boolean;
  setShow: Dispatch<SetStateAction<boolean>>;
  tokenId: number;
};

export function FeedModal({ show, setShow, tokenId }: Props) {
  const { feedChicken, isPending } = useDailyFeedStore();
  const { foodBalances } = useFoodCraftingStore();
  const router = useRouter();

  const [selectedKey, setSelectedKey] = useState<string | null>(null);
  const [feedAmount, setFeedAmount] = useState<number>(1);
  const [isVisible, setIsVisible] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  const selectedItem = selectedKey
    ? items.find((i) => i.id === Number(selectedKey))
    : null;
  const maxAvailable = selectedKey
    ? Number(foodBalances[Number(selectedKey)])
    : 0;

  // Handle show/hide animations
  useEffect(() => {
    if (show) {
      setIsVisible(true);
      setIsClosing(false);
    } else if (isVisible) {
      setIsClosing(true);
      const timer = setTimeout(() => {
        setIsVisible(false);
        setIsClosing(false);
      }, 500); // Match the transition duration
      return () => clearTimeout(timer);
    }
  }, [show, isVisible]);

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setShow(false);
    }, 500);
  };

  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let val = parseInt(e.target.value, 10);
    if (isNaN(val)) val = 1;
    if (val < 1) val = 1;
    if (val > maxAvailable) val = maxAvailable;
    setFeedAmount(val);
  };

  const handleFeed = async () => {
    if (selectedKey && feedAmount > 0 && feedAmount <= maxAvailable) {
      try {
        if (selectedItem) {
          const collectionId = tokenId > 2222 ? 1 : 0;
          await feedChicken(
            collectionId,
            tokenId,
            Number(selectedKey),
            feedAmount
          );
          toast.success("Daily feed successful", {
            description: `Fed ${feedAmount} ${selectedItem.name}${feedAmount > 1 ? "s" : ""} to your chicken! Your feathered friend is happy and well-nourished.`,
            position: "top-center",
          });
        }

        // handleClose();
        setSelectedKey(null);
        setFeedAmount(1);
      } catch (error) {}
    }
  };

  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 flex items-center justify-center bg-black/80 backdrop-blur-md z-50 p-2 sm:p-4 transition-all duration-500 ease-in-out ${
        isClosing ? "opacity-0" : "opacity-100"
      }`}
    >
      <div
        className={`bg-gradient-to-b from-stone-800 to-stone-900 rounded-xl shadow-2xl max-w-4xl w-full border border-primary/30
        max-h-[90vh] overflow-y-auto flex flex-col transition-all duration-500 ease-in-out transform ${
          isClosing
            ? "opacity-0 scale-95 translate-y-4"
            : "opacity-100 scale-100 translate-y-0"
        }`}
      >
        {/* Header */}
        <div className="bg-primary/10 p-4 flex items-center justify-between sticky top-0 z-10">
          <h2 className="text-2xl font-Arcadia text-primary">Daily Feed</h2>
          <Button
            intent="danger"
            onPress={handleClose}
            className="w-8 h-8 rounded-full"
          >
            <span>×</span>
          </Button>
        </div>

        <div className="p-6 flex-1 overflow-y-auto">
          <div className="flex flex-col font-Poppins mb-4">
            <Text className="text-lg text-white">Select Cookies</Text>
            <Text className="text-sm text-muted-fg">
              Select a cookie you want to feed to your chicken.
            </Text>
          </div>
          {/* Selected Item Details */}
          {selectedItem && (
            <div className="bg-stone-800/70 rounded-lg p-6 shadow-lg border border-primary/20 mb-6 animate-fade-in w-full">
              <div className="flex flex-col lg:flex-row items-center gap-6">
                <div className="flex items-center gap-3 bg-stone-800/80 rounded-lg px-6 py-4 shadow-inner border border-primary/20">
                  <img
                    src={selectedItem.image || "/images/cookie-1.jpg"}
                    alt={selectedItem.name}
                    className="w-14 h-14 object-contain rounded-full border border-primary/30"
                  />
                  <div className="flex-1">
                    <div className="text-primary font-semibold text-lg">
                      {selectedItem.name}
                    </div>
                    <div className="text-xs text-muted-fg">
                      Available:{" "}
                      <span className="font-bold text-white">
                        {maxAvailable}
                      </span>
                    </div>
                    {selectedItem.effect && selectedItem.effect.length > 0 && (
                      <div className="text-green-400 text-xs mt-1">
                        {selectedItem.effect.join(", ")}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col gap-4">
                  <label
                    className="text-sm text-white/80 font-medium"
                    htmlFor="feed-amount"
                  >
                    Amount to feed:
                  </label>
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      onClick={() => setFeedAmount(Math.max(1, feedAmount - 1))}
                      className="w-8 h-8 bg-stone-700/50 hover:bg-stone-600 text-primary rounded-full font-bold transition-colors"
                    >
                      −
                    </button>
                    <input
                      id="feed-amount"
                      type="number"
                      min={1}
                      max={maxAvailable}
                      value={feedAmount}
                      onChange={handleAmountChange}
                      className="w-20 px-3 py-1 rounded-lg border border-primary/40 bg-stone-900 text-white text-center font-semibold focus:ring-2 focus:ring-primary outline-none transition"
                    />
                    <button
                      type="button"
                      onClick={() =>
                        setFeedAmount(Math.min(maxAvailable, feedAmount + 1))
                      }
                      className="w-8 h-8 bg-stone-700/50 hover:bg-stone-600 text-primary rounded-full font-bold transition-colors"
                    >
                      +
                    </button>
                  </div>

                  <Button
                    intent="primary"
                    onPress={handleFeed}
                    className="px-8 py-2 font-medium"
                    isPending={isPending}
                    isDisabled={
                      !selectedKey ||
                      feedAmount < 1 ||
                      feedAmount > maxAvailable
                    }
                  >
                    {({ isPending }) => (
                      <>
                        {isPending ? (
                          <ProgressCircle
                            isIndeterminate
                            aria-label="Feeding..."
                          />
                        ) : (
                          <></>
                        )}
                        {isPending ? "Feeding..." : "Feed"}
                      </>
                    )}
                  </Button>
                </div>
              </div>
              <div className="flex mt-2">
                <Text className="text-sm text-muted-fg italic mt-4">
                  Important: Status effects from cookies don't stack. Only the
                  first cookie applied to each stat will provide its special
                  effect. Any additional cookies will be saved for the upcoming
                  'Chicken Affection' feature!
                </Text>
              </div>
            </div>
          )}

          {/* Cookie Grid */}
          {/* Cookie Grid - Filter out items with zero balance */}

          {Object.entries(foodBalances).filter(
            ([key, amount]) => Number(amount) > 0
          ).length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 px-6 bg-stone-800/50 rounded-lg border border-primary/20">
              <div className="text-6xl mb-4">🍪</div>
              <h3 className="text-xl font-semibold text-primary mb-2">
                No Treats Available
              </h3>
              <p className="text-muted-fg text-center mb-4">
                Your pantry is empty! Craft some delicious cookies.
              </p>
              <Button
                intent="secondary"
                onPress={() => router.push("/crafting")}
                className="px-6 py-2"
              >
                Go Craft Cookies
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
              {Object.entries(foodBalances)
                .filter(([key, amount]) => Number(amount) > 0)
                .map(([key, amount]) => {
                  const item = items.find((i) => i.id === Number(key));
                  const isSelected = selectedKey === key;
                  return (
                    <button
                      type="button"
                      key={key}
                      onClick={() => {
                        setSelectedKey(key);
                        setFeedAmount(1);
                      }}
                      className={`group flex flex-col items-center bg-stone-800/70 p-4 rounded-lg border transition-all duration-500 transform hover:scale-105
                        ${
                          isSelected
                            ? "border-primary shadow-lg scale-105 ring-2 ring-primary/40"
                            : "border-primary/20 hover:border-primary/40"
                        } focus:outline-none`}
                    >
                      <div className="relative mb-3">
                        <div className="absolute inset-0 bg-primary/10 rounded-full animate-pulse opacity-30"></div>
                        <img
                          src={item?.image || "/images/cookie-1.jpg"}
                          alt={item?.name || "Cookie"}
                          className="w-20 h-20 object-contain relative z-10"
                        />
                      </div>
                      <span className="text-primary font-medium text-center">
                        {item?.name || `Cookie #${key}`}
                      </span>
                      {item?.effect && item.effect.length > 0 && (
                        <span className="text-green-400 text-xs text-center mt-1">
                          {item.effect.join(", ")}
                        </span>
                      )}
                      <span className="text-white/70 text-sm mt-1 bg-stone-700/50 px-2 py-0.5 rounded-full">
                        x{amount.toString()}
                      </span>
                    </button>
                  );
                })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
