import Image from "next/image";
import { useState, useEffect } from "react";
import {
  Swords,
  Star,
  Crosshair,
  Shield,
  Zap,
  Activity,
  AlertTriangle,
  Trophy,
  Coins,
  Loader2,
} from "lucide-react";
import useMatchmakingStore from "@/store/match-making";
import useAuthStore from "@/store/auth";
import { useBattleApi, useBattleStats } from "@/features/battle";
import { toast } from "sonner";

// Server URL - in production use environment variable
const SERVER_URL =
  process.env.NEXT_PUBLIC_GAME_SERVER_URL || "ws://localhost:2567";

type ChickenStats = {
  level?: number;
  attack?: number;
  defense?: number;
  speed?: number;
  hp?: number;
  maxHp?: number;
  currentHp?: number;
};

type BattleModalProps = {
  isOpen: boolean;
  onClose: () => void;
  chickenId: string;
  chickenImage?: string;
  chickenStats: ChickenStats;
};

export default function BattleModal({
  isOpen,
  onClose,
  chickenId,
  chickenImage,
  chickenStats,
}: BattleModalProps) {
  // Matchmaking state
  const {
    isConnected,
    isConnecting,
    connectionError,
    isInQueue,
    queuePosition,
    waitingCount,
    matchFound,
    matchCode,
    gameUrl,
    setSelectedFighter,
    setAddress,
    setBattleVerification,
    connectToServer,
    joinMatchmaking,
    leaveMatchmaking,
    resetMatchState,
  } = useMatchmakingStore();

  const { address } = useAuthStore();
  const { initiateBattle, isBattleInProgress } = useBattleApi();

  // Fetch battle stats for win rate calculation
  const { data: battleStats, isLoading: statsLoading } = useBattleStats(
    chickenId ? parseInt(chickenId, 10) : null
  );

  // Local state for UI stages
  const [stage, setStage] = useState<
    "ready" | "verifying" | "connecting" | "queuing" | "matched"
  >("ready");

  // Store verification data
  const [verificationData, setVerificationData] = useState<{
    chickenTokenId: number;
    nonce: number;
    signature: string;
  } | null>(null);

  // Calculate win rate
  const calculateWinRate = () => {
    if (!battleStats || statsLoading) return null;

    const totalBattles =
      (battleStats.wins || 0) +
      (battleStats.losses || 0) +
      (battleStats.draws || 0);
    if (totalBattles === 0) return 0;

    return Math.round(((battleStats.wins || 0) / totalBattles) * 100);
  };

  const winRate = calculateWinRate();

  // Set the selected fighter ID when component mounts
  useEffect(() => {
    if (isOpen && chickenId && address) {
      // Convert chickenId from string to number for the matchmaking system
      setSelectedFighter(parseInt(chickenId, 10));
      setAddress(address);
    }
  }, [isOpen, chickenId, address, setSelectedFighter]);

  // Handle connection and matchmaking flow
  useEffect(() => {
    if (isBattleInProgress) {
      setStage("verifying");
    } else if (isConnecting) {
      setStage("connecting");
    } else if (isInQueue) {
      setStage("queuing");
    } else if (matchFound) {
      setStage("matched");
    }
  }, [isBattleInProgress, isConnecting, isInQueue, matchFound]);

  // Handle match found - redirect to game
  useEffect(() => {
    if (matchFound && matchCode && gameUrl) {
      // Use setTimeout to ensure UI updates before redirect
      const redirectTimer = setTimeout(() => {
        // Store token in localStorage (or could use cookies)

        // Redirect to game
        window.location.href = `${gameUrl}?code=${encodeURIComponent(matchCode)}`;
      }, 1500);

      return () => clearTimeout(redirectTimer);
    }
  }, [matchFound, matchCode, gameUrl]);

  // Cleanup on unmount or close
  useEffect(() => {
    return () => {
      // Cleanup on component unmount
      if (!isOpen) {
        // If user closes modal while in queue, leave the queue
        if (isInQueue) {
          leaveMatchmaking();
        }
        // Reset state
        resetMatchState();
        setStage("ready");
      }
    };
  }, [isOpen, isInQueue, leaveMatchmaking, resetMatchState]);

  // Start battle with Battle API verification
  const handleStartBattle = async () => {
    try {
      // Step 1: Verify ownership through Battle API
      const result = await initiateBattle(parseInt(chickenId, 10));

      if (!result) {
        toast.error("Failed to verify chicken ownership");
        return;
      }

      // Store verification data
      setVerificationData(result.data);

      // Set battle verification data in matchmaking store
      setBattleVerification(result.data.signature, result.data.nonce);

      // Step 2: Connect to server and join matchmaking
      if (!isConnected) {
        await connectToServer(SERVER_URL);
      }

      // Then join matchmaking with verified data
      joinMatchmaking();
    } catch (error: any) {
      console.error("Battle start failed:", error);
      toast.error(error.message || "Failed to start battle");
    }
  };

  const handleCancel = () => {
    // Leave matchmaking if in queue
    if (isInQueue) {
      leaveMatchmaking();
    }

    // Reset state
    resetMatchState();
    setStage("ready");

    // Close modal
    onClose();
  };

  if (!isOpen) return null;

  // Helper function to get HP color based on percentage
  const getHpColor = (current: number = 0, max: number = 100) => {
    const percentage = (current / max) * 100;
    if (percentage > 70) return "bg-green-500";
    if (percentage > 30) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Normalize stats to handle different property names
  const normalizedStats = {
    level: chickenStats.level || 1,
    attack: chickenStats.attack || 0,
    defense: chickenStats.defense || 0,
    speed: chickenStats.speed || 0,
    currentHp: chickenStats.currentHp || chickenStats.hp || 100,
    maxHp: chickenStats.maxHp || 100,
  };

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div
        className="bg-gray-800 border border-gray-700 rounded-lg shadow-xl max-w-md w-full h-2/3 overflow-hidden animate-fadeIn flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="bg-gradient-to-r from-red-900 to-red-800 p-4 border-b border-gray-700 flex items-center">
          <Swords className="h-5 w-5 text-red-400 mr-2" />
          <h3 className="text-xl font-bold text-white">
            {stage === "ready" && "Prepare for Battle"}
            {stage === "verifying" && "Verifying Ownership"}
            {stage === "connecting" && "Connecting to Server"}
            {stage === "queuing" && "Finding Opponent"}
            {stage === "matched" && "Match Found!"}
          </h3>
        </div>

        <div className="p-6 flex-1 overflow-y-auto">
          {connectionError && (
            <div className="bg-red-900/30 border border-red-700/50 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-red-400 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium">Connection Error</p>
                  <p className="text-red-200/80 text-sm mt-1">
                    {connectionError}
                  </p>
                </div>
              </div>
            </div>
          )}
          {stage === "ready" && (
            <>
              <div className="flex flex-col sm:flex-row gap-4 items-center mb-6">
                {/* Chicken Image with Battle-Ready Effect */}
                <div className="relative w-32 h-32 rounded-lg overflow-hidden bg-gray-700 flex-shrink-0 border-2 border-red-600 shadow-[0_0_15px_rgba(220,38,38,0.5)]">
                  {chickenImage ? (
                    <div className="relative w-full h-full">
                      <Image
                        src={chickenImage}
                        alt={`Chicken #${chickenId}`}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-red-900/50 to-transparent"></div>
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <span className="text-gray-500">No image</span>
                    </div>
                  )}
                </div>

                {/* Chicken Stats */}
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-yellow-400 mb-2 flex items-center">
                    Chicken #{chickenId}
                    <span className="ml-2 bg-red-900/50 text-red-300 text-xs px-2 py-0.5 rounded-full border border-red-700/50">
                      Battle Ready
                    </span>
                  </h4>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {/* Level */}
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 mr-1.5" />
                      <span className="text-gray-400 text-sm">Level:</span>
                      <span className="ml-1.5 text-white font-medium">
                        {normalizedStats.level}
                      </span>
                    </div>

                    {/* Attack */}
                    <div className="flex items-center">
                      <Crosshair className="h-4 w-4 text-red-400 mr-1.5" />
                      <span className="text-gray-400 text-sm">ATK:</span>
                      <span className="ml-1.5 text-white font-medium">
                        {normalizedStats.attack.toFixed(0)}
                      </span>
                    </div>

                    {/* Defense */}
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 text-blue-400 mr-1.5" />
                      <span className="text-gray-400 text-sm">DEF:</span>
                      <span className="ml-1.5 text-white font-medium">
                        {normalizedStats.defense.toFixed(0)}
                      </span>
                    </div>

                    {/* Speed */}
                    <div className="flex items-center">
                      <Zap className="h-4 w-4 text-yellow-400 mr-1.5" />
                      <span className="text-gray-400 text-sm">SPD:</span>
                      <span className="ml-1.5 text-white font-medium">
                        {normalizedStats.speed.toFixed(0)}
                      </span>
                    </div>
                  </div>

                  {/* HP Bar */}
                  <div className="mb-1 flex justify-between items-center">
                    <span className="text-gray-400 text-sm flex items-center">
                      <Activity className="h-4 w-4 text-green-400 mr-1.5" />
                      HP:
                    </span>
                    <span className="text-white font-medium text-sm">
                      {normalizedStats.currentHp.toFixed(0)}/
                      {normalizedStats.maxHp.toFixed(0)}
                    </span>
                  </div>

                  <div className="w-full bg-gray-700 rounded-full h-4 relative overflow-hidden">
                    <div
                      className={`${getHpColor(
                        normalizedStats.currentHp,
                        normalizedStats.maxHp
                      )} h-4 rounded-full transition-all duration-500`}
                      style={{
                        width: `${
                          (normalizedStats.currentHp / normalizedStats.maxHp) *
                          100
                        }%`,
                      }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* Battle Record & Win Rate */}
              <div className="bg-gray-700 rounded-lg p-4 mb-6">
                <h5 className="text-white font-medium mb-3 flex items-center">
                  <Trophy className="h-4 w-4 text-yellow-400 mr-1.5" />
                  Battle Record
                </h5>

                {statsLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-5 w-5 text-gray-400 animate-spin mr-2" />
                    <span className="text-gray-400 text-sm">
                      Loading battle stats...
                    </span>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-4">
                    {/* Battle Stats */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Wins:</span>
                        <span className="text-green-400 font-medium">
                          {battleStats?.wins || 0}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Losses:</span>
                        <span className="text-red-400 font-medium">
                          {battleStats?.losses || 0}
                        </span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Draws:</span>
                        <span className="text-yellow-400 font-medium">
                          {battleStats?.draws || 0}
                        </span>
                      </div>
                    </div>

                    {/* Win Rate */}
                    <div className="flex flex-col items-center justify-center">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">
                          {winRate !== null ? `${winRate}%` : "--"}
                        </div>
                        <div className="text-xs text-gray-400 uppercase tracking-wide">
                          Win Rate
                        </div>
                      </div>

                      {/* Win Rate Visual Indicator */}
                      {winRate !== null && (
                        <div className="w-full bg-gray-600 rounded-full h-2 mt-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-500 ${
                              winRate >= 70
                                ? "bg-green-500"
                                : winRate >= 50
                                  ? "bg-yellow-500"
                                  : winRate >= 30
                                    ? "bg-orange-500"
                                    : "bg-red-500"
                            }`}
                            style={{ width: `${winRate}%` }}
                          ></div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Battle Information */}
              <div className="bg-gray-700 rounded-lg p-4 mb-6">
                <h5 className="text-white font-medium mb-2 flex items-center">
                  <Trophy className="h-4 w-4 text-yellow-400 mr-1.5" />
                  Battle Information
                </h5>
                <p className="text-gray-300 text-sm mb-3">
                  Victory will soon earn XP and rewards. Remember that this is
                  an MVP. We would like to get your feedback and contact the
                  team for any bugs and features you want to implement.
                </p>
              </div>
            </>
          )}
          {stage === "verifying" && (
            <div className="py-8">
              <div className="flex flex-col items-center justify-center">
                {/* Loading animation */}
                <div className="relative w-20 h-20 mb-6">
                  <div className="absolute inset-0 rounded-full border-4 border-r-transparent border-blue-500 animate-spin"></div>
                  <div className="absolute inset-3 rounded-full border-4 border-l-transparent border-amber-500 animate-spin animation-delay-150"></div>
                </div>

                <h3 className="text-xl font-bold text-blue-400 mb-3">
                  Verifying Chicken Ownership...
                </h3>

                <p className="text-gray-400 text-sm text-center max-w-xs mb-6">
                  Please sign the message in your wallet to verify ownership of
                  your chicken.
                </p>
              </div>
            </div>
          )}

          {(stage === "connecting" || stage === "queuing") && (
            <div className="py-8">
              <div className="flex flex-col items-center justify-center">
                {/* Loading animation */}
                <div className="relative w-20 h-20 mb-6">
                  <div className="absolute inset-0 rounded-full border-4 border-r-transparent border-red-500 animate-spin"></div>
                  <div className="absolute inset-3 rounded-full border-4 border-l-transparent border-amber-500 animate-spin animation-delay-150"></div>
                </div>

                <h3 className="text-xl font-bold text-amber-400 mb-3">
                  {stage === "connecting"
                    ? "Connecting to Server..."
                    : "Finding an Opponent..."}
                </h3>

                {stage === "queuing" && (
                  <div className="text-center mb-6">
                    <p className="text-gray-300">
                      Position in queue:{" "}
                      <span className="text-amber-400 font-medium">
                        {queuePosition}
                      </span>
                    </p>
                    <p className="text-gray-400 text-sm mt-1">
                      {waitingCount} {waitingCount === 1 ? "player" : "players"}{" "}
                      waiting
                    </p>
                  </div>
                )}

                <p className="text-gray-400 text-sm text-center max-w-xs mb-6">
                  {stage === "connecting"
                    ? "Establishing connection to the battle server. Please wait a moment."
                    : "We're matching you with an opponent of similar strength. This may take a few moments."}
                </p>
              </div>
            </div>
          )}
          {stage === "matched" && (
            <div className="py-8">
              <div className="flex flex-col items-center justify-center">
                {/* Success animation */}
                <div className="w-20 h-20 mb-6 relative">
                  <div className="absolute inset-0 bg-green-500 rounded-full animate-ping opacity-50"></div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Trophy className="h-10 w-10 text-yellow-400" />
                  </div>
                </div>

                <h3 className="text-xl font-bold text-green-400 mb-3">
                  Match Found!
                </h3>

                <p className="text-gray-300 text-center max-w-xs mb-8">
                  An opponent has been found. Redirecting you to the battlefield
                  in a moment...
                </p>

                <div className="flex items-center">
                  <Loader2 className="h-5 w-5 text-amber-400 animate-spin mr-2" />
                  <span className="text-amber-400">
                    Redirecting to battle...
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>

        {(stage === "ready" ||
          stage === "verifying" ||
          stage === "connecting" ||
          stage === "queuing") && (
          <div className="p-6 border-t border-gray-700 bg-gray-800 mt-auto">
            <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
              <button
                className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                onClick={handleCancel}
              >
                Cancel
              </button>
              {stage === "ready" && (
                <button
                  className="px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 transition-colors flex items-center justify-center shadow-lg shadow-red-900/30"
                  onClick={handleStartBattle}
                  disabled={isBattleInProgress}
                >
                  {isBattleInProgress ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <Swords className="h-4 w-4 mr-2" />
                      Enter Battle
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
