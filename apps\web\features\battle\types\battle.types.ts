// Battle API Request/Response Types
export interface IBattleRequestPayload {
  chickenTokenId: number;
}

export interface IBattleRequestResponse {
  status: number;
  message: string;
  data: string; // The message to sign
}

export interface IBattleVerifyPayload {
  chickenTokenId: number;
  signature: string;
}

export interface IBattleVerifyResponse {
  status: number;
  message: string;
  data: {
    chickenTokenId: number;
    nonce: number;
    signature: string;
  };
}

// Battle Stats Types (for game integration)
export interface IBattleStats {
  wins: number;
  losses: number;
  draws?: number;
  level?: number;
  mmr?: number;
  state?: "normal" | "faint" | "dead" | "breeding";
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    maxHp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

// Battle Modal Props - removed (using original BattleModal component)

// Battle Flow States
export type BattleFlowState =
  | "idle"
  | "requesting"
  | "signing"
  | "verifying"
  | "verified"
  | "error";

// Game Server Integration Types
export interface IGameServerConfig {
  serverUrl: string;
  fighterId: string;
  address: string;
  signature: string;
  nonce: number;
}

// Matchmaking Types
export interface IMatchmakingState {
  isConnected: boolean;
  isConnecting: boolean;
  connectionError?: string;
  isInQueue: boolean;
  queuePosition?: number;
  waitingCount?: number;
  matchFound: boolean;
  matchCode?: string;
  gameUrl?: string;
}

// Battle History Types
export interface IBattleHistoryEntry {
  id: string;
  chickenTokenId: number;
  opponentTokenId?: number;
  result: "win" | "loss" | "draw";
  date: string;
  mmrChange?: number;
  battleType?: "ranked" | "casual";
}

// Error Types
export interface IBattleError {
  code: string;
  message: string;
  details?: any;
}
