"use client";

import { cn } from "@/utils/classes";
import { Home, Users } from "lucide-react";

export type InventoryTabType = "owned" | "delegated-out";

export interface IInventoryTabsProps {
  activeTab: InventoryTabType;
  onTabChange: (tab: InventoryTabType) => void;
  ownedCount?: number;
  delegatedOutCount?: number;
  className?: string;
}

export function InventoryTabs({
  activeTab,
  onTabChange,
  ownedCount = 0,
  delegatedOutCount = 0,
  className,
}: IInventoryTabsProps) {
  const tabs = [
    {
      id: "owned" as InventoryTabType,
      label: "My Chickens",
      icon: <Home className="w-4 h-4" />,
      count: ownedCount,
    },
    {
      id: "delegated-out" as InventoryTabType,
      label: "Delegated to others",
      icon: <Users className="w-4 h-4" />,
      count: delegatedOutCount,
    },
  ];

  return (
    <div className={cn("flex border-b border-stone-700", className)}>
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;

        return (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "flex items-center gap-2 px-4 py-3 text-sm font-medium transition-colors relative",
              "hover:text-white",
              isActive
                ? "text-yellow-400 border-b-2 border-yellow-400"
                : "text-gray-400"
            )}
          >
            {tab.icon}
            <span>{tab.label}</span>
            {tab.count > 0 && (
              <span
                className={cn(
                  "ml-1 px-2 py-0.5 text-xs rounded-full",
                  isActive
                    ? "bg-yellow-400/20 text-yellow-400"
                    : "bg-stone-700 text-gray-300"
                )}
              >
                {tab.count}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
}
