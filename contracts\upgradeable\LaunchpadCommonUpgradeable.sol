// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "./LaunchpadPresetUpgradeable.sol";

abstract contract LaunchpadCommonUpgradeable is LaunchpadPresetUpgradeable {
    error ErrPhaseNotStarted();
    error ErrPhaseAlreadyEnded();

    /// @dev Emitted when ETH is released in an emergency
    event EmergencyETHRelease(address indexed to, uint256 amount);

    /// @dev Emitted when a sales phase is configured.
    event PhaseConfigured(
        PhaseType indexed phase,
        uint256 startTime,
        uint256 endTime,
        uint256 price,
        uint256 erc1155Amount,
        uint256 erc20Amount
    );

    /// @dev Emitted when a batch of addresses is whitelisted.
    event WhitelistBatchConfigured(
        PhaseType indexed phase,
        uint256 numberOfAddresses
    );

    /// @dev Emitted when tokens are minted.
    event TokensMinted(address indexed user, uint256 amount, PhaseType phase);

    /// @dev Different phases of the token sale.
    enum PhaseType {
        LEGENDARY,
        GENESIS_DELEGATEE,
        MYSTIC_AXIES,
        PUBLIC_MINT
    }

    /// @dev Structure representing a sales phase configuration.
    struct Phase {
        uint256 startTime;
        uint256 endTime;
        uint256 price;
        uint256 erc1155Amount;
        uint256 erc20Amount;
    }

    /// @dev Mapping from sales phase to its configuration.
    mapping(PhaseType => Phase) private phases;

    /// @dev Whitelist allocation for each user in each phase.
    mapping(address => mapping(PhaseType => uint256))
        private whitelistAllocation;

    /// @dev Number of tokens minted by a user per phase.
    mapping(address => mapping(PhaseType => uint256)) private minted;

    /// @dev Total supply of tokens available.
    uint256 public constant TOTAL_SUPPLY = 8888;

    /// @dev Global counter tracking the total minted tokens.
    uint256 public totalMinted;

    /**
     * @dev Reserved storage gap to allow for future additions.
     */
    uint256[50] private __gap;

    /**
     * @notice Configures a sales phase.
     * @dev Sets the phase configuration for minting tokens.
     * @param phaseType The phase type to configure.
     * @param startTime The timestamp when the phase starts.
     * @param endTime The timestamp when the phase ends.
     * @param price The price per token in this phase.
     * @param erc1155Amount The amount of ERC1155 tokens required per mint.
     * @param erc20Amount The amount of ERC20 tokens required per mint.
     */
    function configurePhase(
        PhaseType phaseType,
        uint256 startTime,
        uint256 endTime,
        uint256 price,
        uint256 erc1155Amount,
        uint256 erc20Amount
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(startTime < endTime, "Invalid time range");
        require(price > 0, "Invalid price");

        phases[phaseType] = Phase({
            startTime: startTime,
            endTime: endTime,
            price: price,
            erc1155Amount: erc1155Amount,
            erc20Amount: erc20Amount
        });

        emit PhaseConfigured(
            phaseType,
            startTime,
            endTime,
            price,
            erc1155Amount,
            erc20Amount
        );
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Configures a whitelist batch for a specific phase.
     * @dev Updates whitelist allocations for multiple users at once.
     * @param phase The phase for which the whitelist is applied (except for PUBLIC_MINT).
     * @param users Array of user addresses to be whitelisted.
     * @param amounts Array of amounts corresponding to each user.
     */
    function configureWhitelistBatch(
        PhaseType phase,
        address[] calldata users,
        uint256[] calldata amounts
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(users.length == amounts.length, "Invalid input lengths");
        require(
            phase != PhaseType.PUBLIC_MINT,
            "Cannot whitelist for public phase"
        );

        uint256 totalAllocation;
        for (uint256 i = 0; i < users.length; i++) {
            require(users[i] != address(0), "Invalid address");
            require(amounts[i] > 0, "Invalid amount");
            totalAllocation += amounts[i];
            whitelistAllocation[users[i]][phase] = amounts[i];
        }

        require(
            totalAllocation + totalMinted <= TOTAL_SUPPLY,
            "Exceeds total supply"
        );
        emit WhitelistBatchConfigured(phase, users.length);
    }

    /**
     * @notice Updates the whitelist allocation for a specific user.
     * @dev Allows the admin to modify a user's allocation (except for PUBLIC_MINT).
     * @param phase The phase for which the allocation is updated.
     * @param user The address of the user.
     * @param amount The new allocation amount.
     */
    function updateWhitelistAllocation(
        PhaseType phase,
        address user,
        uint256 amount
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(
            phase != PhaseType.PUBLIC_MINT,
            "Cannot whitelist for public phase"
        );
        require(user != address(0), "Invalid address");

        whitelistAllocation[user][phase] = amount;
    }

    /**
     * @notice Mints new tokens.
     * @dev Handles the minting process including verifications of phase timeframes, whitelist allocations,
     * transferring ETH to the fee wallet, burning required ERC1155 tokens, and transferring ERC20 tokens.
     * @param amount The number of tokens to mint.
     * @param phase The phase during which minting occurs.
     */
    function mint(
        uint256 amount,
        PhaseType phase
    ) external payable nonReentrant whenNotPaused {
        address sender = _msgSender();
        Phase storage currentPhase = phases[phase];
        require(currentPhase.startTime != 0, "Phase not configured");
        require(
            block.timestamp >= currentPhase.startTime,
            ErrPhaseNotStarted()
        );

        if (phase != PhaseType.PUBLIC_MINT) {
            require(
                block.timestamp <= currentPhase.endTime,
                ErrPhaseAlreadyEnded()
            );
            require(
                whitelistAllocation[sender][phase] >= amount,
                "Exceeds allocation"
            );
            whitelistAllocation[sender][phase] -= amount;
        } else {
            // For public phase, check the per-address limit
            require(
                block.timestamp <= currentPhase.endTime,
                ErrPhaseAlreadyEnded()
            );
            require(
                minted[sender][phase] + amount <= 3,
                "Exceeds public mint limit"
            );
        }

        require(totalMinted + amount <= TOTAL_SUPPLY, "Exceeds total supply");
        uint256 paymentAmount = currentPhase.price * amount;
        require(msg.value == paymentAmount, "Incorrect payment amount");

        // Rest of the function remains the same...
        (bool success, ) = payable(feeWallet).call{value: paymentAmount}("");
        require(success, "Payment failed");

        require(
            COCK.transferFrom(
                sender,
                feeWallet,
                currentPhase.erc20Amount * amount
            ),
            "ERC20 transfer failed"
        );

        FEATHERS.burn(sender, 1, currentPhase.erc1155Amount * amount);

        minted[sender][phase] += amount;
        totalMinted += amount;

        LEGACY.mintLaunchpad(sender, amount, "");

        emit TokensMinted(sender, amount, phase);
    }

    /**
     * @notice Retrieves phase configuration details.
     * @dev Allows external callers to get the configuration of a phase.
     * @param phase The phase to query.
     * @return The Phase structure containing configuration.
     */
    function getPhaseInfo(
        PhaseType phase
    ) external view returns (Phase memory) {
        return phases[phase];
    }

    /**
     * @notice Retrieves whitelist allocation for a specific user and phase.
     * @dev Returns the allocated minting amount the user is allowed.
     * @param user The address of the user.
     * @param phase The phase to query.
     * @return The allocation amount.
     */
    function getUserAllocation(
        address user,
        PhaseType phase
    ) external view returns (uint256) {
        return whitelistAllocation[user][phase];
    }

    /**
     * @notice Retrieves the minted token count for a user in a specific phase.
     * @dev Returns the number of tokens minted by the user in the given phase.
     * @param user The address of the user.
     * @param phase The phase to query.
     * @return The minted amount.
     */
    function getUserMinted(
        address user,
        PhaseType phase
    ) external view returns (uint256) {
        return minted[user][phase];
    }

    /**
     * @notice Returns the number of tokens a user can still mint in the public phase
     * @param user The address of the user
     * @return The remaining number of tokens the user can mint
     */
    function getRemainingPublicMintAllocation(
        address user
    ) external view returns (uint256) {
        uint256 alreadyMinted = minted[user][PhaseType.PUBLIC_MINT];
        if (alreadyMinted >= 3) {
            return 0;
        }
        return 3 - alreadyMinted;
    }

    /**
     * @notice Allows admin to withdraw ETH in case of emergency
     * @dev Only callable by admin role, transfers all contract ETH balance
     * @param to Address to send the ETH to
     */
    function emergencyReleaseETH(
        address to
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(to != address(0), "Invalid address");

        uint256 balance = address(this).balance;
        require(balance > 0, "No ETH to release");

        (bool success, ) = payable(to).call{value: balance}("");
        require(success, "ETH transfer failed");

        emit EmergencyETHRelease(to, balance);
    }

    /**
     * @notice Receives ETH.
     */
    receive() external payable {}
}
