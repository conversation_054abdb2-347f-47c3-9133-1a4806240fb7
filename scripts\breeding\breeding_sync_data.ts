import { ethers } from "hardhat";
import abi from "./abi.json";
import chalk from "chalk";

// Helper function to create progress bar
function createProgressBar(
  current: number,
  total: number,
  length: number = 40
): string {
  const percentage = current / total;
  const filled = Math.round(length * percentage);
  const empty = length - filled;
  const bar = "█".repeat(filled) + "░".repeat(empty);
  return `${bar} ${Math.round(percentage * 100)}%`;
}

// Helper function to format numbers with commas
function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function printMintingStats(
  tokenType: string,
  current: number,
  total: number,
  batchSize: number,
  batchNumber: number,
  totalBatches: number,
  startTime: number
) {
  const elapsedTime = (Date.now() - startTime) / 1000; // in seconds
  const avgTimePerBatch = elapsedTime / batchNumber;
  const remainingBatches = totalBatches - batchNumber;
  const estimatedTimeRemaining = remainingBatches * avgTimePerBatch;

  console.clear();
  console.log(chalk.cyan(`\n🪙 ${tokenType} Syncing Progress:`));
  console.log(chalk.white(createProgressBar(current, total)));
  console.log(chalk.yellow("\n📊 Statistics:"));
  console.log(
    chalk.white(`• Synced: ${formatNumber(current)} / ${formatNumber(total)}`)
  );
  console.log(chalk.white(`• Current Batch: ${batchNumber} / ${totalBatches}`));
  console.log(chalk.white(`• Batch Size: ${formatNumber(batchSize)}`));
  console.log(chalk.white(`• Time Elapsed: ${elapsedTime.toFixed(1)}s`));
  console.log(
    chalk.white(
      `• Estimated Time Remaining: ${estimatedTimeRemaining.toFixed(1)}s`
    )
  );
  console.log(
    chalk.white(`• Average Time per Batch: ${avgTimePerBatch.toFixed(1)}s`)
  );
}

let breeding_cooldown_token_ids = [
  6678, 5105, 2489, 3673, 7999, 235, 6494, 5697, 5698, 2518, 6386, 3033, 2781,
  1004, 815, 10486, 433, 392, 10325, 7805, 6894, 7803, 11048, 10324, 3913, 6890,
  2692, 3011, 8210, 2783, 2406, 7498, 2534, 7529, 6824, 7009, 2403, 3914, 1237,
  7525, 3915, 10365, 2698, 7523, 10364, 10363, 2418, 715, 955, 7938, 6859, 50,
  3023, 3030, 3031, 3024, 4339, 3945, 3002, 2398, 1476, 3029, 10782, 2073,
  10805, 3886, 27, 398, 7345, 2261, 2397, 452, 2966, 10460, 4361, 2126, 6875,
  7758, 424, 2588, 463, 346, 6872, 5149, 9293, 3885, 2260, 6695, 9210, 3325,
  6293, 4240, 9213, 3642, 5037, 1363, 111, 10443, 8767, 56, 24, 8766, 6284,
  1232, 10461, 9318, 3685, 40, 3477, 9925, 9114, 7750, 3358, 10557, 9471, 4940,
  8522, 8528, 10960, 10267, 2236, 2790, 10350, 10348, 1485, 4321, 4360, 2517,
  1630, 1094, 2872, 1507, 4147, 2870, 4698, 1279, 10438, 9319, 9295, 5252, 6826,
  2784, 8213, 8245, 10652, 7961, 1589, 2511, 5126, 7893, 7895, 7896, 9910, 6598,
  9066, 3680, 3663, 6254, 8225, 8290, 10488, 516, 3020, 6304, 7275, 7032, 11046,
  4704, 2459, 9031, 8995, 191, 2452, 4326, 2461, 3166, 10439, 8512, 73, 1961,
  2695, 1376, 9119, 6856, 11070, 7786, 760, 5132, 4051, 4536, 5289, 5322, 5650,
  9015, 2342, 8303, 3839, 3840, 1597, 88, 1233, 2347, 1815, 2508, 7149, 7177,
  4067, 4088, 8071, 4530, 2445, 7280, 32, 9242, 6436, 4666, 6618, 7039, 7018,
  3164, 9352, 5678, 7387, 9475, 77, 10754, 3835, 7281, 6153, 6154, 8052, 162,
  8788, 2286, 10723, 8455, 9642, 387, 5048, 1448, 3088, 1945, 3036, 10773, 3037,
  5594, 2302, 4396, 8779, 8806, 8810, 4402, 4414, 4415, 4412, 9243, 8817, 4206,
  10277, 2926, 1542, 8407, 430, 2503, 2981, 2602, 1557, 2803, 4964, 5661, 5660,
  6159, 6161, 253, 339, 8459, 9104, 9038, 10433, 6839, 2495, 7959, 8202, 959,
  9105, 5120, 5118, 1939, 3025, 8518, 4846, 4317, 8479, 2890, 5614, 9299, 6158,
  2922, 4978, 10759, 1782, 4177, 4931, 7204, 7214, 10752, 7210, 2924, 2921,
  7207, 2925, 7206, 7218, 1424, 29, 1748, 7208, 10750, 7038, 6650, 5013, 10580,
  2682, 2680, 3380, 3381, 8094, 3375, 3376, 614, 8669, 2467, 3383, 1479, 292,
  8368, 2712, 2713, 10715, 2218, 6897, 10444, 6311, 9470, 11016, 3028, 8337,
  730, 10424, 935, 113, 6608, 7360, 1649, 2083, 675, 6558, 6562, 7001, 8530,
  10385, 7367, 10870, 6530, 11106, 1379, 7185, 6607, 7261, 5602, 5267, 7688,
  1303, 4221, 7997, 10539, 4831, 2474, 1444, 7022, 6302, 632, 1772, 3787, 8190,
  8758, 8756, 8739, 8738, 3804, 3771, 1057, 3785, 3783, 7278, 8761, 3815, 4880,
  3744, 224, 2109, 3775, 1627, 7501, 2524, 7817, 2521, 2520, 2522, 2530, 2523,
  2529, 2526, 6364, 1058, 7536, 6395, 6397, 6400, 7947, 6396, 6398, 6403, 8651,
  4092, 7889, 7945, 6399, 7816, 7946, 7890, 7891, 7815, 7534, 6409, 1581, 7682,
  6407, 5038, 5039, 6529, 10755, 5145, 7661, 4444, 4461, 4466, 7660, 2775, 93,
  7602, 832, 6262, 6270, 10609, 2227, 1246, 9740, 9741, 6854, 374, 2225, 1137,
  1453, 9353, 10520, 9283, 9282, 1025, 1861, 2679, 567, 507, 9591, 1563, 621,
  1738, 2022, 3567, 2114, 1353, 6495, 554, 7467, 2711, 4220, 1527, 7535, 6385,
  2974, 6764, 6503, 4785, 239, 267, 1879, 335, 7425, 4845, 10823, 4755, 8193,
  10598, 6654, 446, 8454, 6822, 553, 4961, 7197, 10720, 9440, 1638, 2425, 2597,
  9109, 2596, 9126, 10471, 6659, 10915, 731, 2935, 7391, 8234, 8405, 466, 5515,
  6778, 7684, 137, 1610, 3515, 7826, 7823, 8713, 10422, 10459, 9324, 9312, 9327,
  9306, 9296, 9298, 9292, 3683, 3681, 3676, 3668, 3660, 3133, 3649, 702, 474,
  133, 6187, 1245, 1302, 1840, 2167, 2187, 3070, 9783, 9749, 1960, 7555, 4660,
  1383, 9141, 4659, 3505, 3012, 4780, 7538, 9477, 6527, 7346, 389, 10706, 10493,
  399, 10677, 10679, 5032, 5029, 5027, 5026, 11100, 223, 2301, 10669, 10671,
  10716, 10735, 10259, 3901, 10428, 1982, 727, 4679, 4689, 4687, 4680, 4686,
  4690, 4676, 4691, 8674, 8659, 10924, 6250, 7182, 11084, 3068, 3087, 3055,
  3083, 3086, 3084, 1293, 10430, 6184, 6767, 2402, 2408, 2410, 2413, 2415, 2417,
  2421, 2423, 10361, 8205, 2089, 305, 216, 379, 1857, 7739, 9463, 2349, 10603,
  6205, 5402, 5445, 5469, 5476, 5391, 5425, 5401, 5418, 5457, 5481, 5474, 2708,
  10711, 10730, 5410, 5417, 5444, 5454, 5448, 5395, 5467, 5421, 6602, 5393, 61,
  163, 421, 584, 664, 1249, 1251, 1253, 1255, 1257, 5471, 1259, 1442, 1538,
  1818, 2185, 5163, 5390, 5394, 5398, 5403, 5455, 5405, 5406, 5409, 5412, 5414,
  5419, 5422, 5428, 5430, 5433, 5435, 5437, 5439, 5442, 5446, 5451, 5456, 5460,
  5462, 5465, 5468, 5475, 5479, 5484, 5486, 5488, 10683, 5490, 5494, 5496, 5498,
  5622, 6236, 7875, 10709, 7877, 10339, 10684, 10877, 148, 401, 1203, 1205,
  1207, 1387, 1463, 1636, 2228, 2305, 2307, 3004, 3019, 4788, 4844, 4859, 4974,
  5150, 5174, 11052, 5381, 5388, 5638, 6174, 6176, 6225, 6550, 5378, 6710, 7837,
  11050, 10983, 10913, 10911, 10846, 10982, 6718, 6724, 6810, 7190, 10429, 7500,
  8180, 8439, 9263, 9491, 9492, 9495, 9497, 9502, 10382, 9500, 9504, 9506, 9507,
  9508, 9509, 9511, 9517, 9519, 10245, 9515, 9521, 9523, 9525, 9527, 9529, 4803,
  6876, 264, 1983, 313, 1565, 5114, 2887, 10415, 5051, 10878, 6328, 3847, 4272,
  10521, 990, 9803, 9825, 1544, 231, 8107, 9538, 6811, 6494, 8210, 9533, 1809,
  3365, 9540, 7192, 9534, 4947, 9536, 10923, 4988, 4769, 7012, 5620, 7964, 9595,
  7961, 6700, 8450, 6705, 5162, 649, 4366, 11012, 10587, 9302, 9301, 11010,
  10615, 7434, 198, 6424, 6386, 2286, 10723, 8455, 73, 1233, 2441, 2442, 7282,
  6459, 5270, 6539, 6244, 1351, 6518, 2922, 7204, 7207, 7200, 7210, 7218, 10752,
  7206, 7202, 7201, 7177, 7208, 10751, 7217, 520, 330, 455, 460, 442, 614, 8669,
  339, 32, 7686, 588, 7029, 7030, 3839, 1492, 8448, 5327, 2901, 387, 5048, 2965,
  4880, 1451, 7997, 1589, 2512, 1788, 2157, 9654, 9656, 1378, 556, 675, 10388,
  2917, 6859, 3031, 5249, 3030, 3018, 3023, 3974, 3013, 3024, 50, 3163, 1476,
  398, 27, 7284, 3756, 3750, 3744, 3787, 3785, 3784, 3783, 3775, 3777, 3770,
  3806, 3825, 8739, 8738, 955, 9925, 7462, 6321, 6320, 2397, 6294, 3025, 8627,
  5697, 5699, 8466, 4766, 6050, 6048, 6047, 29, 5132, 6826, 2218, 7269, 9470,
  4174, 6898, 9468, 9910, 1448, 224, 7688, 643, 3994, 2533, 4393, 8289, 2455,
  8291, 2454, 88, 7149, 5593, 6777, 3033, 3035, 3036, 2926, 2503, 2981, 2501,
  2803, 615, 179, 1849, 1349, 2302, 8234, 8405, 2713, 2712, 10783, 11073, 6495,
  7346, 389, 10719, 1982, 7529, 7527, 6818, 8141, 4156, 1145, 1369, 1548, 1616,
  4252, 8274, 6854, 2109, 4444, 4461, 4466, 8225, 4514, 7602, 4482, 1422, 6099,
  6095, 6104, 6100, 6098, 37, 2880, 2890, 8459, 2900, 2882, 8110, 1777, 2894,
  2885, 2896, 7467, 2886, 2889, 2884, 8112, 2888, 8113, 10415, 992, 4460, 3542,
  4964, 5661, 5660, 6159, 6160, 6158, 253, 5177, 10773, 10485, 4322, 1201,
  10359, 1442, 5829, 4263, 4259, 4262, 9389, 4260, 1279, 8326, 5324, 1879, 853,
  6608, 6605, 4445, 4448, 6602, 6105, 9096, 1795, 187, 6321, 6322, 6323, 10260,
  10424, 5965, 10422, 4491, 4438, 6518, 1233, 1008, 2286, 9149, 251, 4288, 4302,
  9538, 939, 7293, 7292, 1523, 3018, 10853, 8008, 8004, 8016, 8014, 8048, 8005,
  1059, 4092, 1145, 1369, 1548, 1616, 274, 10348, 5515, 5512, 4433, 4462, 4467,
  4479, 4441, 4504, 4489, 4435, 9223, 9224, 247, 7149, 4396, 8779, 8806, 8810,
  4402, 4414, 4415, 4412, 9243, 8817, 7772, 8103, 9172, 10460, 10789, 9293,
  3672, 2934, 10626, 9296, 3675, 3677, 10459, 10805, 9319, 9618, 3570, 2370,
  8788, 4398, 8812, 4416, 4418, 8772, 4419, 8785, 8804, 4403, 8825, 11008, 8777,
  8784, 8800, 8818, 8811, 8780, 4410, 8782, 1846, 4417, 1934, 453, 957, 3503,
  3561, 5046, 6366, 3866, 2427, 6811, 6859, 7937, 1585, 1476, 27, 6420, 7396,
  2970, 9269, 196, 1303, 37, 10417, 1338, 1544, 1990, 1378, 675, 4459, 4507,
  4510, 4515, 4512, 4516, 4316, 9063, 1383, 7463, 5742, 1601, 11111, 6130, 9214,
  7599, 4464, 7593, 9579, 11150, 4460, 4477, 4448, 4483, 4491, 4438, 4433,
  11125, 7581, 11371, 8684, 6618, 7771, 643, 9223, 9224, 247, 10348, 3022, 7764,
  374, 1309, 4799, 6184, 4560, 11257, 11564, 11105, 1769, 7382, 4486, 4488,
  4462, 4467, 4479, 7619, 7580, 4518, 7598, 11293, 1376, 2342, 3031, 1442,
  11179, 11164, 11167, 11169, 11178, 11172, 11173, 246, 2987, 11766, 11757,
  11805, 11775, 11834, 11839, 11836, 11755, 11764, 11786, 11818, 11759, 11760,
  11795, 11769, 11767, 11773, 11781, 11776, 11780, 11783, 11787, 11793, 11810,
  11799, 11807, 11811, 11814, 11816, 11784, 11762, 11824, 11765, 11832, 11831,
  11820, 11771, 11788, 11790, 11801, 11797, 11897, 11876, 11847, 11850, 11896,
  11887, 11900, 11884, 11870, 11841, 11905, 11906, 11903, 11895, 11849, 11845,
  11842, 11894, 11840, 11852, 11860, 11867, 11853, 11856, 11861, 11862, 11874,
  11398, 11875, 11877, 11879, 11881, 11882, 11891, 9783, 5249, 11917, 9784,
  9749, 9825, 11924, 669, 11702, 8043, 2403, 7609, 7611, 4487, 7572, 4485, 7584,
  7591, 4442, 4504, 4489, 7189, 11111, 3024, 11298, 11318, 652, 11203, 1559,
  4435, 4459, 4508, 7641, 7631, 7629, 7633, 7643, 7638, 7649, 7653, 7656, 7658,
  7627, 7652, 7655, 7828, 11738, 11555, 11556, 11558, 7626, 7646, 7644, 7663,
  7648, 7601, 7621, 7624, 7606, 7615, 7604, 7607, 7590, 7589, 7594, 7595, 7586,
  7577, 4511, 7565, 7569, 4505, 7567, 4509, 7570, 4498, 4437, 4436, 4447, 4443,
  4450, 4456, 4452, 4472, 4478, 4457, 4473, 2199, 11354, 8244, 11172, 11667,
  7573, 7571, 7574, 7568, 7599, 7579, 7596, 7581, 4493, 4471, 7619, 7580, 4518,
  7598, 11262, 1232, 7611, 4487, 4500, 11960, 11958, 11952, 11954, 11345, 12030,
  12027, 6678, 11400, 6494, 7572, 7609, 4485, 7584, 7617, 7641, 7631, 7629,
  7645, 7643, 7650, 7649, 7653, 7657, 7658, 7627, 7652, 7662, 7828, 7664, 7646,
  7644, 7663, 7654, 7178, 11373, 8367, 292, 1991, 746, 11951, 11942, 11949,
  11940, 11936, 11270, 7601, 7621, 7624, 7614, 7605, 7604, 7603, 7576, 7589,
  7594, 7587, 7583, 7585, 11435, 12132, 11430, 241, 4298, 1561, 4511, 11425,
  1301, 11111, 11162, 3016, 5249, 12029, 12032, 10378, 247, 7178, 3022, 12371,
  2062, 11947, 12480, 12478, 12449, 12451, 12452, 12454, 12457, 12459, 12461,
  12463, 12465, 12468, 12470, 12476, 12473, 12475, 12447, 12483, 12446, 12444,
  12441, 12433, 12439, 12443, 12436, 12431, 12429, 12426, 12424, 12422, 12420,
  12418, 12416, 12415, 12412, 12410, 12414, 12407, 12225, 1975, 12242, 7366,
  1383, 1088, 6862, 11683, 11676, 9783, 9749, 9803, 9815, 10348, 12485, 12481,
  11923, 1301, 2073, 12654, 12636, 12633, 11951, 11948, 11940, 4964, 11380,
  11381, 11383, 5662, 6161, 11385, 11408, 11982, 10706, 9475, 6120, 12180, 9538,
  6811, 5186, 6424, 3134, 8238, 592, 10665, 5701, 5699, 2515, 6366, 3034, 2780,
  2911, 10928, 6825, 6516, 5271, 10523, 7806, 7801, 10323, 7802, 7804, 3917,
  7167, 2691, 10487, 10590, 2782, 2404, 9489, 2538, 7528, 6660, 7008, 2414,
  3916, 2652, 7527, 7940, 2696, 2693, 2700, 2697, 7392, 2420, 1829, 1285, 5249,
  3016, 3018, 3006, 3021, 3009, 3027, 3163, 3974, 3013, 2401, 2208, 3001, 10783,
  2258, 10626, 10451, 203, 633, 7428, 2259, 2399, 8379, 2967, 3672, 10312, 4154,
  6874, 8707, 6479, 7469, 6749, 6873, 9942, 6561, 9329, 3643, 2262, 10272, 9211,
  9594, 10680, 11019, 6531, 3641, 10745, 1362, 1435, 10537, 8768, 3884, 7161,
  8769, 6296, 7854, 9323, 9313, 9308, 1329, 10962, 6801, 10632, 7753, 2877,
  6393, 5251, 4941, 8524, 10961, 8523, 7761, 2235, 2792, 2791, 2793, 1964, 6357,
  6704, 2519, 4708, 7180, 42, 7028, 4148, 2871, 4700, 9487, 2716, 9297, 3675,
  7195, 6827, 8211, 10655, 8247, 10653, 7962, 2512, 2513, 7559, 7897, 7892,
  7894, 9933, 6532, 9067, 3650, 3674, 7006, 8226, 2453, 10748, 110, 8487, 6526,
  7276, 7030, 616, 4706, 2451, 10747, 7029, 3167, 2460, 3165, 8336, 3168, 10440,
  8526, 2443, 2442, 2694, 2341, 10564, 10561, 4527, 4533, 1367, 11085, 4372,
  5147, 2434, 5649, 5651, 9926, 2344, 8313, 6376, 6375, 9943, 6441, 2441, 9669,
  693, 9013, 7150, 7178, 4529, 4526, 4534, 4532, 2444, 7026, 419, 10574, 8212,
  4667, 6619, 10322, 6769, 7457, 6831, 9280, 10377, 7366, 274, 10972, 8383,
  7284, 6157, 6155, 8051, 933, 8812, 5203, 3403, 10644, 9643, 639, 5385, 255,
  7910, 1769, 3035, 2313, 8000, 1474, 9696, 4409, 4400, 10694, 4413, 8791, 9447,
  4404, 10859, 7537, 4421, 2303, 3407, 2927, 2095, 8453, 1351, 2501, 2802, 2976,
  615, 2601, 5663, 5665, 5662, 6162, 6160, 1331, 3143, 10534, 242, 10432, 10434,
  6841, 10518, 8204, 10292, 9107, 10388, 5116, 5119, 5117, 3032, 10310, 4929,
  8246, 8829, 9676, 7885, 5033, 10651, 2923, 4890, 6766, 31, 7353, 4930, 7217,
  7215, 7212, 7200, 7199, 7205, 7203, 7219, 7201, 7202, 7686, 2363, 9448, 7209,
  10751, 6518, 6793, 9214, 10579, 2688, 2686, 3384, 8686, 8092, 3379, 3382,
  3402, 7271, 7708, 8687, 8287, 8367, 10798, 2714, 2715, 7446, 4174, 6898, 9468,
  7269, 8176, 6238, 10445, 10331, 155, 9875, 8608, 7359, 6606, 7363, 1378, 556,
  2164, 7004, 7000, 8521, 8590, 10386, 10462, 10872, 10387, 11107, 853, 7186,
  6605, 10806, 5604, 7414, 7777, 6502, 4236, 251, 9265, 4972, 2471, 4932, 7024,
  9986, 7861, 8208, 8729, 9841, 3756, 3825, 8731, 8736, 3784, 3777, 8652, 3797,
  3803, 7279, 3750, 3806, 8647, 8753, 312, 8073, 3767, 7697, 8177, 2527, 7814,
  6401, 2531, 2525, 10691, 2528, 7532, 6394, 6363, 8648, 7887, 10690, 10692,
  6405, 6411, 6404, 6402, 6406, 8649, 7771, 7888, 7453, 7681, 7818, 7683, 6408,
  7455, 7679, 6413, 6410, 5041, 7533, 6412, 5047, 5040, 9590, 7789, 5146, 4482,
  4454, 4474, 4476, 4514, 10999, 5581, 7829, 629, 6264, 6352, 7400, 10591, 9739,
  9746, 9743, 6855, 1594, 2224, 9286, 3240, 3534, 9087, 9284, 9285, 1022, 8482,
  2684, 6833, 1234, 9593, 5648, 7726, 7707, 5376, 7699, 1414, 2043, 4796, 6537,
  7468, 9277, 11018, 5288, 7454, 10682, 7842, 8333, 5042, 4830, 1382, 8108,
  1524, 6554, 10465, 6553, 10993, 4746, 8489, 1795, 6656, 1875, 8318, 6820, 611,
  7194, 10958, 10719, 2485, 9139, 2595, 2598, 9112, 9116, 9127, 10994, 10995,
  10969, 2939, 2936, 9690, 8236, 11000, 1404, 5512, 606, 7685, 1533, 2079,
  10862, 7827, 7824, 10569, 10423, 9325, 9314, 9311, 9322, 9305, 9289, 9294,
  9291, 3677, 3682, 3671, 3654, 3657, 3655, 3140, 1224, 752, 681, 9309, 1268,
  368, 378, 2104, 2934, 3069, 9776, 9774, 9813, 7160, 4670, 2155, 9476, 4657,
  8529, 9478, 5160, 10718, 6862, 10347, 11035, 7347, 10891, 10494, 3159, 5031,
  9300, 10678, 5028, 5024, 5030, 3156, 2018, 9629, 10670, 10717, 10734, 10736,
  10260, 9218, 10812, 10009, 1991, 4684, 4685, 4681, 4683, 4688, 4677, 4682,
  4759, 8675, 8661, 11081, 7011, 9609, 10761, 3056, 3078, 3079, 3058, 10457, 49,
  1309, 6178, 4799, 7692, 2407, 2409, 2411, 2412, 2416, 2419, 2422, 10360,
  10362, 9412, 7524, 8319, 9255, 657, 819, 7942, 9466, 10987, 6551, 6204, 5416,
  5447, 5477, 5493, 5171, 5624, 5389, 5423, 5472, 5432, 10876, 8657, 10685,
  5168, 5424, 5480, 5449, 5396, 5453, 5473, 5407, 5426, 6603, 5399, 102, 194,
  548, 597, 1248, 1250, 1252, 1254, 1256, 1258, 1260, 1261, 1523, 1817, 1819,
  5153, 5166, 5392, 5397, 5400, 5404, 5440, 5458, 5408, 5411, 5413, 5415, 5420,
  5427, 5429, 5431, 5434, 5436, 5438, 5441, 5443, 5450, 5452, 5459, 5461, 5463,
  5466, 5470, 5478, 5482, 5485, 5487, 5489, 5491, 5492, 5495, 5497, 5499, 6230,
  7871, 10341, 7876, 7878, 10340, 10710, 10728, 310, 967, 1204, 1206, 1208,
  1415, 1619, 1898, 2304, 2306, 2308, 3007, 4371, 4811, 4856, 4869, 5065, 5155,
  5380, 11051, 5386, 5464, 6173, 6175, 6177, 6227, 6648, 6415, 6716, 690, 11049,
  10981, 10912, 10847, 10845, 10383, 6722, 6726, 6813, 7499, 10263, 7936, 8201,
  7497, 9490, 9494, 9512, 9496, 9498, 9503, 10332, 9501, 9505, 10384, 9513,
  9510, 9516, 9514, 9518, 9520, 9532, 9531, 9522, 9524, 9526, 9528, 9530, 8143,
  6877, 11064, 10760, 10247, 10622, 10246, 1776, 10571, 3478, 10729, 6331, 3848,
  4275, 10522, 991, 9784, 9815, 8258, 1931, 10338, 8244, 6678, 10665, 10590,
  3364, 1810, 3366, 10623, 7193, 9539, 9535, 9537, 4919, 6708, 4949, 11071,
  5641, 9472, 10954, 7962, 6715, 6703, 6849, 8490, 7436, 9853, 10614, 6774,
  7435, 10589, 10613, 10588, 9854, 2198, 2489, 6366, 5203, 3403, 10644, 1961,
  2443, 2445, 10393, 7383, 6314, 7900, 7213, 9374, 4177, 7038, 2923, 7215, 7203,
  7205, 7199, 7214, 7212, 7209, 2925, 2921, 9678, 7219, 10750, 2924, 608, 459,
  47, 422, 623, 7271, 3402, 3144, 419, 7687, 1637, 8995, 7032, 77, 7185, 8493,
  5326, 994, 639, 5385, 6365, 7697, 8305, 7998, 2514, 2511, 1838, 9653, 9657,
  9655, 1649, 2083, 2164, 959, 5965, 3016, 3009, 3006, 7938, 3021, 3945, 4339,
  3001, 3027, 3002, 3029, 2208, 203, 633, 7281, 8758, 8761, 8753, 8727, 3797,
  3804, 3803, 3767, 3771, 8729, 3815, 8756, 8731, 8736, 1285, 10632, 11037,
  6322, 6323, 2399, 6319, 3032, 8478, 5698, 5701, 9672, 10441, 6051, 6052, 6049,
  2363, 11085, 6827, 6311, 11016, 10444, 3028, 8176, 6897, 9933, 255, 312, 7777,
  644, 2532, 10284, 10840, 2457, 2458, 2462, 8288, 6441, 7150, 9063, 10475,
  3034, 3037, 8000, 2982, 2927, 2802, 2976, 2601, 1557, 8290, 2459, 1404, 10277,
  8236, 11000, 2715, 2714, 10782, 4796, 5116, 7347, 11035, 10720, 10009, 7528,
  7525, 140, 8381, 4384, 3934, 3935, 3937, 9481, 4253, 8275, 6855, 8074, 4454,
  4474, 4476, 8226, 7660, 7829, 7661, 1423, 6102, 6096, 6101, 6103, 6097, 5126,
  1441, 9676, 5055, 2887, 2893, 2898, 1266, 5374, 2899, 2883, 7468, 2892, 2891,
  2897, 2895, 1776, 8111, 10571, 993, 4468, 8328, 5663, 5665, 5662, 6162, 6161,
  10651, 1331, 5201, 3481, 4323, 4324, 2139, 10601, 1843, 5841, 9394, 9392,
  9385, 9393, 9387, 9499, 5267, 10616, 1524, 1379, 6606, 6607, 4477, 4455, 6603,
  6106, 9097, 130, 1196, 6294, 6319, 6320, 10259, 9875, 1004, 10423, 4505, 4494,
  7038, 2445, 1112, 5203, 9167, 7998, 9154, 9157, 8244, 1714, 7295, 7294, 3001,
  3030, 8006, 8021, 8013, 8024, 8009, 8023, 8003, 2211, 8654, 3934, 3935, 3937,
  9481, 3839, 7557, 10491, 5513, 4434, 4465, 4480, 4501, 4442, 4506, 4497, 4449,
  3715, 3699, 9231, 7150, 4409, 4400, 10694, 4413, 8791, 9447, 4404, 10859,
  7537, 4421, 10237, 8400, 6830, 9329, 8203, 3680, 3650, 3140, 9325, 9308, 9323,
  3676, 9324, 9318, 9313, 2400, 7848, 3840, 4394, 10695, 4399, 8773, 4408, 8793,
  10693, 8807, 8815, 8805, 8822, 11007, 8771, 4420, 8816, 8821, 4406, 4395,
  8801, 8774, 204, 8783, 558, 617, 2140, 3552, 10787, 6424, 6421, 4311, 3089,
  6678, 3016, 3009, 203, 2208, 7938, 6456, 8106, 2972, 7143, 7195, 6502, 7559,
  4696, 3095, 8259, 693, 1649, 2083, 4470, 4508, 7573, 7571, 7574, 7568, 4047,
  5593, 9141, 8234, 5743, 6050, 11112, 6152, 7353, 7600, 7579, 7596, 9472, 1198,
  4468, 4445, 4455, 4484, 4500, 4494, 4434, 5961, 7582, 11377, 2971, 6619, 8654,
  644, 3699, 3715, 9231, 7557, 10266, 5384, 1293, 1594, 6178, 10430, 6308,
  11705, 11563, 8797, 1945, 7384, 4493, 4492, 4465, 4480, 4501, 7625, 7616,
  4519, 7623, 11306, 2345, 2344, 11162, 1843, 11170, 11166, 11165, 11161, 11168,
  11163, 50, 1789, 7482, 11827, 11835, 11838, 11823, 11778, 11837, 11833, 11758,
  11768, 11817, 11821, 11804, 11761, 11813, 11770, 11774, 11822, 11806, 11779,
  11782, 11785, 11792, 11794, 11800, 11803, 11809, 11812, 11815, 11819, 11756,
  11763, 11825, 11826, 11829, 11830, 11828, 11772, 11789, 11808, 11798, 11796,
  11889, 11868, 11843, 11848, 11893, 11866, 11899, 11873, 11859, 11855, 11885,
  11904, 11902, 11892, 11143, 11846, 11844, 11890, 11851, 11858, 11865, 11901,
  11854, 11857, 11864, 11863, 11871, 11872, 11878, 11883, 11880, 11888, 11886,
  11898, 9776, 3006, 11802, 9803, 9774, 9815, 11674, 672, 11703, 7953, 2414,
  7610, 7612, 4502, 7578, 4499, 7588, 7617, 4441, 4506, 4497, 9690, 11112, 3974,
  11321, 11319, 825, 11487, 8798, 4449, 4470, 4507, 7642, 7632, 7630, 7645,
  7647, 7650, 7651, 7628, 7657, 7640, 7659, 7634, 7662, 7636, 11148, 11562,
  11557, 11559, 7664, 7637, 7639, 7635, 7654, 7618, 7622, 7613, 7614, 7605,
  7608, 7603, 7576, 7592, 7597, 7587, 7583, 7585, 4517, 7575, 4503, 4520, 4513,
  7566, 4490, 4496, 4495, 4439, 4440, 4446, 4453, 4451, 4458, 4475, 4469, 4481,
  4463, 1752, 11356, 9538, 11179, 11655, 4510, 4515, 4512, 4516, 7600, 4464,
  7593, 7582, 4486, 4492, 7625, 7616, 4519, 7623, 11435, 7854, 7612, 4502, 4484,
  11959, 11957, 11953, 11955, 11964, 12032, 7937, 6811, 12063, 11930, 7578,
  7610, 4499, 7588, 7591, 7642, 7632, 7630, 7633, 7647, 7638, 7651, 7628, 7656,
  7640, 7659, 7634, 7655, 7636, 7626, 7637, 7639, 7635, 7648, 7857, 10884,
  11447, 11446, 727, 1066, 11948, 11938, 11939, 11935, 11933, 12137, 7618, 7622,
  7613, 7606, 7615, 7608, 7607, 7590, 7592, 7597, 7595, 7586, 7577, 11555,
  11571, 11977, 321, 9152, 1405, 4517, 12001, 2062, 11112, 6859, 3031, 3023,
  12030, 11928, 10971, 145, 7857, 12372, 5384, 1301, 12378, 12479, 12477, 12450,
  12455, 12453, 12456, 12458, 12460, 12462, 12464, 12466, 12469, 12471, 12472,
  12474, 12467, 12448, 12445, 12434, 12442, 12440, 12435, 12437, 12438, 12432,
  12430, 12428, 12425, 12423, 12421, 12419, 12417, 12427, 12413, 12411, 12409,
  12408, 12406, 12224, 7216, 11961, 11335, 10891, 1089, 11686, 11680, 11674,
  9776, 9774, 9784, 9825, 7557, 9813, 11924, 12486, 2062, 2258, 12568, 12634,
  12632, 11949, 11942, 11939, 5663, 5661, 10651, 6162, 11382, 11384, 6160, 6159,
  11983, 11690, 11335, 2894, 11159,
];

let breeding_cooldown_timestamps = [
  1743582551, 1743582584, 1743582605, 1743582680, 1743582707, 1743582761,
  1743582767, 1743582782, 1743582782, 1743582803, 1743582815, 1743582815,
  1743582830, 1743582833, 1743582836, 1743582845, 1743582854, 1743582860,
  1743582884, 1743582884, 1743582884, 1743582884, 1743582884, 1743582884,
  1743582905, 1743582908, 1743582908, 1743582938, 1743582989, 1743582995,
  1743583010, 1743583019, 1743583049, 1743583049, 1743583073, 1743583082,
  1743583097, 1743583106, 1743583106, 1743583184, 1743583193, 1743583214,
  1743583214, 1743583214, 1743583214, 1743583214, 1743583220, 1743583256,
  1743583406, 1743583409, 1743583409, 1743583409, 1743583409, 1743583409,
  1743583409, 1743583409, 1743583409, 1743583409, 1743583409, 1743583481,
  1743583499, 1743583499, 1743583505, 1743583583, 1743583595, 1743583610,
  1743583634, 1743583634, 1743583706, 1743583709, 1743583712, 1743583751,
  1743583766, 1743583787, 1743583805, 1743583826, 1743583856, 1743583880,
  1743583940, 1743583940, 1743583952, 1743583973, 1743583982, 1743583994,
  1743584009, 1743584039, 1743584069, 1743584069, 1743584069, 1743584072,
  1743584084, 1743584099, 1743584102, 1743584102, 1743584111, 1743584132,
  1743584153, 1743584192, 1743584198, 1743584207, 1743584240, 1743584243,
  1743584243, 1743584249, 1743584252, 1743584252, 1743584252, 1743584300,
  1743584312, 1743584312, 1743584312, 1743584324, 1743584369, 1743584372,
  1743584375, 1743584390, 1743584408, 1743584408, 1743584408, 1743584441,
  1743584465, 1743584522, 1743584522, 1743584522, 1743584630, 1743584630,
  1743584645, 1743584858, 1743584861, 1743584894, 1743584927, 1743584939,
  1743584945, 1743584972, 1743584996, 1743585029, 1743585038, 1743585047,
  1743585047, 1743585086, 1743585116, 1743585134, 1743585134, 1743585230,
  1743585248, 1743585248, 1743585278, 1743585278, 1743585392, 1743585434,
  1743585434, 1743585434, 1743585434, 1743585479, 1743585494, 1743585509,
  1743585509, 1743585509, 1743585662, 1743585743, 1743585767, 1743585770,
  1743585782, 1743585815, 1743585923, 1743585941, 1743586046, 1743586073,
  1743586160, 1743586199, 1743586229, 1743586244, 1743586331, 1743586436,
  1743586481, 1743586541, 1743586760, 1743586889, 1743587000, 1743587138,
  1743587210, 1743587240, 1743587288, 1743587315, 1743587507, 1743587507,
  1743587507, 1743587537, 1743587582, 1743587582, 1743587582, 1743587582,
  1743587582, 1743587621, 1743587660, 1743587675, 1743587699, 1743587699,
  1743587699, 1743587720, 1743587723, 1743587744, 1743587780, 1743587807,
  1743587879, 1743587909, 1743587933, 1743587933, 1743587933, 1743587933,
  1743588152, 1743588581, 1743588623, 1743588638, 1743588686, 1743588749,
  1743589016, 1743589016, 1743589178, 1743589178, 1743589178, 1743589244,
  1743589400, 1743589598, 1743589619, 1743589697, 1743590378, 1743590390,
  1743590483, 1743590525, 1743590612, 1743590708, 1743590711, 1743591269,
  1743591269, 1743591269, 1743591824, 1743592085, 1743592133, 1743592202,
  1743592445, 1743592640, 1743592649, 1743592700, 1743592745, 1743592754,
  1743593183, 1743594005, 1743594005, 1743594005, 1743594005, 1743594005,
  1743594005, 1743594005, 1743594005, 1743594005, 1743594005, 1743594209,
  1743594512, 1743594629, 1743594632, 1743594686, 1743594881, 1743594992,
  1743595031, 1743595076, 1743595265, 1743595427, 1743595793, 1743595793,
  1743595793, 1743595793, 1743595793, 1743595793, 1743595853, 1743596441,
  1743596543, 1743596546, 1743596546, 1743596546, 1743596546, 1743596546,
  1743596546, 1743596618, 1743596651, 1743596723, 1743596783, 1743596873,
  1743596930, 1743596975, 1743596981, 1743597125, 1743597125, 1743597143,
  1743597158, 1743597455, 1743597641, 1743597824, 1743598154, 1743598196,
  1743598244, 1743598259, 1743598469, 1743598532, 1743598532, 1743598532,
  1743598532, 1743598532, 1743598532, 1743598532, 1743598532, 1743598532,
  1743598532, 1743598553, 1743598595, 1743598874, 1743598880, 1743598880,
  1743598889, 1743598994, 1743599084, 1743599153, 1743599231, 1743599231,
  1743599372, 1743599447, 1743599498, 1743599519, 1743599564, 1743599603,
  1743599603, 1743599603, 1743599615, 1743600017, 1743600740, 1743600884,
  1743601082, 1743601082, 1743601181, 1743601199, 1743601199, 1743601199,
  1743601199, 1743601199, 1743601349, 1743601349, 1743601745, 1743601805,
  1743601874, 1743602483, 1743602579, 1743602732, 1743602804, 1743602837,
  1743602837, 1743602897, 1743602981, 1743602981, 1743602981, 1743602981,
  1743602981, 1743602996, 1743603020, 1743603041, 1743603044, 1743603437,
  1743603467, 1743603596, 1743604265, 1743604508, 1743605252, 1743605717,
  1743606005, 1743606056, 1743606107, 1743606143, 1743606464, 1743606668,
  1743606857, 1743606917, 1743606998, 1743607010, 1743607331, 1743607355,
  1743607451, 1743607595, 1743607658, 1743607748, 1743607823, 1743607937,
  1743608057, 1743608162, 1743608180, 1743608264, 1743608354, 1743608375,
  1743608423, 1743608507, 1743608528, 1743608531, 1743608543, 1743608600,
  1743608654, 1743608894, 1743609122, 1743609122, 1743609122, 1743609122,
  1743609122, 1743609122, 1743609122, 1743609122, 1743609122, 1743609122,
  1743609326, 1743609350, 1743609350, 1743609350, 1743609350, 1743609350,
  1743609350, 1743609350, 1743609350, 1743609392, 1743610076, 1743610265,
  1743610265, 1743610265, 1743610265, 1743610265, 1743610265, 1743610265,
  1743610265, 1743610265, 1743610265, 1743610343, 1743610406, 1743610406,
  1743610418, 1743610496, 1743611027, 1743611105, 1743611519, 1743611531,
  1743611606, 1743611681, 1743611780, 1743611843, 1743611963, 1743612005,
  1743612911, 1743613598, 1743614906, 1743615146, 1743615176, 1743615176,
  1743615341, 1743615398, 1743615485, 1743616124, 1743617451, 1743617988,
  1743618003, 1743618522, 1743618585, 1743618807, 1743619617, 1743619617,
  1743620196, 1743620505, 1743620505, 1743621937, 1743622084, 1743622540,
  1743623920, 1743625981, 1743626056, 1743626281, 1743626749, 1743627676,
  1743627982, 1743629257, 1743629455, 1743629686, 1743633844, 1743635047,
  1743635305, 1743635525, 1743636722, 1743638801, 1743638801, 1743639560,
  1743639590, 1743640295, 1743640673, 1743640685, 1743641171, 1743641333,
  1743641909, 1743642296, 1743643046, 1743643049, 1743643790, 1743644048,
  1743644288, 1743645617, 1743647657, 1743647933, 1743648209, 1743648209,
  1743648467, 1743648602, 1743648836, 1743648836, 1743648836, 1743648836,
  1743648836, 1743648836, 1743649265, 1743649343, 1743650168, 1743650297,
  1743650414, 1743650783, 1743653126, 1743653213, 1743654146, 1743654221,
  1743654269, 1743659478, 1743661693, 1743661732, 1743661771, 1743661804,
  1743661990, 1743664222, 1743666013, 1743667174, 1743667174, 1743667174,
  1743667174, 1743667174, 1743667174, 1743667174, 1743667174, 1743667174,
  1743667174, 1743667327, 1743667327, 1743667327, 1743667327, 1743667327,
  1743667501, 1743667501, 1743667501, 1743667501, 1743667501, 1743667501,
  1743667501, 1743667501, 1743667501, 1743668698, 1743669145, 1743669244,
  1743669316, 1743669385, 1743670657, 1743670663, 1743670732, 1743670900,
  1743671026, 1743671356, 1743671416, 1743671494, 1743671551, 1743671710,
  1743671821, 1743672091, 1743672157, 1743674392, 1743677257, 1743677506,
  1743677506, 1743677506, 1743677506, 1743677506, 1743677506, 1743677704,
  1743678376, 1743678496, 1743679414, 1743679414, 1743679414, 1743679414,
  1743680926, 1743681964, 1743682006, 1743682930, 1743683698, 1743683923,
  1743683923, 1743683923, 1743683923, 1743683923, 1743683923, 1743683923,
  1743683923, 1743684556, 1743684559, 1743684859, 1743687652, 1743690694,
  1743692536, 1743694393, 1743694393, 1743694393, 1743694393, 1743694393,
  1743694393, 1743694435, 1743694546, 1743694582, 1743696331, 1743696586,
  1743696586, 1743696586, 1743696586, 1743696586, 1743696586, 1743696586,
  1743696586, 1743696586, 1743696865, 1743698026, 1743698212, 1743698344,
  1743698377, 1743698416, 1743698443, 1743698737, 1743700222, 1743701650,
  1743701782, 1743704524, 1743704524, 1743704524, 1743704524, 1743704524,
  1743704524, 1743704524, 1743704524, 1743704524, 1743704524, 1743704851,
  1743704851, 1743704851, 1743704851, 1743704851, 1743704851, 1743704851,
  1743704851, 1743704851, 1743704851, 1743704962, 1743704962, 1743708978,
  1743709011, 1743709011, 1743709011, 1743709011, 1743709011, 1743709011,
  1743709011, 1743709011, 1743709011, 1743709011, 1743709191, 1743709191,
  1743709191, 1743709191, 1743709191, 1743709191, 1743709191, 1743709191,
  1743709191, 1743709191, 1743709311, 1743709311, 1743709311, 1743709311,
  1743709311, 1743709311, 1743709311, 1743709311, 1743709311, 1743709311,
  1743709413, 1743709413, 1743709413, 1743709413, 1743709413, 1743709413,
  1743709413, 1743709413, 1743709413, 1743709413, 1743709530, 1743709530,
  1743709530, 1743709530, 1743709530, 1743709530, 1743709530, 1743709530,
  1743709530, 1743709530, 1743709650, 1743709650, 1743709650, 1743709650,
  1743709650, 1743709650, 1743709650, 1743709650, 1743709650, 1743709650,
  1743709695, 1743709695, 1743713643, 1743713643, 1743713643, 1743713643,
  1743713643, 1743713643, 1743713643, 1743713643, 1743713643, 1743713811,
  1743713811, 1743713811, 1743713811, 1743713811, 1743713811, 1743713811,
  1743713811, 1743713811, 1743713811, 1743713967, 1743713967, 1743713967,
  1743713967, 1743713967, 1743713967, 1743713967, 1743713967, 1743713967,
  1743713967, 1743714081, 1743714111, 1743714111, 1743714111, 1743714111,
  1743714111, 1743714111, 1743714111, 1743714111, 1743714111, 1743714111,
  1743714291, 1743714291, 1743714291, 1743714291, 1743714291, 1743714291,
  1743714291, 1743714291, 1743714291, 1743714291, 1743714549, 1743714549,
  1743714549, 1743714549, 1743714549, 1743714549, 1743714549, 1743714549,
  1743714549, 1743714549, 1743714792, 1743714792, 1743714792, 1743714792,
  1743714792, 1743714792, 1743714792, 1743715758, 1743716055, 1743716976,
  1743717009, 1743717048, 1743718590, 1743718704, 1743723474, 1743725034,
  1743727155, 1743728905, 1743734836, 1743735700, 1743736739, 1743739466,
  1743741485, 1743742580, 1743742580, 1743743621, 1743744071, 1743753882,
  1743928395, 1743928395, 1743928866, 1743930621, 1743759279, 1743759387,
  1743759387, 1743759387, 1743759387, 1743759387, 1743759387, 1743759387,
  1743759387, 1743759387, 1743759387, 1743759435, 1743759435, 1743759435,
  1743759435, 1743932343, 1743759618, 1743759618, 1743759618, 1743759618,
  1743759636, 1743759636, 1743759636, 1743759636, 1743759636, 1743759636,
  1743759636, 1743759636, 1743759636, 1743762342, 1743935958, 1743935997,
  1743938127, 1743938127, 1743938127, 1743938190, 1743938352, 1743938841,
  1743852618, 1743766596, 1743766596, 1743767163, 1743767163, 1743768105,
  1743944079, 1743944577, 1743945610, 1743945610, 1743945610, 1743945610,
  1743945610, 1743945610, 1743945610, 1743945610, 1743945610, 1743945610,
  1743859306, 1743945986, 1743945986, 1743945986, 1743773186, 1743773186,
  1743773186, 1743773186, 1743773186, 1743946142, 1743946142, 1743859832,
  1743946442, 1743860714, 1743775124, 1743948479, 1743948677, 1743949361,
  1743863612, 1743777797, 1743778823, 1743778874, 1743952109, 1743952109,
  1743782228, 1743955103, 1743782825, 1743870401, 1743870938, 1743957338,
  1743785534, 1743785645, 1743785732, 1743785825, 1743960050, 1743960050,
  1743960050, 1743960164, 1743787724, 1743963215, 1743963215, 1743963215,
  1743963215, 1743963215, 1743963215, 1743963215, 1743963215, 1743963215,
  1743963215, 1743963317, 1743963317, 1743963317, 1743963317, 1743963728,
  1743964859, 1743964859, 1743964859, 1743878459, 1743964859, 1743964859,
  1743964859, 1743964859, 1743964859, 1743878459, 1743965099, 1743965099,
  1743965099, 1743965099, 1743969026, 1743969389, 1743797162, 1743802061,
  1743802061, 1743976034, 1743804374, 1743977429, 1743806255, 1743979604,
  1743979604, 1743806888, 1743808862, 1743811736, 1743811838, 1743811889,
  1743985334, 1743985844, 1743986336, 1743986435, 1743986435, 1743986435,
  1743986435, 1743986435, 1743986435, 1743986483, 1743987845, 1743989183,
  1743989219, 1743817331, 1743818252, 1743818306, 1743818354, 1743818846,
  1743819089, 1743819152, 1743819329, 1743994295, 1743994724, 1743823868,
  1743824510, 1743998087, 1743998156, 1743998207, 1743913478, 1743999878,
  1743999878, 1743999878, 1743999878, 1744000349, 1743914768, 1743914834,
  1743915962, 1744010075, 1744012997, 1744013087, 1744013405, 1744013405,
  1744018655, 1743938936, 1744025372, 1744026878, 1744027175, 1744028321,
  1744028699, 1744031801, 1744031846, 1743860753, 1743869353, 1743870106,
  1743872396, 1743872396, 1743872396, 1743872396, 1743873672, 1743873768,
  1744046583, 1743960594, 1744047724, 1744047790, 1744047835, 1744047895,
  1744047949, 1744047997, 1744048051, 1743888603, 1743888603, 1743888603,
  1743888603, 1743888603, 1743888603, 1743975996, 1743891540, 1744064421,
  1743981373, 1743984364, 1743898150, 1743898150, 1743898150, 1743898150,
  1743898150, 1743898150, 1744072537, 1743905900, 1743905900, 1743905900,
  1743905900, 1743992300, 1743906002, 1744083083, 1743934666, 1743937174,
  1743938776, 1744121207, 1744121207, 1744121207, 1744121207, 1744121207,
  1744121207, 1744121207, 1743950990, 1744046972, 1743960827, 1743960998,
  1743971110, 1743982048, 1744068865, 1743991626, 1744020285, 1744020474,
  1744020543, 1744020600, 1744020864, 1744107873, 1744123293, 1744037607,
  1744216539, 1744219005, 1744219071, 1744219122, 1744049571, 1744049649,
  1744223079, 1744058487, 1744058487, 1744145085, 1744064730, 1744237530,
  1744237530, 1744237530, 1744240833, 1744266039, 1744267542, 1744267752,
  1744114798, 1744115131, 1744463080, 1744468300, 1744128691, 1744478404,
  1744134265, 1744307506, 1744134712, 1744134805, 1744487138, 1744142000,
  1744142039, 1744142072, 1744407644, 1744494044, 1744149602, 1744149602,
  1744149602, 1744149602, 1744149602, 1744149602, 1744149602, 1744157727,
  1744249296, 1744335801, 1744335801, 1744335801, 1744335801, 1744428585,
  1744263093, 1744270724, 1744270778, 1744189247, 1744189442, 1744201725,
  1744201773, 1744201833, 1744201881, 1744201944, 1744202001, 1744202937,
  1744203051, 1744203141, 1744552911, 1744384152, 1744384152, 1744384152,
  1744384152, 1744384152, 1744384152, 1744384152, 1744384152, 1744384152,
  1744384152, 1744211883, 1744212054, 1744212504, 1744385352, 1744212720,
  1744385640, 1744385640, 1744385640, 1744385640, 1744385640, 1744385640,
  1744385640, 1744385640, 1744385640, 1744385640, 1744213008, 1744213248,
  1744299861, 1744300725, 1744214325, 1744300725, 1744214325, 1744214325,
  1744214325, 1744214325, 1744214325, 1744214325, 1744214325, 1744214556,
  1744214556, 1744214556, 1744214556, 1744214556, 1744214556, 1744214556,
  1744214556, 1744214556, 1744214556, 1744215723, 1744215723, 1744215723,
  1744215723, 1744215723, 1744217541, 1744217541, 1744394310, 1744394592,
  1744223385, 1744223460, 1744569534, 1744569534, 1744396734, 1744396734,
  1744569534, 1744569534, 1744224336, 1744224495, 1744226901, 1744227696,
  1744320822, 1744408272, 1744408899, 1744240770, 1744242938, 1744331239,
  1744331729, 1744591730, 1744593155, 1744286236, 1744292650, 1744294405,
  1744294462, 1744294516, 1744294567, 1744297255, 1744485897, 1744493168,
  1744501955, 1744340922, 1744433614, 1744360651, 1744374026, 1744551404,
  1744381736, 1744381850, 1744381991, 1744470431, 1744384550, 1744561232,
  1744561319, 1744561367, 1744388630, 1744475096, 1744561607, 1744561715,
  1744388966, 1744388996, 1744389533, 1744391432, 1744590076, 1744596032,
  1744597388, 1744614029, 1744614095, 1744614152, 1744704509, 1744452332,
  1744452383, 1744625237, 1744625264, 1744625417, 1744625441, 1744458674,
  1744458704, 1744459770, 1744466397, 1744639311, 1744466568, 1744466922,
  1744467672, 1744640907, 1744640967, 1744641099, 1744468389, 1744468488,
  1744468659, 1744468755, 1744478142, 1744564665, 1744651308, 1744651362,
  1744737828, 1744478892, 1744478892, 1744478892, 1744478892, 1744478892,
  1744478892, 1744651749, 1744480428, 1744480530, 1744493126, 1744493126,
  1744493126, 1744493126, 1744493126, 1744493126, 1744493126, 1744493126,
  1744493126, 1744493126, 1744493357, 1744493357, 1744493357, 1744493357,
  1744493357, 1744493357, 1744493357, 1744493357, 1744493357, 1744493357,
  1744493612, 1744493612, 1744493612, 1744493612, 1744493612, 1744493612,
  1744493612, 1744493612, 1744493612, 1744493612, 1744493894, 1744493894,
  1744493894, 1744493894, 1744493894, 1744493894, 1744493894, 1744493894,
  1744493894, 1744493894, 1744493993, 1744496303, 1744496303, 1744496303,
  1744496303, 1744496303, 1744496303, 1744496303, 1744496531, 1744496531,
  1744496531, 1744496531, 1744496531, 1744496531, 1744496531, 1744496531,
  1744496531, 1744496531, 1744496726, 1744496726, 1744496726, 1744496726,
  1744496726, 1744496726, 1744496726, 1744496726, 1744496726, 1744496726,
  1744497155, 1744497155, 1744497155, 1744497155, 1744497155, 1744497155,
  1744497155, 1744674827, 1744861408, 1744518778, 1744695356, 1744695356,
  1744695356, 1744522991, 1744531703, 1744538044, 1744545849, 1744719858,
  1744552282, 1744552336, 1744552762, 1744553017, 1744553017, 1744553017,
  1744553017, 1744728748, 1744728811, 1744728862, 1744644415, 1744736215,
  1744909078, 1744586173, 1744586560, 1744586626, 1744590514, 1744590595,
  1744785659, 1744785857, 1744785932, 1744613759, 1744613759, 1744613759,
  1744613759, 1744613759, 1744613759, 1744614023, 1744614023, 1744614023,
  1744614023, 1744614023, 1744614023, 1744614023, 1744614023, 1744623554,
  1744636094, 1744636094, 1744636094, 1744636493, 1744636493, 1744636493,
  1744636493, 1744636493, 1744646465, 1744646465, 1744646465, 1744646465,
  1744646465, 1744646465, 1744646465, 1744646465, 1744646465, 1744646465,
  1744646606, 1744646606, 1744646606, 1744647410, 1744647410, 1744647410,
  1744733810, 1744647410, 1744647410, 1744647410, 1744647410, 1744647410,
  1744647410, 1744647998, 1744647998, 1744647998, 1744647998, 1744647998,
  1744647998, 1744647998, 1744647998, 1744647998, 1744670903, 1744677503,
  1745381702, 1744863302, 1744693493, 1744886039, 1744886039, 1744886039,
  1744886039, 1744886039, 1744886039, 1744886039, 1744886039, 1744886039,
  1744799639, 1744886372, 1744886372, 1744886372, 1744886372, 1744719404,
  1744898328, 1744898352, 1744898439, 1744898736, 1744726851, 1744726851,
  1744726851, 1744726851, 1744740816, 1744741551, 1744828011, 1745446342,
  1744768771, 1744964155, 1744970657, 1744970657, 1744970657, 1744970657,
  1744970657, 1744970657, 1744970657, 1744970657, 1744970657, 1744985595,
  1744985796, 1744985796, 1744985796, 1744985796, 1744985970, 1744985970,
  1744985970, 1744985970, 1744985970, 1744986228, 1744986228, 1744986228,
  1744986228, 1744986228, 1744911207, 1744844585, 1744946988, 1744946988,
  1745043708, 1744883926, 1744890857, 1744890905, 1744890998, 1744891043,
  1744891097, 1744892573, 1745084552, 1745085014, 1745085014, 1745085014,
  1745085014, 1745085014, 1745085014, 1745085014, 1745085014, 1745085014,
  1745085149, 1745085149, 1745085149, 1745085434, 1744914758, 1744926761,
  1744940644, 1744969958, 1744970051, 1745228437, 1745069766, 1745076181,
  1745424301, 1745511019, 1745770219, 1745597419, 1745165419, 1745165419,
  1745125700, 1745310596, 1745413574, 1745320002, 1745320155, 1745429406,
  1745288082, 1745308008, 1745308008, 1745308008, 1745308008, 1745308008,
  1745308008, 1745308008, 1745308008, 1745308008, 1745308008, 1745308131,
  1745308131, 1745308131, 1745309073, 1745309073, 1745309442, 1745309442,
  1745313720, 1745313720, 1745313720, 1745313720, 1745313720, 1745313720,
  1745313720, 1745313720, 1745313720, 1745313720, 1745313981, 1745313981,
  1745313981, 1745313981, 1745313981, 1745313981, 1745313981, 1745313981,
  1745313981, 1745313981, 1745314083, 1745314083, 1745324080, 1745347609,
  1745441086, 1745613958, 1745460684, 1745607209, 1745520884, 1745618489,
  1745877689, 1745877689, 1745877689, 1745877689, 1746093216, 1745707840,
  1745707840, 1745621440, 1745991946, 1745824555, 1745746042, 1745766431,
  1745766431, 1745939231, 1745939231, 1745939231, 1746119008, 1745946208,
  1745946208, 1745946208, 1745946208, 1745946208, 1745946208, 1745946208,
  1745933682, 1746047451, 1746133914, 1746062404, 1745978221, 1743582551,
  1743582551, 1743582584, 1743582605, 1743582680, 1743582707, 1743582761,
  1743582767, 1743582782, 1743582782, 1743582803, 1743582815, 1743582815,
  1743582830, 1743582833, 1743582836, 1743582845, 1743582854, 1743582860,
  1743582884, 1743582884, 1743582884, 1743582884, 1743582884, 1743582884,
  1743582905, 1743582908, 1743582908, 1743582938, 1743582989, 1743582995,
  1743583010, 1743583019, 1743583049, 1743583049, 1743583073, 1743583082,
  1743583097, 1743583106, 1743583106, 1743583184, 1743583193, 1743583214,
  1743583214, 1743583214, 1743583214, 1743583214, 1743583220, 1743583256,
  1743583406, 1743583409, 1743583409, 1743583409, 1743583409, 1743583409,
  1743583409, 1743583409, 1743583409, 1743583409, 1743583409, 1743583481,
  1743583499, 1743583499, 1743583505, 1743583583, 1743583595, 1743583610,
  1743583634, 1743583634, 1743583706, 1743583709, 1743583712, 1743583751,
  1743583766, 1743583787, 1743583805, 1743583826, 1743583856, 1743583880,
  1743583940, 1743583940, 1743583952, 1743583973, 1743583982, 1743583994,
  1743584009, 1743584039, 1743584069, 1743584069, 1743584069, 1743584072,
  1743584084, 1743584099, 1743584102, 1743584102, 1743584111, 1743584132,
  1743584153, 1743584192, 1743584198, 1743584207, 1743584240, 1743584243,
  1743584243, 1743584249, 1743584252, 1743584252, 1743584252, 1743584300,
  1743584312, 1743584312, 1743584312, 1743584324, 1743584369, 1743584372,
  1743584375, 1743584390, 1743584408, 1743584408, 1743584408, 1743584441,
  1743584465, 1743584522, 1743584522, 1743584522, 1743584630, 1743584630,
  1743584645, 1743584858, 1743584861, 1743584894, 1743584927, 1743584939,
  1743584945, 1743584972, 1743584996, 1743585029, 1743585038, 1743585047,
  1743585047, 1743585086, 1743585116, 1743585134, 1743585134, 1743585230,
  1743585248, 1743585248, 1743585278, 1743585278, 1743585392, 1743585434,
  1743585434, 1743585434, 1743585434, 1743585479, 1743585494, 1743585509,
  1743585509, 1743585509, 1743585662, 1743585743, 1743585767, 1743585770,
  1743585782, 1743585815, 1743585923, 1743585941, 1743586046, 1743586073,
  1743586160, 1743586199, 1743586229, 1743586244, 1743586331, 1743586436,
  1743586481, 1743586541, 1743586760, 1743586889, 1743587000, 1743587138,
  1743587210, 1743587240, 1743587288, 1743587315, 1743587507, 1743587507,
  1743587507, 1743587537, 1743587582, 1743587582, 1743587582, 1743587582,
  1743587582, 1743587621, 1743587660, 1743587675, 1743587699, 1743587699,
  1743587699, 1743587720, 1743587723, 1743587744, 1743587780, 1743587807,
  1743587879, 1743587909, 1743587933, 1743587933, 1743587933, 1743587933,
  1743588152, 1743588581, 1743588623, 1743588638, 1743588686, 1743588749,
  1743589016, 1743589016, 1743589178, 1743589178, 1743589178, 1743589244,
  1743589400, 1743589598, 1743589619, 1743589697, 1743590378, 1743590390,
  1743590483, 1743590525, 1743590612, 1743590708, 1743590711, 1743591269,
  1743591269, 1743591269, 1743591824, 1743592085, 1743592133, 1743592202,
  1743592445, 1743592640, 1743592649, 1743592700, 1743592745, 1743592754,
  1743593183, 1743594005, 1743594005, 1743594005, 1743594005, 1743594005,
  1743594005, 1743594005, 1743594005, 1743594005, 1743594005, 1743594209,
  1743594512, 1743594629, 1743594632, 1743594686, 1743594881, 1743594992,
  1743595031, 1743595076, 1743595265, 1743595427, 1743595793, 1743595793,
  1743595793, 1743595793, 1743595793, 1743595793, 1743595853, 1743596441,
  1743596543, 1743596546, 1743596546, 1743596546, 1743596546, 1743596546,
  1743596546, 1743596618, 1743596651, 1743596723, 1743596783, 1743596873,
  1743596930, 1743596975, 1743596981, 1743597125, 1743597125, 1743597143,
  1743597158, 1743597455, 1743597641, 1743597824, 1743598154, 1743598196,
  1743598244, 1743598259, 1743598469, 1743598532, 1743598532, 1743598532,
  1743598532, 1743598532, 1743598532, 1743598532, 1743598532, 1743598532,
  1743598532, 1743598553, 1743598595, 1743598874, 1743598880, 1743598880,
  1743598889, 1743598994, 1743599084, 1743599153, 1743599231, 1743599231,
  1743599372, 1743599447, 1743599498, 1743599519, 1743599564, 1743599603,
  1743599603, 1743599603, 1743599615, 1743600017, 1743600740, 1743600884,
  1743601082, 1743601082, 1743601181, 1743601199, 1743601199, 1743601199,
  1743601199, 1743601199, 1743601349, 1743601349, 1743601745, 1743601805,
  1743601874, 1743602483, 1743602579, 1743602732, 1743602804, 1743602837,
  1743602837, 1743602897, 1743602981, 1743602981, 1743602981, 1743602981,
  1743602981, 1743602996, 1743603020, 1743603041, 1743603044, 1743603437,
  1743603467, 1743603596, 1743604265, 1743604508, 1743605252, 1743605717,
  1743606005, 1743606056, 1743606107, 1743606143, 1743606464, 1743606668,
  1743606857, 1743606917, 1743606998, 1743607010, 1743607331, 1743607355,
  1743607451, 1743607595, 1743607658, 1743607748, 1743607823, 1743607937,
  1743608057, 1743608162, 1743608180, 1743608264, 1743608354, 1743608375,
  1743608423, 1743608507, 1743608528, 1743608531, 1743608543, 1743608600,
  1743608654, 1743608894, 1743609122, 1743609122, 1743609122, 1743609122,
  1743609122, 1743609122, 1743609122, 1743609122, 1743609122, 1743609122,
  1743609326, 1743609350, 1743609350, 1743609350, 1743609350, 1743609350,
  1743609350, 1743609350, 1743609350, 1743609392, 1743610076, 1743610265,
  1743610265, 1743610265, 1743610265, 1743610265, 1743610265, 1743610265,
  1743610265, 1743610265, 1743610265, 1743610343, 1743610406, 1743610406,
  1743610418, 1743610496, 1743611027, 1743611105, 1743611519, 1743611531,
  1743611606, 1743611681, 1743611780, 1743611843, 1743611963, 1743612005,
  1743612911, 1743613598, 1743614906, 1743615146, 1743615176, 1743615176,
  1743615341, 1743615398, 1743615485, 1743616124, 1743617451, 1743617988,
  1743618003, 1743618522, 1743618585, 1743618807, 1743619617, 1743619617,
  1743620196, 1743620505, 1743620505, 1743621937, 1743622084, 1743622540,
  1743623920, 1743625981, 1743626056, 1743626281, 1743626749, 1743627676,
  1743627982, 1743629257, 1743629455, 1743629686, 1743633844, 1743635047,
  1743635305, 1743635525, 1743636722, 1743638801, 1743638801, 1743639560,
  1743639590, 1743640295, 1743640673, 1743640685, 1743641171, 1743641333,
  1743641909, 1743642296, 1743643046, 1743643049, 1743643790, 1743644048,
  1743644288, 1743645617, 1743647657, 1743647933, 1743648209, 1743648209,
  1743648467, 1743648602, 1743648836, 1743648836, 1743648836, 1743648836,
  1743648836, 1743648836, 1743649265, 1743649343, 1743650168, 1743650297,
  1743650414, 1743650783, 1743653126, 1743653213, 1743654146, 1743654221,
  1743654269, 1743659478, 1743661693, 1743661732, 1743661771, 1743661804,
  1743661990, 1743664222, 1743666013, 1743667174, 1743667174, 1743667174,
  1743667174, 1743667174, 1743667174, 1743667174, 1743667174, 1743667174,
  1743667174, 1743667327, 1743667327, 1743667327, 1743667327, 1743667327,
  1743667501, 1743667501, 1743667501, 1743667501, 1743667501, 1743667501,
  1743667501, 1743667501, 1743667501, 1743668698, 1743669145, 1743669244,
  1743669316, 1743669385, 1743670657, 1743670663, 1743670732, 1743670900,
  1743671026, 1743671356, 1743671416, 1743671494, 1743671551, 1743671710,
  1743671821, 1743672091, 1743672157, 1743674392, 1743677257, 1743677506,
  1743677506, 1743677506, 1743677506, 1743677506, 1743677506, 1743677704,
  1743678376, 1743678496, 1743679414, 1743679414, 1743679414, 1743679414,
  1743680926, 1743681964, 1743682006, 1743682930, 1743683698, 1743683923,
  1743683923, 1743683923, 1743683923, 1743683923, 1743683923, 1743683923,
  1743683923, 1743684556, 1743684559, 1743684859, 1743687652, 1743690694,
  1743692536, 1743694393, 1743694393, 1743694393, 1743694393, 1743694393,
  1743694393, 1743694435, 1743694546, 1743694582, 1743696331, 1743696586,
  1743696586, 1743696586, 1743696586, 1743696586, 1743696586, 1743696586,
  1743696586, 1743696586, 1743696865, 1743698026, 1743698212, 1743698344,
  1743698377, 1743698416, 1743698443, 1743698737, 1743700222, 1743701650,
  1743701782, 1743704524, 1743704524, 1743704524, 1743704524, 1743704524,
  1743704524, 1743704524, 1743704524, 1743704524, 1743704524, 1743704851,
  1743704851, 1743704851, 1743704851, 1743704851, 1743704851, 1743704851,
  1743704851, 1743704851, 1743704851, 1743704962, 1743704962, 1743708978,
  1743709011, 1743709011, 1743709011, 1743709011, 1743709011, 1743709011,
  1743709011, 1743709011, 1743709011, 1743709011, 1743709191, 1743709191,
  1743709191, 1743709191, 1743709191, 1743709191, 1743709191, 1743709191,
  1743709191, 1743709191, 1743709311, 1743709311, 1743709311, 1743709311,
  1743709311, 1743709311, 1743709311, 1743709311, 1743709311, 1743709311,
  1743709413, 1743709413, 1743709413, 1743709413, 1743709413, 1743709413,
  1743709413, 1743709413, 1743709413, 1743709413, 1743709530, 1743709530,
  1743709530, 1743709530, 1743709530, 1743709530, 1743709530, 1743709530,
  1743709530, 1743709530, 1743709650, 1743709650, 1743709650, 1743709650,
  1743709650, 1743709650, 1743709650, 1743709650, 1743709650, 1743709650,
  1743709695, 1743709695, 1743713643, 1743713643, 1743713643, 1743713643,
  1743713643, 1743713643, 1743713643, 1743713643, 1743713643, 1743713811,
  1743713811, 1743713811, 1743713811, 1743713811, 1743713811, 1743713811,
  1743713811, 1743713811, 1743713811, 1743713967, 1743713967, 1743713967,
  1743713967, 1743713967, 1743713967, 1743713967, 1743713967, 1743713967,
  1743713967, 1743714081, 1743714111, 1743714111, 1743714111, 1743714111,
  1743714111, 1743714111, 1743714111, 1743714111, 1743714111, 1743714111,
  1743714291, 1743714291, 1743714291, 1743714291, 1743714291, 1743714291,
  1743714291, 1743714291, 1743714291, 1743714291, 1743714549, 1743714549,
  1743714549, 1743714549, 1743714549, 1743714549, 1743714549, 1743714549,
  1743714549, 1743714549, 1743714792, 1743714792, 1743714792, 1743714792,
  1743714792, 1743714792, 1743714792, 1743715758, 1743716055, 1743716976,
  1743717009, 1743717048, 1743718590, 1743718704, 1743723474, 1743725034,
  1743727155, 1743728905, 1743734836, 1743735700, 1743736739, 1743739466,
  1743741485, 1743742580, 1743742580, 1743743621, 1743744071, 1743753882,
  1743928395, 1743928395, 1743928866, 1743930621, 1743759279, 1743759387,
  1743759387, 1743759387, 1743759387, 1743759387, 1743759387, 1743759387,
  1743759387, 1743759387, 1743759387, 1743759435, 1743759435, 1743759435,
  1743759435, 1743932343, 1743759618, 1743759618, 1743759618, 1743759618,
  1743759636, 1743759636, 1743759636, 1743759636, 1743759636, 1743759636,
  1743759636, 1743759636, 1743759636, 1743762342, 1743935958, 1743935997,
  1743938127, 1743938127, 1743938127, 1743938190, 1743938352, 1743938841,
  1743852618, 1743766596, 1743766596, 1743767163, 1743767163, 1743768105,
  1743944079, 1743944577, 1743945610, 1743945610, 1743945610, 1743945610,
  1743945610, 1743945610, 1743945610, 1743945610, 1743945610, 1743945610,
  1743859306, 1743945986, 1743945986, 1743945986, 1743773186, 1743773186,
  1743773186, 1743773186, 1743773186, 1743946142, 1743946142, 1743859832,
  1743946442, 1743860714, 1743775124, 1743948479, 1743948677, 1743949361,
  1743863612, 1743777797, 1743778823, 1743778874, 1743952109, 1743952109,
  1743782228, 1743955103, 1743782825, 1743870401, 1743870938, 1743957338,
  1743785534, 1743785645, 1743785732, 1743785825, 1743960050, 1743960050,
  1743960050, 1743960164, 1743787724, 1743963215, 1743963215, 1743963215,
  1743963215, 1743963215, 1743963215, 1743963215, 1743963215, 1743963215,
  1743963215, 1743963317, 1743963317, 1743963317, 1743963317, 1743963728,
  1743964859, 1743964859, 1743964859, 1743878459, 1743964859, 1743964859,
  1743964859, 1743964859, 1743964859, 1743878459, 1743965099, 1743965099,
  1743965099, 1743965099, 1743969026, 1743969389, 1743797162, 1743802061,
  1743802061, 1743976034, 1743804374, 1743977429, 1743806255, 1743979604,
  1743979604, 1743806888, 1743808862, 1743811736, 1743811838, 1743811889,
  1743985334, 1743985844, 1743986336, 1743986435, 1743986435, 1743986435,
  1743986435, 1743986435, 1743986435, 1743986483, 1743987845, 1743989183,
  1743989219, 1743817331, 1743818252, 1743818306, 1743818354, 1743818846,
  1743819089, 1743819152, 1743819329, 1743994295, 1743994724, 1743823868,
  1743824510, 1743998087, 1743998156, 1743998207, 1743913478, 1743999878,
  1743999878, 1743999878, 1743999878, 1744000349, 1743914768, 1743914834,
  1743915962, 1744010075, 1744012997, 1744013087, 1744013405, 1744013405,
  1744018655, 1743938936, 1744025372, 1744026878, 1744027175, 1744028321,
  1744028699, 1744031801, 1744031846, 1743860753, 1743869353, 1743870106,
  1743872396, 1743872396, 1743872396, 1743872396, 1743873672, 1743873768,
  1744046583, 1743960594, 1744047724, 1744047790, 1744047835, 1744047895,
  1744047949, 1744047997, 1744048051, 1743888603, 1743888603, 1743888603,
  1743888603, 1743888603, 1743888603, 1743975996, 1743891540, 1744064421,
  1743981373, 1743984364, 1743898150, 1743898150, 1743898150, 1743898150,
  1743898150, 1743898150, 1744072537, 1743905900, 1743905900, 1743905900,
  1743905900, 1743992300, 1743906002, 1744083083, 1743934666, 1743937174,
  1743938776, 1744121207, 1744121207, 1744121207, 1744121207, 1744121207,
  1744121207, 1744121207, 1743950990, 1744046972, 1743960827, 1743960998,
  1743971110, 1743982048, 1744068865, 1743991626, 1744020285, 1744020474,
  1744020543, 1744020600, 1744020864, 1744107873, 1744123293, 1744037607,
  1744216539, 1744219005, 1744219071, 1744219122, 1744049571, 1744049649,
  1744223079, 1744058487, 1744058487, 1744145085, 1744064730, 1744237530,
  1744237530, 1744237530, 1744240833, 1744266039, 1744267542, 1744267752,
  1744114798, 1744115131, 1744463080, 1744468300, 1744128691, 1744478404,
  1744134265, 1744307506, 1744134712, 1744134805, 1744487138, 1744142000,
  1744142039, 1744142072, 1744407644, 1744494044, 1744149602, 1744149602,
  1744149602, 1744149602, 1744149602, 1744149602, 1744149602, 1744157727,
  1744249296, 1744335801, 1744335801, 1744335801, 1744335801, 1744428585,
  1744263093, 1744270724, 1744270778, 1744189247, 1744189442, 1744201725,
  1744201773, 1744201833, 1744201881, 1744201944, 1744202001, 1744202937,
  1744203051, 1744203141, 1744552911, 1744384152, 1744384152, 1744384152,
  1744384152, 1744384152, 1744384152, 1744384152, 1744384152, 1744384152,
  1744384152, 1744211883, 1744212054, 1744212504, 1744385352, 1744212720,
  1744385640, 1744385640, 1744385640, 1744385640, 1744385640, 1744385640,
  1744385640, 1744385640, 1744385640, 1744385640, 1744213008, 1744213248,
  1744299861, 1744300725, 1744214325, 1744300725, 1744214325, 1744214325,
  1744214325, 1744214325, 1744214325, 1744214325, 1744214325, 1744214556,
  1744214556, 1744214556, 1744214556, 1744214556, 1744214556, 1744214556,
  1744214556, 1744214556, 1744214556, 1744215723, 1744215723, 1744215723,
  1744215723, 1744215723, 1744217541, 1744217541, 1744394310, 1744394592,
  1744223385, 1744223460, 1744569534, 1744569534, 1744396734, 1744396734,
  1744569534, 1744569534, 1744224336, 1744224495, 1744226901, 1744227696,
  1744320822, 1744408272, 1744408899, 1744240770, 1744242938, 1744331239,
  1744331729, 1744591730, 1744593155, 1744286236, 1744292650, 1744294405,
  1744294462, 1744294516, 1744294567, 1744297255, 1744485897, 1744493168,
  1744501955, 1744340922, 1744433614, 1744360651, 1744374026, 1744551404,
  1744381736, 1744381850, 1744381991, 1744470431, 1744384550, 1744561232,
  1744561319, 1744561367, 1744388630, 1744475096, 1744561607, 1744561715,
  1744388966, 1744388996, 1744389533, 1744391432, 1744590076, 1744596032,
  1744597388, 1744614029, 1744614095, 1744614152, 1744704509, 1744452332,
  1744452383, 1744625237, 1744625264, 1744625417, 1744625441, 1744458674,
  1744458704, 1744459770, 1744466397, 1744639311, 1744466568, 1744466922,
  1744467672, 1744640907, 1744640967, 1744641099, 1744468389, 1744468488,
  1744468659, 1744468755, 1744478142, 1744564665, 1744651308, 1744651362,
  1744737828, 1744478892, 1744478892, 1744478892, 1744478892, 1744478892,
  1744478892, 1744651749, 1744480428, 1744480530, 1744493126, 1744493126,
  1744493126, 1744493126, 1744493126, 1744493126, 1744493126, 1744493126,
  1744493126, 1744493126, 1744493357, 1744493357, 1744493357, 1744493357,
  1744493357, 1744493357, 1744493357, 1744493357, 1744493357, 1744493357,
  1744493612, 1744493612, 1744493612, 1744493612, 1744493612, 1744493612,
  1744493612, 1744493612, 1744493612, 1744493612, 1744493894, 1744493894,
  1744493894, 1744493894, 1744493894, 1744493894, 1744493894, 1744493894,
  1744493894, 1744493894, 1744493993, 1744496303, 1744496303, 1744496303,
  1744496303, 1744496303, 1744496303, 1744496303, 1744496531, 1744496531,
  1744496531, 1744496531, 1744496531, 1744496531, 1744496531, 1744496531,
  1744496531, 1744496531, 1744496726, 1744496726, 1744496726, 1744496726,
  1744496726, 1744496726, 1744496726, 1744496726, 1744496726, 1744496726,
  1744497155, 1744497155, 1744497155, 1744497155, 1744497155, 1744497155,
  1744497155, 1744674827, 1744861408, 1744518778, 1744695356, 1744695356,
  1744695356, 1744522991, 1744531703, 1744538044, 1744545849, 1744719858,
  1744552282, 1744552336, 1744552762, 1744553017, 1744553017, 1744553017,
  1744553017, 1744728748, 1744728811, 1744728862, 1744644415, 1744736215,
  1744909078, 1744586173, 1744586560, 1744586626, 1744590514, 1744590595,
  1744785659, 1744785857, 1744785932, 1744613759, 1744613759, 1744613759,
  1744613759, 1744613759, 1744613759, 1744614023, 1744614023, 1744614023,
  1744614023, 1744614023, 1744614023, 1744614023, 1744614023, 1744623554,
  1744636094, 1744636094, 1744636094, 1744636493, 1744636493, 1744636493,
  1744636493, 1744636493, 1744646465, 1744646465, 1744646465, 1744646465,
  1744646465, 1744646465, 1744646465, 1744646465, 1744646465, 1744646465,
  1744646606, 1744646606, 1744646606, 1744647410, 1744647410, 1744647410,
  1744733810, 1744647410, 1744647410, 1744647410, 1744647410, 1744647410,
  1744647410, 1744647998, 1744647998, 1744647998, 1744647998, 1744647998,
  1744647998, 1744647998, 1744647998, 1744647998, 1744670903, 1744677503,
  1745381702, 1744863302, 1744693493, 1744886039, 1744886039, 1744886039,
  1744886039, 1744886039, 1744886039, 1744886039, 1744886039, 1744886039,
  1744799639, 1744886372, 1744886372, 1744886372, 1744886372, 1744719404,
  1744898328, 1744898352, 1744898439, 1744898736, 1744726851, 1744726851,
  1744726851, 1744726851, 1744740816, 1744741551, 1744828011, 1745446342,
  1744768771, 1744964155, 1744970657, 1744970657, 1744970657, 1744970657,
  1744970657, 1744970657, 1744970657, 1744970657, 1744970657, 1744985595,
  1744985796, 1744985796, 1744985796, 1744985796, 1744985970, 1744985970,
  1744985970, 1744985970, 1744985970, 1744986228, 1744986228, 1744986228,
  1744986228, 1744986228, 1744911207, 1744844585, 1744946988, 1744946988,
  1745043708, 1744883926, 1744890857, 1744890905, 1744890998, 1744891043,
  1744891097, 1744892573, 1745084552, 1745085014, 1745085014, 1745085014,
  1745085014, 1745085014, 1745085014, 1745085014, 1745085014, 1745085014,
  1745085149, 1745085149, 1745085149, 1745085434, 1744914758, 1744926761,
  1744940644, 1744969958, 1744970051, 1745228437, 1745069766, 1745076181,
  1745424301, 1745511019, 1745770219, 1745597419, 1745165419, 1745165419,
  1745125700, 1745310596, 1745413574, 1745320002, 1745320155, 1745429406,
  1745288082, 1745308008, 1745308008, 1745308008, 1745308008, 1745308008,
  1745308008, 1745308008, 1745308008, 1745308008, 1745308008, 1745308131,
  1745308131, 1745308131, 1745309073, 1745309073, 1745309442, 1745309442,
  1745313720, 1745313720, 1745313720, 1745313720, 1745313720, 1745313720,
  1745313720, 1745313720, 1745313720, 1745313720, 1745313981, 1745313981,
  1745313981, 1745313981, 1745313981, 1745313981, 1745313981, 1745313981,
  1745313981, 1745313981, 1745314083, 1745314083, 1745324080, 1745347609,
  1745441086, 1745613958, 1745460684, 1745607209, 1745520884, 1745618489,
  1745877689, 1745877689, 1745877689, 1745877689, 1746093216, 1745707840,
  1745707840, 1745621440, 1745991946, 1745824555, 1745746042, 1745766431,
  1745766431, 1745939231, 1745939231, 1745939231, 1746119008, 1745946208,
  1745946208, 1745946208, 1745946208, 1745946208, 1745946208, 1745946208,
  1745933682, 1746047451, 1746133914, 1746062404, 1745978221,
];

let breeding_count_token_ids = [
  6678, 5249, 6859, 3031, 6811, 9538, 3016, 10348, 11111, 6366, 7149, 7557,
  1476, 1378, 9815, 7038, 3024, 27, 10651, 3030, 3023, 50, 247, 7938, 2062,
  3018, 3974, 5203, 9774, 1649, 2083, 675, 7150, 6160, 9784, 9776, 203, 5662,
  3001, 2208, 3009, 3006, 3839, 2445, 5663, 1233, 6162, 6494, 9825, 9749, 1383,
  6161, 8234, 1442, 7178, 2286, 9803, 4964, 8244, 6424, 5661, 6159, 6518, 11112,
  1301, 9783, 7595, 7586, 7771, 7581, 4799, 7594, 7589, 3022, 7590, 1309, 1769,
  4486, 7619, 4477, 7655, 7593, 4464, 3675, 3677, 8812, 4505, 4511, 7937, 4459,
  4507, 4510, 4515, 4512, 7577, 4516, 9063, 4518, 9214, 7599, 7580, 4487, 7598,
  7663, 7643, 7638, 7649, 7621, 7601, 7656, 7648, 7644, 11179, 7646, 7626,
  11555, 7658, 7627, 7652, 7828, 7624, 7633, 7629, 7606, 11172, 7607, 11924,
  7609, 7611, 7572, 4485, 7584, 7591, 4442, 4508, 7604, 7615, 7641, 7631, 7653,
  274, 10626, 2512, 2442, 1351, 7200, 7202, 7201, 10751, 7217, 7686, 7029, 7030,
  556, 1544, 10388, 3013, 3163, 7284, 3756, 3750, 3784, 3777, 3806, 3825, 2441,
  10415, 6320, 3676, 6495, 7467, 1879, 10720, 8405, 5515, 10422, 10459, 9324,
  9296, 9141, 2887, 7346, 389, 10706, 10259, 1982, 727, 1293, 10430, 6184, 6602,
  6321, 6294, 2934, 5512, 1795, 6322, 6323, 10260, 5965, 4491, 4438, 251, 1523,
  7571, 4433, 4445, 4462, 4467, 4479, 4441, 4504, 4489, 4435, 9223, 9224, 3672,
  4448, 6605, 5699, 10783, 6050, 7269, 4174, 6898, 9468, 643, 5593, 3035, 2501,
  615, 10719, 853, 7527, 1145, 1369, 1548, 1616, 4514, 4482, 37, 2894, 4460,
  7573, 11951, 7574, 10571, 9325, 3140, 9813, 11035, 7347, 10891, 10009, 6178,
  6603, 1776, 9472, 1404, 7998, 6319, 644, 3934, 3935, 3937, 9481, 4468, 1843,
  4455, 10423, 11000, 8654, 3767, 2164, 7777, 6502, 8729, 8731, 8736, 3797,
  3803, 8753, 312, 7697, 8236, 4454, 4474, 4476, 7829, 6855, 1594, 4796, 7468,
  1524, 9690, 4494, 4434, 9875, 7639, 7632, 7630, 7647, 7651, 7628, 7640, 7659,
  7634, 7636, 7637, 7635, 7588, 7618, 7622, 7613, 7608, 7592, 7597, 4517, 7857,
  11939, 11335, 7642, 4499, 4465, 4484, 4480, 4501, 4506, 4497, 4449, 3715,
  3699, 9231, 4470, 7600, 7582, 7578, 5384, 4492, 7625, 7616, 4519, 7623, 11674,
  7610, 7612, 4502, 6606, 8176, 7568, 1285, 12032, 7366, 6862, 11948, 10665,
  5701, 3034, 10590, 7528, 2414, 3021, 11435, 3027, 2258, 633, 2399, 9329, 7854,
  9323, 9313, 9308, 10632, 11162, 7585, 6827, 7654, 7579, 7596, 4493, 4500,
  7617, 7645, 7650, 7657, 7662, 7664, 8367, 7583, 1991, 6854, 11942, 11949,
  11940, 7614, 7605, 7603, 7576, 7587, 7195, 7962, 2715, 7353, 2927, 2802, 2976,
  2601, 5665, 1331, 5116, 3032, 9676, 2923, 7215, 7537, 7212, 7199, 7205, 7203,
  7219, 2363, 7209, 3402, 7271, 2714, 4421, 10859, 7559, 3403, 9933, 3650, 8226,
  2443, 11085, 2344, 6441, 693, 419, 6619, 10644, 4404, 639, 5385, 255, 8000,
  4409, 4400, 10694, 4413, 8791, 9447, 374, 12030, 10444, 292, 5267, 6607, 7185,
  1379, 6608, 10424, 3028, 11016, 9470, 6311, 6897, 2218, 2713, 2712, 8669,
  2922, 614, 10750, 7208, 29, 7218, 7206, 2925, 7207, 2921, 2924, 7210, 10752,
  7214, 7204, 7688, 1303, 7997, 3787, 7529, 2403, 7525, 955, 4339, 3945, 3002,
  3029, 10782, 2073, 10805, 398, 2397, 10460, 3775, 2109, 224, 3744, 4880, 3815,
  8761, 3783, 3785, 3771, 3804, 8738, 8739, 8756, 8758, 4177, 9293, 8210, 1279,
  8788, 7281, 77, 9475, 6618, 32, 1232, 7177, 9318, 9925, 88, 3840, 2342, 5132,
  1376, 6158, 1961, 9319, 73, 6826, 7961, 1589, 2511, 5126, 9910, 3680, 8225,
  8290, 8995, 7032, 10723, 8455, 387, 5048, 2890, 3025, 959, 8459, 339, 253,
  5660, 2803, 1557, 2981, 2503, 2926, 10277, 8817, 9243, 4412, 4415, 4414, 4402,
  8810, 8806, 8779, 4396, 2302, 3037, 10773, 3036, 1945, 1448, 4092, 2459, 6386,
  5697, 4461, 3033, 1004, 4444, 5698, 7661, 7602, 2489, 7660, 4466, 12455, 7685,
  12458, 12453, 12450, 1533, 12456, 2079, 10862, 12477, 8798, 12460, 7827,
  12462, 12464, 12466, 12469, 12471, 606, 4240, 12472, 9213, 3642, 12479, 10569,
  12378, 9291, 368, 1268, 9309, 1405, 681, 752, 1224, 9210, 3655, 3657, 3654,
  3671, 3682, 12001, 7824, 9294, 9289, 9305, 9322, 9311, 9314, 3325, 11928,
  10971, 145, 12372, 6293, 2936, 5037, 9116, 2939, 12430, 1363, 8108, 12435,
  1382, 12437, 12438, 12432, 4830, 5042, 8333, 7842, 10682, 7454, 10969, 5288,
  11018, 12428, 9277, 111, 6537, 10443, 2043, 1414, 7699, 5376, 12425, 6554,
  10465, 6553, 10993, 10995, 10994, 9127, 9152, 9112, 2598, 2595, 9139, 2485,
  10958, 7194, 12474, 611, 6820, 12467, 12448, 12445, 12434, 8318, 12442, 1875,
  6656, 12440, 8489, 4746, 378, 6695, 2104, 8657, 5432, 5472, 5423, 5389, 11571,
  5624, 5171, 5493, 5477, 5447, 5416, 6204, 6551, 10987, 9466, 7942, 819, 657,
  9255, 8319, 7524, 9412, 10362, 10360, 2422, 2419, 2416, 10876, 10685, 3069,
  5168, 1254, 1252, 1250, 10884, 1248, 11447, 11446, 597, 548, 1066, 194, 102,
  11938, 5399, 2588, 3673, 5426, 5407, 5473, 5453, 5396, 11935, 5449, 11933,
  5480, 5424, 12137, 2412, 2411, 2409, 2407, 2018, 3156, 5030, 5024, 5028,
  10678, 9300, 5031, 3159, 10494, 6872, 5149, 11977, 10347, 10718, 5160, 9478,
  8529, 4657, 9476, 2155, 4670, 7160, 321, 3885, 2260, 7726, 9629, 10670, 10717,
  8661, 7692, 463, 49, 10457, 3058, 3079, 3078, 3056, 10761, 9609, 7011, 11081,
  8675, 10734, 4759, 4682, 4677, 4688, 4683, 4681, 4685, 4684, 346, 10812, 9218,
  10736, 7707, 5648, 1258, 9107, 5033, 7885, 12406, 8829, 8246, 4929, 10310,
  10438, 5117, 12224, 5119, 7216, 11961, 10292, 2236, 8204, 10518, 6841, 10434,
  10432, 242, 10534, 3143, 9295, 5252, 1089, 2784, 8213, 4698, 2870, 4890, 6766,
  8287, 12408, 8687, 7708, 2790, 10350, 3382, 3379, 8092, 8686, 3384, 2686,
  2688, 10579, 6793, 1485, 9448, 4321, 4360, 2517, 1630, 1094, 2872, 1507, 4930,
  4147, 31, 8245, 10652, 8453, 8383, 10377, 9280, 6831, 7457, 6769, 10322, 6304,
  4667, 8212, 10574, 7275, 7026, 2444, 4532, 4534, 11983, 4526, 4529, 11690,
  9013, 11046, 9669, 4704, 9943, 6375, 6376, 8313, 10972, 6157, 2095, 6155,
  11686, 3407, 2303, 11680, 12486, 12568, 7893, 7895, 7896, 12634, 6598, 9066,
  12632, 9696, 1474, 3663, 2313, 7910, 6254, 11382, 11384, 9643, 10488, 516,
  3020, 933, 8051, 10798, 10267, 9593, 7681, 9590, 5040, 5047, 6412, 7533, 5041,
  6410, 6413, 7679, 7455, 6408, 7683, 7818, 7453, 7446, 7888, 8649, 6406, 6402,
  6404, 6411, 6405, 10692, 10690, 7887, 8648, 6363, 6394, 7789, 5146, 12421,
  6284, 1234, 6833, 2684, 8482, 1022, 12423, 9285, 9284, 9087, 3534, 3240, 9286,
  2224, 8767, 56, 9743, 9746, 9739, 10591, 7400, 6352, 6264, 629, 24, 5581,
  10999, 8766, 7532, 2528, 10691, 4236, 9471, 7414, 5604, 10806, 7186, 11107,
  10387, 10872, 10462, 10386, 12411, 8590, 8521, 7000, 7004, 4940, 7363, 8522,
  7359, 8608, 8528, 155, 10331, 10445, 12409, 6238, 10960, 10557, 9265, 2525,
  4972, 2531, 6401, 12419, 7814, 2527, 8177, 10461, 12417, 8073, 3685, 40, 8647,
  7279, 3477, 12427, 8652, 9114, 7750, 9841, 12413, 3358, 8208, 7861, 9986,
  7024, 4932, 2471, 1256, 11930, 7999, 8815, 10693, 8793, 4408, 8773, 4399,
  10695, 4394, 7848, 2400, 8203, 6830, 8400, 10237, 10364, 10363, 2418, 715,
  6894, 7805, 10325, 392, 2398, 433, 5513, 10491, 11148, 11562, 8807, 8805,
  8003, 8822, 7523, 8259, 3095, 4696, 7143, 2972, 8106, 6456, 3089, 4311, 6421,
  10787, 3552, 2140, 617, 558, 8783, 204, 8774, 8801, 4395, 4406, 8821, 8816,
  4420, 8771, 11007, 2211, 8023, 452, 4323, 5201, 8328, 7345, 993, 8111, 2895,
  2897, 2891, 2892, 2883, 2899, 5374, 1266, 2898, 2893, 5055, 1441, 6097, 6103,
  6101, 6096, 6102, 1423, 8074, 8275, 4253, 2261, 3481, 4324, 8009, 2139, 8024,
  8013, 8021, 8006, 7294, 7295, 1714, 9157, 9154, 9167, 1112, 11557, 1196, 130,
  9097, 6106, 3886, 10616, 9499, 9387, 9393, 9385, 9392, 9394, 5841, 11559,
  10601, 4047, 5743, 2698, 11851, 11844, 11846, 11143, 11892, 11902, 11904,
  11885, 11855, 11859, 11873, 11899, 11866, 11893, 11848, 11843, 11868, 11889,
  11796, 11798, 11808, 11789, 11772, 11828, 11830, 11829, 11826, 11825, 11890,
  11858, 6152, 11865, 825, 11319, 11321, 2692, 3011, 6890, 2783, 2406, 7498,
  7953, 11703, 672, 2534, 11802, 11898, 11886, 11888, 11880, 11883, 11878,
  11872, 11871, 11863, 11864, 11857, 11854, 11901, 11763, 11756, 11819, 11815,
  11163, 11168, 11161, 11165, 11166, 11170, 2345, 11306, 10324, 6824, 7009,
  11048, 3914, 7384, 8797, 11563, 11705, 6308, 1237, 10266, 2971, 11377, 7803,
  5961, 3915, 1198, 10365, 1789, 3913, 7482, 11770, 11812, 11809, 11803, 11800,
  11794, 11792, 11785, 11782, 11779, 11806, 11822, 11774, 11813, 11827, 11761,
  11804, 11821, 11817, 11768, 11758, 11833, 11837, 11778, 11823, 11838, 11835,
  10486, 2966, 1260, 6716, 4481, 6648, 6227, 6177, 6175, 6173, 5464, 5386,
  11051, 5380, 4463, 5155, 5065, 4869, 4856, 4811, 4371, 3007, 2308, 1752,
  11356, 11655, 9926, 11959, 11957, 2306, 11953, 6415, 4469, 2304, 690, 9516,
  9510, 9513, 10384, 9505, 9501, 10332, 9503, 9498, 9496, 9512, 9494, 9490,
  7497, 8201, 7936, 10263, 7499, 6813, 6726, 6722, 10383, 10845, 10847, 10912,
  10981, 11049, 11955, 11964, 4384, 5461, 5452, 5450, 5443, 5441, 5438, 5436,
  5434, 5431, 5429, 5427, 5420, 5415, 5413, 5411, 5408, 5458, 5440, 5404, 5400,
  5397, 5392, 5166, 5153, 1819, 1817, 1261, 11487, 5459, 5463, 1898, 5466, 1619,
  1415, 1208, 1206, 1204, 967, 12063, 310, 10728, 10710, 10340, 7878, 7876,
  10341, 7871, 6230, 5499, 5497, 5495, 5492, 5491, 5489, 5487, 5485, 5482, 5478,
  5470, 9514, 9518, 9520, 994, 8493, 1637, 7687, 3144, 623, 422, 47, 459, 608,
  9678, 9374, 7213, 7900, 6314, 7383, 10393, 2781, 2198, 2518, 9854, 235, 7575,
  10588, 4503, 10613, 4520, 4513, 5326, 6365, 9532, 8305, 8381, 140, 2982,
  10475, 8288, 2462, 2458, 2457, 10840, 10284, 2532, 815, 6049, 6052, 6051,
  10441, 9672, 8478, 4361, 11037, 8727, 9655, 9657, 9653, 1838, 2514, 2126,
  10589, 7566, 4490, 4496, 4446, 991, 10522, 4275, 4453, 3848, 6331, 4451,
  10729, 3478, 4458, 4475, 7758, 424, 10246, 10622, 10247, 10760, 11064, 6877,
  8143, 9530, 9528, 9526, 9524, 9522, 9531, 4440, 8258, 1931, 5641, 7435, 6774,
  10614, 9853, 4495, 7436, 8490, 6849, 6703, 6715, 10954, 6875, 11071, 10338,
  4949, 6708, 4919, 9537, 9535, 9539, 7193, 10623, 3366, 4439, 1810, 3364, 6270,
  2871, 5651, 4803, 4272, 3847, 6328, 10878, 5051, 6403, 8651, 5114, 1565, 313,
  1983, 264, 6876, 9529, 990, 9527, 9525, 9523, 9521, 9515, 10245, 9519, 9517,
  9511, 9509, 9508, 9507, 9506, 10521, 6398, 9500, 4988, 10587, 11012, 4366,
  649, 5162, 6705, 8450, 6700, 9595, 7964, 5620, 7012, 4769, 10923, 6396, 9536,
  4947, 9534, 7192, 9540, 3365, 1809, 9533, 6397, 6400, 8107, 231, 7947, 9504,
  10382, 9301, 1205, 4974, 4859, 4844, 4788, 3019, 3004, 2307, 2305, 2228, 1636,
  1463, 1387, 1207, 1203, 5174, 401, 148, 10877, 10684, 10339, 7877, 10709,
  7875, 6236, 5622, 5498, 5496, 5494, 5150, 11052, 9502, 10982, 9497, 9495,
  9492, 9491, 9263, 8439, 8180, 7500, 10429, 7190, 6810, 6724, 6718, 10846,
  5381, 10911, 10913, 10983, 11050, 7837, 6710, 5378, 6550, 6225, 6176, 6174,
  5638, 5388, 9302, 11010, 5649, 6099, 2896, 2885, 6530, 1777, 8110, 2882, 2900,
  2880, 11106, 6098, 6100, 6104, 6095, 1422, 2889, 8274, 4252, 7261, 5602, 4156,
  8141, 6818, 11073, 4221, 1349, 1849, 179, 10539, 2886, 2884, 6777, 4262, 7360,
  187, 6558, 9096, 6105, 6562, 7001, 8530, 10385, 5324, 8326, 4260, 9389, 4259,
  8112, 4263, 5829, 10359, 1201, 4322, 10485, 5177, 7367, 3542, 10870, 992,
  8113, 2888, 4831, 2474, 10615, 2530, 8448, 1492, 2524, 7817, 588, 2521, 442,
  460, 455, 330, 520, 2520, 2522, 2523, 2901, 2529, 2526, 6364, 6244, 6539,
  5270, 6459, 7282, 1058, 7536, 6395, 198, 7434, 5327, 2965, 2454, 4766, 8291,
  2455, 8289, 4393, 2533, 3994, 1444, 7022, 6302, 632, 1772, 6047, 6048, 8466,
  1451, 8190, 8627, 7462, 3770, 1057, 7278, 2917, 1627, 9656, 9654, 2157, 1788,
  7501, 5490, 10683, 5488, 9312, 702, 3649, 3133, 3660, 3668, 5039, 3681, 3683,
  9292, 9298, 6529, 9306, 9327, 10755, 133, 5145, 8713, 7823, 7826, 3515, 1610,
  137, 7684, 6778, 466, 7391, 2935, 731, 474, 6187, 6659, 3012, 5029, 5032,
  10679, 10677, 399, 10493, 7815, 7534, 6409, 6527, 9477, 7538, 4780, 3505,
  1245, 4659, 1581, 7682, 4660, 7555, 1960, 6407, 5038, 3070, 2187, 2167, 1840,
  1302, 10915, 10471, 5486, 1861, 93, 554, 1353, 2114, 3567, 2022, 1738, 621,
  1563, 9591, 507, 567, 2679, 1025, 4220, 9282, 9283, 10520, 9353, 1453, 1137,
  2225, 832, 6262, 9741, 9740, 1246, 2227, 2711, 1527, 9126, 10598, 2596, 9109,
  2597, 2425, 1638, 9440, 7197, 4961, 553, 6822, 8454, 446, 6654, 8193, 7535,
  4755, 10823, 4845, 7425, 335, 2775, 267, 239, 4785, 6503, 6764, 2974, 6385,
  5027, 5026, 11100, 5393, 1538, 1259, 5471, 1257, 1255, 1253, 1251, 1249, 664,
  584, 421, 163, 61, 7889, 2185, 5421, 5467, 5395, 5448, 5454, 5444, 5417, 5410,
  10730, 10711, 2708, 5474, 5481, 1818, 5163, 223, 5435, 5484, 5479, 5475, 5468,
  5465, 5462, 5460, 5456, 5451, 5446, 5442, 5439, 5437, 5433, 5390, 5430, 5428,
  5422, 5419, 5414, 5412, 5409, 5406, 5405, 5455, 5403, 5398, 5394, 5457, 5418,
  5401, 4686, 3083, 3055, 3087, 3068, 11084, 7182, 6250, 10924, 8659, 8674,
  4691, 4676, 4690, 4680, 5425, 4687, 4689, 4679, 7946, 7890, 10428, 3901, 7891,
  10735, 10716, 10671, 10669, 2301, 3086, 3084, 7816, 6399, 5391, 5476, 5469,
  5445, 5402, 6205, 10603, 2349, 9463, 7739, 1857, 379, 216, 305, 2089, 8205,
  10361, 2423, 2421, 2417, 2415, 2413, 2410, 2408, 2402, 6767, 7945, 113, 935,
  730, 11380, 1815, 592, 8238, 3134, 5186, 12180, 6120, 11982, 11408, 11385,
  2508, 11383, 11381, 12633, 2515, 12636, 12654, 11923, 12481, 12485, 4067,
  11676, 11683, 4088, 1088, 8071, 12242, 1975, 2347, 2780, 12407, 1597, 2700,
  2693, 2696, 7940, 2652, 3916, 8303, 7008, 6660, 2538, 9489, 2404, 2782, 10487,
  2911, 2691, 7167, 3917, 7804, 7802, 10323, 7801, 7806, 10523, 5271, 6516,
  6825, 10928, 12225, 12414, 6153, 4298, 12449, 12478, 12480, 11947, 4530,
  12371, 10378, 7280, 12029, 9242, 6436, 11425, 1561, 241, 12452, 11430, 12132,
  4666, 7039, 7018, 3164, 9352, 5678, 7387, 11270, 11936, 10754, 3835, 12451,
  12454, 12410, 12433, 12412, 12415, 12416, 12418, 12420, 12422, 12424, 12426,
  12429, 12431, 12436, 12443, 12439, 12441, 12457, 12444, 12446, 12483, 12447,
  12475, 12473, 12476, 12470, 12468, 12465, 12463, 12461, 12459, 2697, 7392,
  2420, 10439, 9067, 6532, 4326, 7894, 7892, 7897, 2461, 2513, 3166, 10653,
  8247, 10655, 8211, 8512, 3674, 9297, 2716, 9487, 4700, 10609, 4148, 7028, 42,
  7180, 4708, 2519, 6704, 6357, 2452, 7006, 1829, 3168, 2434, 5147, 4372, 9031,
  1367, 4533, 4527, 10561, 10564, 2341, 2694, 8526, 10440, 8336, 191, 3165,
  2460, 3167, 10747, 2451, 4706, 616, 7276, 6526, 8487, 110, 10748, 2453, 1964,
  2793, 2791, 2967, 2262, 3643, 6856, 6561, 9942, 6873, 6749, 7469, 6479, 8707,
  6874, 4154, 10312, 8379, 2792, 11070, 2259, 7428, 7786, 760, 10451, 4051,
  4536, 2401, 5289, 5322, 5650, 9015, 10272, 9211, 9594, 10680, 2235, 7761,
  8523, 10961, 8524, 4941, 5251, 6393, 2877, 7753, 6801, 10962, 1329, 2695,
  9119, 6296, 8769, 7161, 3884, 8768, 10537, 1435, 1362, 10745, 3641, 6531,
  11019, 746, 11373, 1008, 1748, 11105, 11564, 11257, 4560, 7764, 8684, 11371,
  11125, 4483, 11150, 9579, 1424, 6130, 1601, 4488, 5742, 7463, 4316, 6650,
  5013, 10580, 2682, 2680, 1990, 1338, 10417, 196, 9269, 7382, 4931, 7396,
  11834, 11781, 11773, 11767, 11769, 11795, 11760, 11759, 11818, 11786, 11764,
  11755, 11836, 11839, 11775, 1782, 11805, 11757, 11766, 2987, 246, 11173,
  11178, 11169, 11167, 11164, 4978, 11293, 10759, 2970, 6420, 6154, 1059, 3570,
  9618, 3375, 3376, 2467, 10789, 9172, 8103, 7772, 3383, 1479, 8368, 10715,
  8005, 4398, 8048, 8014, 8016, 8004, 8008, 10853, 7292, 7293, 939, 4302, 4288,
  8337, 9149, 2370, 8094, 1585, 4410, 3380, 2427, 3866, 3381, 5046, 3561, 3503,
  957, 453, 1934, 4417, 1846, 8782, 8780, 4416, 8811, 8818, 8800, 8784, 8777,
  11008, 8825, 4403, 8804, 8785, 4419, 8772, 4418, 11776, 11780, 11783, 10433,
  7567, 7569, 7565, 4206, 1542, 8407, 430, 2602, 11558, 11556, 11738, 9104,
  9038, 6839, 7570, 2495, 7959, 8202, 9105, 5120, 5118, 1559, 11203, 652, 11318,
  11298, 7189, 1939, 4509, 4498, 11787, 3088, 8052, 162, 9642, 11400, 12027,
  5105, 11345, 11954, 11952, 11958, 11960, 11262, 4471, 5594, 4437, 11667,
  11354, 2199, 4473, 4457, 4478, 4472, 4452, 4456, 4450, 4443, 4447, 4436, 8518,
  4846, 4317, 11771, 11870, 11884, 11900, 11887, 11896, 11850, 11847, 11876,
  11897, 11797, 11801, 11790, 11788, 11820, 8479, 11831, 11832, 11765, 11824,
  11762, 11784, 11816, 11814, 11811, 11807, 11799, 11810, 11793, 11841, 11905,
  11906, 11903, 5614, 8043, 11702, 669, 9299, 11917, 11891, 11882, 11881, 11879,
  11877, 11875, 11398, 11874, 11862, 11861, 11856, 11853, 11867, 11860, 11852,
  11840, 11894, 11842, 11845, 11849, 11895, 11159,
];
let breeding_count_breed_counts = [
  4, 4, 4, 4, 4, 4, 4, 4, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
  3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3,
  3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2,
  2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,
  1, 1,
];

const nonceTracker = 1638;

async function main() {
  const [deployer] = await ethers.getSigners();
  const CHUNK_SIZE = 200; // Adjust this value based on gas limits

  console.log("Executing script with the account:", deployer.address);

  const breedingContract = new ethers.Contract(
    abi.sabong_saga_breeding_address,
    abi.sabong_saga_breeding,
    deployer
  );

  // Process breeding cooldown sync
  const cooldownStartTime = Date.now();
  const totalCooldownBatches = Math.ceil(
    breeding_cooldown_token_ids.length / CHUNK_SIZE
  );

  //   console.log("\n🔄 Starting Breeding Cooldown Sync...");
  //   for (let i = 0; i < breeding_cooldown_token_ids.length; i += CHUNK_SIZE) {
  //     const tokenIdsChunk = breeding_cooldown_token_ids.slice(i, i + CHUNK_SIZE);
  //     const timestampsChunk = breeding_cooldown_timestamps.slice(
  //       i,
  //       i + CHUNK_SIZE
  //     );

  //     const batchNumber = Math.floor(i / CHUNK_SIZE) + 1;
  //     printMintingStats(
  //       "Breeding Cooldown",
  //       i + tokenIdsChunk.length,
  //       breeding_cooldown_token_ids.length,
  //       CHUNK_SIZE,
  //       batchNumber,
  //       totalCooldownBatches,
  //       cooldownStartTime
  //     );

  //     const syncBreedingCooldown = await breedingContract.syncBreedingCooldown(
  //       tokenIdsChunk,
  //       timestampsChunk
  //     );
  //     await syncBreedingCooldown.wait();
  //   }

  //   // Process breeding count sync
  //   const countStartTime = Date.now();
  //   const totalCountBatches = Math.ceil(
  //     breeding_count_token_ids.length / CHUNK_SIZE
  //   );

  //   console.log("\n🔄 Starting Breeding Count Sync...");
  //   for (let i = 0; i < breeding_count_token_ids.length; i += CHUNK_SIZE) {
  //     const tokenIdsChunk = breeding_count_token_ids.slice(i, i + CHUNK_SIZE);
  //     const breedCountsChunk = breeding_count_breed_counts.slice(
  //       i,
  //       i + CHUNK_SIZE
  //     );

  //     const batchNumber = Math.floor(i / CHUNK_SIZE) + 1;
  //     printMintingStats(
  //       "Breeding Count",
  //       i + tokenIdsChunk.length,
  //       breeding_count_token_ids.length,
  //       CHUNK_SIZE,
  //       batchNumber,
  //       totalCountBatches,
  //       countStartTime
  //     );

  //     const syncBreedingCount = await breedingContract.syncBreedingCount(
  //       tokenIdsChunk,
  //       breedCountsChunk
  //     );
  //     await syncBreedingCount.wait();
  //   }

  console.log("\n🔄 Starting Nonce Sync...");

  // Create arrays from 1 to nonceTracker
  const nonces: number[] = [];
  const used: boolean[] = [];
  for (let i = 1; i <= nonceTracker; i++) {
    nonces.push(i);
    used.push(true); // Mark all historical nonces as used
  }

  const totalNonceBatches = Math.ceil(nonces.length / CHUNK_SIZE);
  const nonceStartTime = Date.now();

  for (let i = 0; i < nonces.length; i += CHUNK_SIZE) {
    const noncesChunk = nonces.slice(i, i + CHUNK_SIZE);
    const usedChunk = used.slice(i, i + CHUNK_SIZE);

    const batchNumber = Math.floor(i / CHUNK_SIZE) + 1;
    printMintingStats(
      "Nonce",
      i + noncesChunk.length,
      nonces.length,
      CHUNK_SIZE,
      batchNumber,
      totalNonceBatches,
      nonceStartTime
    );

    const syncUsedNonces = await breedingContract.syncUsedNonces(
      noncesChunk,
      usedChunk,
      nonceTracker
    );
    await syncUsedNonces.wait();
  }

  console.log("\n✅ Sync completed successfully!");
  console.log(
    `Total Cooldown Records Processed: ${breeding_cooldown_token_ids.length}`
  );
  console.log(
    `Total Count Records Processed: ${breeding_count_token_ids.length}`
  );
  console.log(`Total Nonce Records Processed: ${nonceTracker}`);
  console.log(
    `Total Time Elapsed: ${((Date.now() - cooldownStartTime) / 1000).toFixed(
      1
    )}s`
  );
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
