"use client";

import { cn } from "ui";
import { User, UserCheck, Users, DollarSign } from "lucide-react";
import {
  ERewardDistributionType,
  REWARD_DISTRIBUTION_LABELS,
} from "../../types/delegation.types";

interface IRewardDistributionSelectorProps {
  value: ERewardDistributionType;
  onChange: (value: ERewardDistributionType) => void;
  disabled?: boolean;
  className?: string;
}

export function RewardDistributionSelector({
  value,
  onChange,
  disabled = false,
  className,
}: IRewardDistributionSelectorProps) {
  const allOptions = [
    {
      value: ERewardDistributionType.DELEGATOR_ONLY,
      label: "Owner",
      description: "You keep all rewards earned by the chicken",
      icon: User,
      isPopular: false,
      advantages: ["Maximum earnings for you", "Simple arrangement"],
      considerations: ["May be less attractive to renters"],
      disabled: false,
    },
    {
      value: ERewardDistributionType.DELEGATEE_ONLY,
      label: "Renter",
      description: "<PERSON><PERSON> keeps all rewards earned by the chicken",
      icon: User<PERSON>he<PERSON>,
      isPopular: false,
      advantages: ["Very attractive to renters", "Higher rental demand"],
      considerations: ["No reward earnings for you"],
      disabled: false,
    },
    {
      value: ERewardDistributionType.SHARED,
      label: "Shared",
      description: "Split rewards between you and the renter",
      icon: Users,
      isPopular: true,
      advantages: ["Balanced incentives", "Attractive to both parties"],
      considerations: ["Requires feather amount configuration"],
      disabled: false,
    },
  ];

  // Filter out disabled options
  const options = allOptions.filter((option) => !option.disabled);

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-white mb-4">
        Reward Distribution
      </label>
      <p className="text-sm text-gray-400 mb-6">
        Choose how rewards earned by your chicken will be distributed
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {options.map((option) => {
          const Icon = option.icon;
          const isSelected = value === option.value;

          return (
            <div
              key={option.value}
              onClick={() => !disabled && onChange(option.value)}
              className={cn(
                "relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 min-h-[280px]",
                disabled && "opacity-50 cursor-not-allowed",
                isSelected
                  ? "border-green-500 bg-green-500/10"
                  : "border-stone-600 bg-stone-800 hover:border-stone-500 hover:bg-stone-700",
                !disabled && "hover:scale-[1.02]"
              )}
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-3">
                <div
                  className={cn(
                    "p-2 rounded-lg",
                    isSelected
                      ? "bg-green-500/20 text-green-400"
                      : "bg-stone-700 text-gray-400"
                  )}
                >
                  <Icon className="w-5 h-5" />
                </div>

                <div className="flex gap-1">
                  {isSelected && (
                    <span className="px-2 py-0.5 text-xs font-medium bg-yellow-500/20 text-yellow-400 rounded-full">
                      Selected
                    </span>
                  )}
                  {option.isPopular && (
                    <span className="px-2 py-0.5 text-xs font-medium bg-green-500/20 text-green-400 rounded-full">
                      Popular
                    </span>
                  )}
                </div>
              </div>

              {/* Title and Description */}
              <h3 className="font-medium text-white mb-2">{option.label}</h3>
              <p className="text-sm text-gray-400 mb-4">{option.description}</p>

              {/* Advantages */}
              <div className="mb-3">
                <h4 className="text-xs font-medium text-green-400 mb-2">
                  Advantages:
                </h4>
                <ul className="space-y-1">
                  {option.advantages.map((advantage, index) => (
                    <li
                      key={index}
                      className="text-xs text-gray-300 flex items-start gap-1"
                    >
                      <span className="text-green-400 mt-0.5">•</span>
                      {advantage}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Considerations */}
              <div>
                <h4 className="text-xs font-medium text-red-400 mb-2">
                  Considerations:
                </h4>
                <ul className="space-y-1">
                  {option.considerations.map((consideration, index) => (
                    <li
                      key={index}
                      className="text-xs text-gray-300 flex items-start gap-1"
                    >
                      <span className="text-red-400 mt-0.5">•</span>
                      {consideration}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
