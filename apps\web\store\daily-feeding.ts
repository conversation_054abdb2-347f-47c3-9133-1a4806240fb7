import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import {
  UserRejectedRequestError,
  ContractFunctionExecutionError,
  Address,
} from "viem";
import { create } from "zustand";
import erc1155Abi from "@/abi/Erc1155.abi.json";
import { ronin, saigon } from "viem/chains";
import dailyFeedAbi from "@/abi/DailyFeed.abi.json";
import useFoodCraftingStore from "./food-crafting";
import { delay } from "@/utils/delay";

type DailyFeedState = {
  isPending: boolean;
  approvingResources: boolean;
  approvedResources: boolean;
};

type Actions = {
  feedChicken: (
    collectionId: 0 | 1,
    tokenId: number,
    itemId: number,
    amount: number
  ) => Promise<void>;
  approveResources: () => Promise<void>;
  checkApproval: () => Promise<boolean>;
  clearStore: () => void;
};

type StoreState = DailyFeedState & Actions;

const ADDRESSES = {
  RESOURCES_TOKEN: process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address,
  DAILY_FEED: process.env.NEXT_PUBLIC_DAILY_FEED_CONTRACT as Address,
};

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const chain = CHAIN_ID === 2020 ? ronin : saigon;

const handleError = (
  error: unknown,
  defaultMessage: string,
  operation: string
) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }
  if (error instanceof ContractFunctionExecutionError) {
    toast.error(error.name, {
      description: error.shortMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }
  } else {
    const err = error as Error;

    // Fallback for non-Error objects
    toast.error(err.name, {
      description: err.message,
      position: "top-right",
    });
    throw err;
  }
};

const initialState = {
  isPending: false,
  approvingResources: false,
  approvedResources: false,
};

const useDailyFeedStore = create<StoreState>()((set, get) => ({
  ...initialState,
  feedChicken: async (collectionId, tokenId, itemId, amount) => {
    const stateContext = window.stateContext;
    if (!stateContext) {
      toast.error("Cannot craft cookie", {
        description: "State context not available",
        position: "top-right",
      });
      return;
    }

    const { address, publicClient, walletClient, ConnectRecentWallet } =
      stateContext;
    const { fetchFoodBalance } = useFoodCraftingStore.getState();
    await ConnectRecentWallet();
    const { approvedResources, approveResources } = get();

    try {
      set({ isPending: true });
      if (!approvedResources) {
        await approveResources();
      }

      const simulateReq = await publicClient.simulateContract({
        address: ADDRESSES.DAILY_FEED,
        abi: dailyFeedAbi,
        functionName: "feedChicken",
        args: [collectionId, tokenId, itemId, amount],
        chain,
        account: address,
      });

      //   const gasEstimate = await publicClient.estimateContractGas(
      //     simulateReq.request
      //   );
      //   const gasWithBuffer = (gasEstimate * 115n) / 100n;

      //   simulateReq.request.gas = gasWithBuffer;

      const hash = await walletClient!.writeContract(simulateReq.request);
      toast.info("Daily feed transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });
      await delay(3000);

      // Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });
      if (receipt.status === "success") {
        // Refresh balances after successful mint
        await Promise.all([fetchFoodBalance(), get().checkApproval()]);
      } else {
        toast.error("Daily feed transaction failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-center",
        });
        throw Error("Daily feed transaction failed");
      }
    } catch (error) {
      handleError(error, "Failed to daily feed", "daily feed");
    } finally {
      set({ isPending: false });
    }
  },
  approveResources: async () => {
    // Get state context values at call time, not store creation time
    const stateContext = window.stateContext;
    if (!stateContext) {
      toast.error("Cannot approve ERC1155", {
        description: "State context not available",
        position: "top-right",
      });
      return;
    }

    const { address, publicClient, walletClient, ConnectRecentWallet } =
      stateContext;

    try {
      await ConnectRecentWallet();

      if (!address || !walletClient) {
        toast.error("Cannot approve ERC1155", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return;
      }

      set({ approvingResources: true });
      toast.info("Preparing approval transaction...", {
        description: "Setting approval for all Resources tokens",
        position: "top-right",
      });

      // Get the current chain

      // Set approval for all for ERC1155 token
      const hash = await walletClient.writeContract({
        address: ADDRESSES.RESOURCES_TOKEN,
        abi: erc1155Abi,
        functionName: "setApprovalForAll",
        args: [ADDRESSES.DAILY_FEED, true],
        chain, // Explicitly specify the chain
        account: address, // Explicitly specify the account
      });

      toast.info("Approval transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });

      // Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        toast.success("ERC1155 approval successful", {
          description:
            "Successfully approved all Resources tokens for spending",
          position: "top-right",
        });

        set({ approvedResources: true });
      } else {
        toast.error("ERC1155 approval failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-right",
        });
      }
    } catch (error) {
      handleError(error, "Failed to approve ERC1155 token", "ERC1155 approval");
    } finally {
      set({ approvingResources: false });
    }
  },
  checkApproval: async () => {
    const stateContext = window.stateContext;
    if (!stateContext || !stateContext.address) {
      return false; // Always return a boolean
    }
    const { address, publicClient } = stateContext;

    try {
      const isApprove = await publicClient?.readContract({
        address: ADDRESSES.RESOURCES_TOKEN,
        abi: erc1155Abi,
        functionName: "isApprovedForAll",
        args: [address as Address, ADDRESSES.DAILY_FEED],
      });
      set({ approvedResources: Boolean(isApprove) });
      return Boolean(isApprove); // Always return a boolean
    } catch (error) {
      handleError(error, "Failed to fetch approval", "daily feed");
      set({ approvedResources: false });
      return false;
    }
  },
  clearStore: () => {
    set({ ...initialState });
  },
}));

export default useDailyFeedStore;
