#!/bin/bash

# Exit on any error
set -e

# Configuration
DEPLOY_PATH="./deployment"
BACKUP_PATH="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Logger function
log() {
    echo -e "${GREEN}[DEPLOY]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Check required commands
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error "$1 is not installed. Please install it first. $2"
    fi
}

# Check all required commands
check_command "pm2" "Install using: npm install -g pm2"
check_command "yarn" "Install using: npm install -g yarn"
check_command "unzip" "Install using: sudo apt-get install unzip"
check_command "tar" "Install using: sudo apt-get install tar"

# Check if .env file exists
if [ ! -f ".env" ]; then
    error ".env file not found. Please configure your environment variables"
fi

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_PATH"

# Backup tmp folder from current deployment if exists
# if [ -d "$DEPLOY_PATH/tmp" ]; then
#     log "Creating backup of tmp folder from current deployment..."
#     tar -czf "$BACKUP_PATH/tmp_backup_$TIMESTAMP.tar.gz" -C "$DEPLOY_PATH" tmp
# fi

# Build the application
log "Installing dependencies..."
yarn || error "Failed to install dependencies"

log "Building application..."
yarn build || error "Failed to build application"


# Deploy to production
log "Deploying to production..."
mkdir -p "$DEPLOY_PATH" || error "Failed to create deployment directory"

# Copy files while preserving tmp directory
log "Copying files to deployment directory..."
for item in build/.[!.]* build/*; do
    # Skip if no files match the pattern
    [ -e "$item" ] || continue
    
    base_name=$(basename "$item")
    if [ "$base_name" != "tmp" ]; then
        rm -rf "$DEPLOY_PATH/$base_name"
        cp -r "$item" "$DEPLOY_PATH/" || error "Failed to copy $base_name to deployment directory"
    fi
done

# Setup deployment directory
log "Setting up deployment directory..."
cp .env "$DEPLOY_PATH/.env" || error "Failed to copy .env file"
mkdir -p "$DEPLOY_PATH/tmp/uploads" || error "Failed to create tmp directory"

# Create PM2 ecosystem file
log "Creating PM2 ecosystem file..."
cat > "$DEPLOY_PATH/ecosystem.config.js" << EOL
 module.exports = {
  apps: [
    {
      name: "breeding-api",
      script: "./server.js",
      instances: "1",
      exec_mode: "cluster",
      autorestart: true,
    },
  ],
}; 
EOL

# Extract metadata if exists
if [ -f "metadata.zip" ]; then
    log "Extracting metadata..."
    unzip -o -q metadata.zip -d "$DEPLOY_PATH/" || error "Failed to extract metadata"
else
    error "metadata.zip not found"
fi

# Copy layers directory
if [ -d "feature/hashlips/layers" ]; then
    log "Copying layers directory..."
    mkdir -p "$DEPLOY_PATH/feature/hashlips/"
    cp -r "feature/hashlips/layers" "$DEPLOY_PATH/feature/hashlips/" || error "Failed to copy layers directory"
else
    error "feature/hashlips/layers directory not found"
fi

# Install dependencies in deployment folder
log "Installing dependencies in deployment folder..."
cd "$DEPLOY_PATH" || error "Failed to change to deployment directory"
yarn install || error "Failed to install dependencies in deployment folder"

# Run database migrations
log "Running database migrations..."
node ace migration:run || error "Failed to run database migrations"
