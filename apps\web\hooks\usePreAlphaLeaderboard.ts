// hooks/usePreAlphaleaderboard.ts
import { useState, useEffect } from "react";

export function usePreAlphaLeaderboard<T>(
  type: string,
  page: number = 1,
  limit: number = 20,
  additionalParams: Record<string, string | undefined> = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams();
        params.set("page", page.toString());
        params.set("limit", limit.toString());

        // Add any additional parameters
        Object.entries(additionalParams).forEach(([key, value]) => {
          if (value !== undefined) {
            params.set(key, value);
          }
        });

        const response = await fetch(
          `https://chicken-api-ivory.vercel.app/api/stats/prealpha/leaderboard/${type}?${params.toString()}`
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err : new Error(String(err)));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [type, page, limit, JSON.stringify(additionalParams)]);

  return { data, isLoading, error };
}
