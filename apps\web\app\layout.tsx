import { isHideDevTools } from "@/lib/constants";
import Web3Provider from "@/providers/web3/web3-provider";
import { GoogleAnalytics } from "@next/third-parties/google";
import type { Metadata } from "next";
import localFont from "next/font/local";
import { Poppins } from "next/font/google";
import "./globals.css";

const arcadia = localFont({
  src: "../public/fonts/Arcadia.woff",
  display: "swap",
  variable: "--font-arcadia",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  variable: "--font-poppins",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Sabong Saga",
  description:
    "Welcome to Sabong Saga, a blockchain game that merges the excitement of traditional cockfighting with the innovation of Web3. In Sabong Saga, players can collect, breed, battle, and trade digital roosters.Real-life cockfighting is harmful and unnecessary, causing animal suffering for sport. Sabong Saga offers a humane alternative, channeling the competitive spirit of cockfighting into a digital, cruelty-free game that preserves the thrill—without harming any chickens. Save chicken lives, play Sabong Saga! ",
  metadataBase: new URL(
    process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
  ),
  openGraph: {
    images: "/images/sabongsaga-poster.jpg",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sabong Saga",
    description:
      "Welcome to Sabong Saga, a blockchain game that merges the excitement of traditional cockfighting with the innovation of Web3. In Sabong Saga, players can collect, breed, battle, and trade digital roosters.Real-life cockfighting is harmful and unnecessary, causing animal suffering for sport. Sabong Saga offers a humane alternative, channeling the competitive spirit of cockfighting into a digital, cruelty-free game that preserves the thrill—without harming any chickens. Save chicken lives, play Sabong Saga! ",
    images: ["/images/sabongsaga-poster.jpg"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className="dark">
      <head>
        {!isHideDevTools && process.env.NODE_ENV === "development" && (
          <script
            src="https://unpkg.com/react-scan/dist/auto.global.js"
            async
          />
        )}
      </head>
      <body className={`${arcadia.variable} ${poppins.variable} antialiased`}>
        <Web3Provider>{children}</Web3Provider>
      </body>
      <GoogleAnalytics gaId="GTM-5R8MRRXJ" />
    </html>
  );
}
