export * from "./token-transfer";

export interface SignMessage {
  version: string;
  chainId: number;
  nonce: string;
  statement: string;
  domain: string;
  address: string;
  uri: string;
}

export interface NonceData {
  nonce: string;
  timestamp: number;
}

export interface AuthConfig {
  jwtSecret: string;
  refreshJwtSecret: string;
  signMessage?: SignMessage;
  expiresIn?: number;
  redisUrl?: string;
  refreshTokenExpiresIn?: number;
}

export interface JWTPayload {
  address: string;
  exp: number;
}

export interface ApiResponse<T> {
  status: boolean;
  responseCode: number;
  data?: T;
  message: string;
  errors?: string[] | Record<string, string[]>;
}

export interface TokenPayload {
  address: string;
  exp: number;
  tokenType: "access" | "refresh";
}

export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public errors?: string[] | Record<string, string[]>
  ) {
    super(message);
    this.name = "AppError";
  }
}

export interface DailyFeathersData {
  tokenId: string;
  image?: string;
  dailyFeathers?: number;
  legendaryCount?: number;
  renterAddress?: string;
  ownerAddress?: string;
  delegatedTask?: DelegatedTaskType;
  rewardDistribution?: RewardDistributionType;
  sharedRewardAmount?: number;
  rubStreakBenefactor?: RubStreakBenefactorType;
}

export enum RewardDistributionType {
  DELEGATOR_ONLY = 1,
  DELEGATEE_ONLY = 2,
  SHARED = 3,
}

export enum DelegatedTaskType {
  DAILY_RUB = 1,
  GAMEPLAY = 2,
  BOTH = 3,
}

export enum RubStreakBenefactorType {
  DELEGATOR = 1,
  DELEGATEE = 2,
}

export interface RentedNFT {
  delegatedTask: DelegatedTaskType;
  rewardDistribution: RewardDistributionType;
  sharedRewardAmount: number;
  renterAddress: string;
  ownerAddress: string;
  tokenId: string;
  image?: string;
  dailyFeathers?: number;
  legendaryCount?: number;
  rubStreakBenefactor?: RubStreakBenefactorType;
}

// Interface for result data (extends input with item drop result)
export interface LegendaryFeathersDropResult extends DailyFeathersData {
  itemsDropped: number;
}
