import { ethers } from "hardhat";
import abi from "./abi.json";
const signer = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Executing script with the account:", deployer.address);

  const legacyContract = new ethers.Contract(
    abi.sabong_saga_legacy_address,
    abi.sabong_saga_legacy_chickens,
    deployer
  );
  await legacyContract.grantRole(
    ethers.keccak256(ethers.toUtf8Bytes("MINTER_ROLE")),
    abi.sabong_saga_breeding_address
  );
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
