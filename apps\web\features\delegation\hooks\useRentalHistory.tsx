"use client";

import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  IRentalWithMetadata,
  IRentalHistoryResponse,
  IRental,
} from "../types/delegation.types";
import { DelegationAPI } from "../api/delegation.api";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useStateContext } from "@/providers/app/state";

// Helper function to create rental with real metadata
const createRentalWithRealMetadata = (
  rental: IRental,
  metadataMap: Record<number, IChickenMetadata>
): IRentalWithMetadata => {
  const metadata = metadataMap[rental.chickenTokenId];

  // Extract daily feathers and legendary count from metadata attributes
  const dailyFeathersAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Daily Feathers"
  );
  const legendaryCountAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Legendary Count"
  );

  const dailyFeathers = dailyFeathersAttr?.value
    ? typeof dailyFeathersAttr.value === "string"
      ? parseInt(dailyFeathersAttr.value)
      : Number(dailyFeathersAttr.value)
    : 0;

  const legendaryCount = legendaryCountAttr?.value
    ? typeof legendaryCountAttr.value === "string"
      ? parseInt(legendaryCountAttr.value)
      : Number(legendaryCountAttr.value)
    : 0;

  return {
    ...rental,
    chickenMetadata: metadata,
    dailyFeathers,
    legendaryCount,
  };
};

// Real API function
const fetchRentalHistory = async (
  page: number = 1,
  pageSize: number = 10
): Promise<IRentalHistoryResponse> => {
  return await DelegationAPI.getRentalHistory(page, pageSize);
};

export function useRentalHistory(pageSize: number = 12) {
  const { address, isConnected } = useStateContext();
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch rental history
  const {
    data: historyResponse,
    isLoading: isLoadingHistory,
    error,
    refetch,
  } = useQuery({
    queryKey: ["rental-history", address, currentPage, pageSize],
    queryFn: () => fetchRentalHistory(currentPage, pageSize),
    enabled: !!address && isConnected,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from rental history for metadata fetching
  const tokenIds = useMemo(() => {
    if (!historyResponse?.data?.data) return [];
    return historyResponse.data.data.map((rental) => rental.chickenTokenId);
  }, [historyResponse?.data?.data]);

  // Fetch chicken metadata for all rentals
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Combine rental data with metadata
  const historyWithMetadata = useMemo(() => {
    if (!historyResponse?.data?.data || !metadataMap) return [];

    return historyResponse.data.data.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );
  }, [historyResponse?.data?.data, metadataMap]);

  // Pagination helpers
  const pagination = useMemo(() => {
    if (!historyResponse?.data?.meta) return null;

    const meta = historyResponse.data.meta;
    return {
      currentPage: meta.currentPage,
      totalPages: meta.lastPage,
      totalItems: meta.total,
      itemsPerPage: meta.perPage,
      hasNextPage: meta.nextPageUrl !== null,
      hasPreviousPage: meta.previousPageUrl !== null,
    };
  }, [historyResponse?.data?.meta]);

  // Navigation functions
  const goToPage = (page: number) => {
    if (pagination && page >= 1 && page <= pagination.totalPages) {
      setCurrentPage(page);
    }
  };

  const goToNextPage = () => {
    if (pagination?.hasNextPage) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPreviousPage = () => {
    if (pagination?.hasPreviousPage) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToFirstPage = () => {
    setCurrentPage(1);
  };

  const goToLastPage = () => {
    if (pagination) {
      setCurrentPage(pagination.totalPages);
    }
  };

  // Combined loading state
  const isLoading = isLoadingHistory || isLoadingMetadata;

  return {
    // Data
    history: historyWithMetadata,
    pagination,

    // Loading states
    isLoading,

    // Error states
    error: error || metadataError,

    // Actions
    refetch,

    // Pagination controls
    currentPage,
    goToPage,
    goToNextPage,
    goToPreviousPage,
    goToFirstPage,
    goToLastPage,
  };
}
