import { ethers } from "hardhat";
import abi from "./abi.json";
const signer = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);

  const accountBalance = await deployer.provider.getBalance(deployer.address);

  console.log("Account balance:", accountBalance.toString());
  const SabongSagaBreeding = await ethers.getContractFactory(
    "SabongSagaBreeding"
  );
  const sabongSagaBreeding = await SabongSagaBreeding.deploy(
    abi.sabong_saga_cock_address,
    abi.sabong_saga_genesis_address,
    abi.sabong_saga_legacy_address,
    abi.sabong_saga_items_address,
    deployer.address,
    signer
  );

  await sabongSagaBreeding.waitForDeployment();
  const sabongSagaBreedingAddress = await sabongSagaBreeding.getAddress();
  console.log("sabongSagaBreedingAddress:", sabongSagaBreedingAddress);
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
