"use client";

import { hookstate, useHookstate } from "@hookstate/core";
import { devtools } from "@hookstate/devtools";

const initialState = {
  breeding: {
    tab: "breeding" as "breeding" | "hatching",
  },
  loading: false,
  loadingMessage: 'Loading'
};

const globalState = hookstate(initialState, devtools({ key: "globalState" }));

export const useGlobalState = () => useHookstate(globalState);

export default globalState;
