"use client";

import { cn } from "@/components/ui";
import { LoadingIcon } from "../icons/loading.icon";
import LoadingDots from "./loading-dots";

interface ILoadingScreenProps {
  message?: string;
  className?: string;
}

export default function LoadingScreen({
  message = "Loading",
  className = "",
}: ILoadingScreenProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 flex items-center justify-center bg-black/70 backdrop-blur-sm px-4",
        className
      )}
    >
      <div>
        <LoadingIcon className="mx-auto size-6 animate-spin text-amber-500" />
        <div className="mt-2 flex items-center">
          <div>{message}</div>
          <LoadingDots dots="." />
        </div>
      </div>
    </div>
  );
}
