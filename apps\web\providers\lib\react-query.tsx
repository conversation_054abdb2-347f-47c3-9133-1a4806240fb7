"use client";

import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { hashFn } from "wagmi/query";
import { isHideDevTools } from "@/lib/constants";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryKeyHashFn: hashFn,
    },
  },
});

interface IReactQueryProviderProps {
  children: React.ReactNode;
}

const ReactQueryProvider = ({ children }: IReactQueryProviderProps) => {
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {!isHideDevTools && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  );
};

export default ReactQueryProvider;
