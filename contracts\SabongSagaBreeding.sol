// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "./ERC721Common.sol";
import "./ERC20Common.sol";
import "./ERC1155Common.sol";
import "./SabongSagaChickens.sol";

contract SabongSagaBreeding is Ownable, ReentrancyGuard {
    // Errors
    error BreedTime();
    error InvalidArrayLength();
    error ERC1155InsufficientBalance();
    error ERC20InsufficientBalance();
    error InsufficientNinunoBalance();
    error NonceAlreadyUsed();
    error InvalidParents();
    error InvalidSignature();
    error UnauthorizedOwner();
    error WithdrawalRequestIdAlreadyUsed();

    // Events
    event Breed(
        uint256 indexed chickenLeftTokenId,
        uint256 indexed chickenRightTokenId,
        uint256 indexed newTokenId,
        uint256 amountToNinuno,
        uint256[][] feathersData, // [[tokenId, amount], ...]
        uint256[][] resourcesData // [[index, tokenId, amount], ...]
    );

    event BatchBreed(
        uint256[] indexed chickenLeftTokenIds,
        uint256[] indexed chickenRightTokenIds,
        uint256[] newTokenIds,
        uint256[][][] feathersData, // [[[tokenId, amount], ...], ...]
        uint256[][][] resourcesData // [[[index, tokenId, amount], ...], ...]
    );

    event NinunoBalanceClaimed(
        address indexed user,
        uint256 indexed tokenId,
        uint256 indexed withdrawalRequestId,
        uint256 amount
    );

    // Create a struct to hold breeding parameters
    struct BreedingParams {
        uint256 chickenLeftTokenId;
        uint256 chickenRightTokenId;
        uint256 totalAmount;
        uint256 amountToVault;
        uint256 amountToNinuno;
        uint256 breedingCooldownTime;
        uint256[][] feathersData; // [[tokenId, amount], ...]
        uint256[][] resourcesData; // [[index, tokenId, amount], ...]
    }

    struct BatchBreedingParams {
        uint256[] chickenLeftTokenIds;
        uint256[] chickenRightTokenIds;
        uint256[] totalAmounts;
        uint256[] amountsToVault;
        uint256[] amountsToNinuno;
        uint256[] breedingCooldownTimes;
        uint256[][][] feathersData; // [[[tokenId, amount], ...], ...]
        uint256[][][] resourcesData; // [[[index, tokenId, amount], ...], ...]
        bytes[] signatures;
    }

    // External contracts
    ERC721Common public immutable genesis;
    SabongSagaChickens public immutable legacy;
    ERC20Common public immutable cock;
    ERC1155Common public immutable feathers;
    ERC1155Common public immutable resources;

    address public treasury;

    // Mappings
    mapping(uint256 => uint256) chickenBreedTime;
    mapping(uint256 => uint256) chickenBreedCount;

    uint256 public nonceTracker;
    mapping(uint256 => bool) usedNonces;
    mapping(uint256 => bool) usedWithdrawalRequestIds;

    address public signer;

    constructor(
        address _cockAddress,
        address _genesisAddress,
        address _legacyAddress,
        address _feathersAddress,
        address _resourcesAddress,
        address _treasury,
        address _signer
    ) Ownable(msg.sender) {
        cock = ERC20Common(_cockAddress);
        genesis = ERC721Common(_genesisAddress);
        legacy = SabongSagaChickens(_legacyAddress);
        feathers = ERC1155Common(_feathersAddress);
        resources = ERC1155Common(_resourcesAddress);
        treasury = _treasury;
        signer = _signer;
    }

    function breed(
        uint256 chickenLeftTokenId,
        uint256 chickenRightTokenId,
        uint256 _totalAmount,
        uint256 _amountToVault,
        uint256 _amountToNinuno,
        uint256 _breedingCooldownTime,
        uint256[][] calldata _feathersData,
        uint256[][] calldata _resourcesData,
        bytes calldata _sig
    ) external nonReentrant {
        BreedingParams memory params = BreedingParams({
            chickenLeftTokenId: chickenLeftTokenId,
            chickenRightTokenId: chickenRightTokenId,
            totalAmount: _totalAmount,
            breedingCooldownTime: _breedingCooldownTime,
            amountToVault: _amountToVault,
            amountToNinuno: _amountToNinuno,
            feathersData: _feathersData,
            resourcesData: _resourcesData
        });

        _validateBreedingSignature(
            params.chickenLeftTokenId,
            params.chickenRightTokenId,
            params.totalAmount,
            params.amountToVault,
            params.amountToNinuno,
            params.breedingCooldownTime,
            _feathersData,
            _resourcesData,
            _sig
        );

        // --- Validation Checks ---
        if (params.chickenLeftTokenId == params.chickenRightTokenId)
            revert InvalidParents();
        if (usedNonces[nonceTracker]) revert NonceAlreadyUsed();
        if (cock.balanceOf(msg.sender) < params.totalAmount)
            revert ERC20InsufficientBalance();

        _processBreeding(params);
    }

    function _processBreeding(
        BreedingParams memory params
    ) private returns (uint256) {
        // --- State Preparation ---
        uint256 newTokenId = legacy.mint(msg.sender);

        // --- Handle Parents ---
        _handleChicken(
            params.chickenLeftTokenId,
            block.timestamp,
            params.breedingCooldownTime
        );
        _handleChicken(
            params.chickenRightTokenId,
            block.timestamp,
            params.breedingCooldownTime
        );

        // --- Burn Feathers and Resources ---
        _burnFeathers(params.feathersData);
        _burnResources(params.resourcesData);

        // --- Token Transfers ---
        _cockTransfer(params.totalAmount, params.amountToVault);

        // --- Nonce Update ---
        _updateNonce();

        // --- Emit Event ---
        emit Breed(
            params.chickenLeftTokenId,
            params.chickenRightTokenId,
            newTokenId,
            params.amountToNinuno,
            params.feathersData,
            params.resourcesData
        );

        return newTokenId;
    }

    function _handleChicken(
        uint256 tokenId,
        uint256 currentTime,
        uint256 breedingCooldownTime
    ) internal {
        // Check if still in cooldown period
        if (chickenBreedTime[tokenId] >= currentTime) revert BreedTime();

        // Ownership verification (single branch approach)
        address owner = isGenesis(tokenId)
            ? genesis.ownerOf(tokenId)
            : legacy.ownerOf(tokenId);
        if (owner != msg.sender) revert UnauthorizedOwner();

        // Update breeding time and increment count
        unchecked {
            chickenBreedTime[tokenId] = currentTime + breedingCooldownTime;
            chickenBreedCount[tokenId] += 1;
        }
    }

    function _burnFeathers(uint256[][] memory _feathersData) internal {
        uint256[] memory tokenIds = new uint256[](_feathersData.length);
        uint256[] memory amounts = new uint256[](_feathersData.length);

        for (uint256 i = 0; i < _feathersData.length; i++) {
            require(
                _feathersData[i].length == 2,
                "Invalid feathers data format"
            );

            tokenIds[i] = _feathersData[i][0];
            amounts[i] = _feathersData[i][1];

            if (feathers.balanceOf(msg.sender, tokenIds[i]) < amounts[i])
                revert ERC1155InsufficientBalance();
        }

        feathers.burnBatch(msg.sender, tokenIds, amounts);
    }

    function _burnResources(uint256[][] memory _resourcesData) internal {
        uint256[] memory tokenIds = new uint256[](_resourcesData.length);
        uint256[] memory amounts = new uint256[](_resourcesData.length);

        for (uint256 i = 0; i < _resourcesData.length; i++) {
            require(
                _resourcesData[i].length == 3,
                "Invalid resource data format"
            );

            tokenIds[i] = _resourcesData[i][1];
            amounts[i] = _resourcesData[i][2];

            if (resources.balanceOf(msg.sender, tokenIds[i]) < amounts[i])
                revert ERC1155InsufficientBalance();
        }

        resources.burnBatch(msg.sender, tokenIds, amounts);
    }

    function _updateNonce() internal {
        usedNonces[nonceTracker] = true;
        nonceTracker++;
    }

    function _cockTransfer(
        uint256 _totalAmount,
        uint256 _amountToVault
    ) internal {
        cock.transferFrom(msg.sender, address(this), _totalAmount);
        cock.transfer(treasury, _amountToVault);
    }

    function claimNinunoBalance(
        uint256 tokenId,
        uint256 withdrawalRequestId,
        uint256 amount,
        bytes calldata _sig
    ) external nonReentrant {
        _validateClaimNinunoBalance(tokenId, withdrawalRequestId, amount, _sig);
        if (amount > cock.balanceOf(address(this)))
            revert InsufficientNinunoBalance();

        if (usedWithdrawalRequestIds[withdrawalRequestId])
            revert WithdrawalRequestIdAlreadyUsed();

        // Ownership check based on chicken type
        address owner = isGenesis(tokenId)
            ? genesis.ownerOf(tokenId)
            : legacy.ownerOf(tokenId);
        if (owner != msg.sender) revert UnauthorizedOwner();

        usedWithdrawalRequestIds[withdrawalRequestId] = true;

        cock.transfer(msg.sender, amount);

        emit NinunoBalanceClaimed(
            msg.sender,
            tokenId,
            withdrawalRequestId,
            amount
        );
    }

    function _validateClaimNinunoBalance(
        uint256 tokenId,
        uint256 withdrawalRequestId,
        uint256 amount,
        bytes memory _sig
    ) internal view {
        bytes32 message = prefixed(
            keccak256(
                abi.encodePacked(
                    msg.sender,
                    tokenId,
                    withdrawalRequestId,
                    amount
                )
            )
        );
        require(recoverSigner(message, _sig) == signer, InvalidSignature());
    }

    function isNinunoEligible(uint256 tokenId) internal pure returns (bool) {
        return tokenId <= 11110;
    }

    function isNinunoEligibleBatch(
        uint256[] calldata tokenIds
    ) public pure returns (bool[] memory) {
        bool[] memory result = new bool[](tokenIds.length);
        for (uint256 i = 0; i < tokenIds.length; i++) {
            result[i] = isNinunoEligible(tokenIds[i]);
        }
        return result;
    }

    function isGenesis(uint256 tokenId) internal pure returns (bool) {
        return tokenId <= 2222;
    }

    function isGenesisBatch(
        uint256[] calldata tokenIds
    ) public pure returns (bool[] memory) {
        bool[] memory result = new bool[](tokenIds.length);
        for (uint256 i = 0; i < tokenIds.length; i++) {
            result[i] = isGenesis(tokenIds[i]);
        }
        return result;
    }

    function getChickenBreedTimeBatch(
        uint256[] calldata tokenIds
    ) external view returns (uint256[] memory) {
        uint256[] memory result = new uint256[](tokenIds.length);
        for (uint256 i = 0; i < tokenIds.length; i++) {
            result[i] = chickenBreedTime[tokenIds[i]];
        }
        return result;
    }

    function getChickenBreedCountBatch(
        uint256[] calldata tokenIds
    ) external view returns (uint256[] memory) {
        uint256[] memory result = new uint256[](tokenIds.length);
        for (uint256 i = 0; i < tokenIds.length; i++) {
            result[i] = chickenBreedCount[tokenIds[i]];
        }
        return result;
    }

    function updateSigner(address _signer) external nonReentrant onlyOwner {
        signer = _signer;
    }

    function _validateBreedingSignature(
        uint256 chickenLeftTokenId,
        uint256 chickenRightTokenId,
        uint256 _totalAmount,
        uint256 _amountToVault,
        uint256 _amountToNinuno,
        uint256 _breedingCooldownTime,
        uint256[][] calldata _feathersData,
        uint256[][] calldata _resourcesData,
        bytes memory _sig
    ) internal view {
        // Hash the resources data separately
        bytes32 resourcesHash = keccak256(abi.encode(_resourcesData));
        bytes32 feathersHash = keccak256(abi.encode(_feathersData));

        bytes32 message = prefixed(
            keccak256(
                abi.encodePacked(
                    msg.sender,
                    chickenLeftTokenId,
                    chickenRightTokenId,
                    _totalAmount,
                    _amountToVault,
                    _amountToNinuno,
                    _breedingCooldownTime,
                    feathersHash,
                    resourcesHash
                )
            )
        );
        require(recoverSigner(message, _sig) == signer, InvalidSignature());
    }

    function splitSignature(
        bytes memory sig
    ) internal pure returns (uint8, bytes32, bytes32) {
        require(sig.length == 65, "Incorrect signature length");

        bytes32 r;
        bytes32 s;
        uint8 v;

        assembly {
            //first 32 bytes, after the length prefix
            r := mload(add(sig, 0x20))
            //next 32 bytes
            s := mload(add(sig, 0x40))
            //final byte, first of next 32 bytes
            v := byte(0, mload(add(sig, 0x60)))
        }

        return (v, r, s);
    }

    function recoverSigner(
        bytes32 message,
        bytes memory sig
    ) internal pure returns (address) {
        uint8 v;
        bytes32 r;
        bytes32 s;

        (v, r, s) = splitSignature(sig);

        return ecrecover(message, v, r, s);
    }

    function prefixed(bytes32 hash) internal pure returns (bytes32) {
        return
            keccak256(
                abi.encodePacked("\x19Ethereum Signed Message:\n32", hash)
            );
    }

    function breedBatch(
        BatchBreedingParams calldata params
    ) external nonReentrant {
        _validateBatchParams(params);

        uint256 pairCount = params.chickenLeftTokenIds.length;
        uint256 totalCockNeeded = _calculateTotalCock(params.totalAmounts);

        if (cock.balanceOf(msg.sender) < totalCockNeeded)
            revert ERC20InsufficientBalance();

        for (uint256 i = 0; i < pairCount; i++) {
            _processBatchBreed(params, i);
        }
    }

    function _validateBatchParams(
        BatchBreedingParams calldata params
    ) private pure {
        uint256 pairCount = params.chickenLeftTokenIds.length;
        if (pairCount == 0 || pairCount > 10) revert InvalidArrayLength();
        if (
            params.chickenRightTokenIds.length != pairCount ||
            params.totalAmounts.length != pairCount ||
            params.amountsToVault.length != pairCount ||
            params.amountsToNinuno.length != pairCount ||
            params.feathersData.length != pairCount ||
            params.resourcesData.length != pairCount ||
            params.signatures.length != pairCount
        ) revert InvalidArrayLength();
    }

    function _calculateTotalCock(
        uint256[] calldata amounts
    ) private pure returns (uint256) {
        uint256 total;
        for (uint256 i = 0; i < amounts.length; i++) {
            total += amounts[i];
        }
        return total;
    }

    function _processBatchBreed(
        BatchBreedingParams calldata params,
        uint256 index
    ) private returns (uint256) {
        _validateBreedingSignature(
            params.chickenLeftTokenIds[index],
            params.chickenRightTokenIds[index],
            params.totalAmounts[index],
            params.amountsToVault[index],
            params.amountsToNinuno[index],
            params.breedingCooldownTimes[index],
            params.feathersData[index],
            params.resourcesData[index],
            params.signatures[index]
        );

        if (
            params.chickenLeftTokenIds[index] ==
            params.chickenRightTokenIds[index]
        ) revert InvalidParents();
        if (usedNonces[nonceTracker]) revert NonceAlreadyUsed();

        BreedingParams memory breedParams = BreedingParams({
            chickenLeftTokenId: params.chickenLeftTokenIds[index],
            chickenRightTokenId: params.chickenRightTokenIds[index],
            totalAmount: params.totalAmounts[index],
            amountToVault: params.amountsToVault[index],
            amountToNinuno: params.amountsToNinuno[index],
            breedingCooldownTime: params.breedingCooldownTimes[index],
            feathersData: params.feathersData[index],
            resourcesData: params.resourcesData[index]
        });

        return _processBreeding(breedParams);
    }
}
