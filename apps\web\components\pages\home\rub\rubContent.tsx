"use client";
import Box from "@/components/shared/box";
import { Grid } from "@/components/ui";
import { useStateContext } from "@/providers/app/state";
import useChickenStore from "@/store/chicken";
import { ChickensData } from "@/types/chicken.type";
import { useCallback, useMemo, useState } from "react";
import { toast } from "sonner";
import { cn } from "@/utils/classes";
import { NftGrid } from "./nft-grid";
import RubbingChickenAnim from "./rubbing-chicken";
import useAuthStore from "@/store/auth";
import { RubResult } from "./rub-result";
import { RubHeader } from "./header";
import { useDelegationDistribution } from "@/features/delegation/hooks/useDelegationDistribution";

export function RubContent({ chickens }: { chickens: ChickensData[] | null }) {
  const [showResult, setShowResult] = useState(false);
  const [feathersResult, setFeathersResult] = useState(0);
  const [legFeathersResult, setLegFeathersResult] = useState(0);
  const { getMe } = useAuthStore();
  const { getChickens } = useChickenStore();
  const { isConnected } = useStateContext();
  const { getDelegationDistribution } = useDelegationDistribution();
  const [isLoading, setIsLoading] = useState(false);
  const totalFeathers = useMemo(() => {
    let feathers = 0;
    if (chickens && chickens.length !== 0) {
      chickens.forEach((chicken) => {
        feathers = chicken.dailyFeathers + feathers;
      });
    }
    return feathers;
  }, [chickens]);

  const rub = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/csrf-token");
      if (!response.ok) {
        throw new Error(`API call failed: ${response.statusText}`);
      }
      const { csrfToken } = await response.json();
      const res = await fetch("/api/chickens/daily-rub", {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
          "Content-Type": "application/json",
        },
      });
      await getMe();
      await new Promise((resolve) => setTimeout(resolve, 3000));
      if (res.ok) {
        const result = await res.json();
        const data = result.data;
        setFeathersResult(data.totalFeathers);
        setLegFeathersResult(data.totalLegFeathers);
        setShowResult(true); // Moved after setting other states for clarity
      } else {
        throw new Error("API call failed");
      }
    } catch (error) {
      toast.error("Something went wrong, please try again later.", {
        position: "top-center",
      });
    } finally {
      setIsLoading(false);
    }
  }, [chickens, isConnected, getMe]); // Removed showResult from dependencies

  // Calculate delegation distribution when showing results
  const delegationDistribution = showResult
    ? getDelegationDistribution(feathersResult, legFeathersResult)
    : null;

  return (
    <>
      <RubResult
        show={showResult}
        feathers={feathersResult}
        close={async () => {
          getChickens(isConnected);
          setShowResult(false);
          console.log("showResult set to false via close"); // Debug log
        }}
        legendaryFeathers={legFeathersResult}
        delegationDistribution={delegationDistribution}
      />
      <RubbingChickenAnim show={isLoading} />
      <Grid.Item className="backdrop-blur-sm bg-opacity-10 h-full">
        <Box>
          <RubHeader />
          <div className="flex justify-center h-96">
            <NftGrid chickens={chickens} />
          </div>
          <div className="flex flex-col items-center w-full">
            <p className="font-bold text-lg text-center">
              Rubbing your chickens gives you Feathers.
            </p>
            {chickens && chickens.length !== 0 ? (
              <p className="text-sm w-full md:w-[90%] text-center text-muted-fg">
                You have{" "}
                <span className="font-bold text-white">
                  {chickens.length}{" "}
                  {chickens.length > 1 ? "chickens" : "chicken"}
                </span>{" "}
                ready to rub and can earn{" "}
                <span className="font-bold text-white">
                  {totalFeathers} {totalFeathers > 1 ? "Feathers" : "Feather"}
                </span>
                {totalFeathers > 1 ? ". Click the button below to start!" : "."}
              </p>
            ) : (
              <p className="text-sm w-full md:w-[90%] text-center text-muted-fg">
                Either you&apos;re done rubbing for today, or there are no
                eligible chickens to rub. Try again tomorrow—everyone needs a
                rest!
              </p>
            )}
          </div>
          <div className="flex items-center justify-center mt-4 mb-8">
            <button
              className={cn(
                "w-full md:w-fit px-8 py-3 text-lg font-bold text-black uppercase rounded-lg bg-yellow-400 hover:bg-yellow-500 transition-all duration-300 transform hover:scale-105",
                !chickens || isLoading || chickens.length === 0
                  ? " opacity-60 forced-colors:disabled:text-[GrayText] cursor-not-allowed pointer-events-none"
                  : ""
              )}
              style={{
                boxShadow:
                  "0 0 15px rgba(247, 203, 66, 0.7), 0 0 30px rgba(247, 203, 66, 0.4)",
                textShadow: "0 0 2px rgba(0,0,0,0.2)",
              }}
              onClick={rub}
            >
              {isLoading ? "RUBBING CHICKENS" : "RUB YOUR CHICKENS"}
            </button>
          </div>
        </Box>
      </Grid.Item>
    </>
  );
}
