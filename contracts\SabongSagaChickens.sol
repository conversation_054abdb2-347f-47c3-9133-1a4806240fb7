// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;
import "./upgradeable/ERC721CommonUpgradeable.sol";

contract SabongSagaChickens is ERC721CommonUpgradeable {
    /// @dev Mint NFTs for the launchpad.
    function mintLaunchpad(
        address to,
        uint256 quantity,
        bytes calldata /* extraData */
    )
        external
        onlyRole(MINTER_ROLE)
        returns (uint256[] memory tokenIds, uint256[] memory amounts)
    {
        tokenIds = new uint256[](quantity);
        amounts = new uint256[](quantity);
        for (uint256 i; i < quantity; ++i) {
            tokenIds[i] = _mintFor(to);
            amounts[i] = 1;
        }
    }
}
