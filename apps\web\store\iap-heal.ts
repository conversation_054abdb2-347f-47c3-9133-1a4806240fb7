import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import {
  UserRejectedRequestError,
  ContractFunctionExecutionError,
  Address,
} from "viem";
import { create } from "zustand";
import { ronin, saigon } from "viem/chains";
import IAP_ABI from "@/abi/iap.abi.json";
import { delay } from "@/utils/delay";
type ItemPrices = {
  ronInWei: string;
  itemId: number;
};

type VerifyStatus = {
  prevHealth: number;
  currentHealth: number;
  itemId: number;
  isCalled: boolean;
};

type SignatureWithData = {
  signature: string;
  contractAddress: string;
  raw: {
    buyer: string;
    tokenId: number;
    amount: string;
    token: string;
    functionId: number;
    nonce: number;
    epoch: number;
    deadline: number;
  };
};

type IAPHealState = {
  isPending: boolean;
  prices: ItemPrices[];
};

type Actions = {
  clearStore: () => void;
  getSignature: (itemId: number, tokenId: string) => Promise<SignatureWithData>;
  getPrices: () => Promise<void>;
  processPurchase: (purchaseData: SignatureWithData) => Promise<`0x${string}`>;
  verifyStatus: (txHash: `0x${string}`) => Promise<VerifyStatus>;
};

type StoreState = IAPHealState & Actions;

const ADDRESSES = {
  RESOURCES_TOKEN: process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address,
  DAILY_FEED: process.env.NEXT_PUBLIC_DAILY_FEED_CONTRACT as Address,
};

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const chain = CHAIN_ID === 2020 ? ronin : saigon;

const handleError = (
  error: unknown,
  defaultMessage: string,
  operation: string
) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }
  if (error instanceof ContractFunctionExecutionError) {
    toast.error(error.name, {
      description: error.shortMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }
  } else {
    const err = error as Error;

    // Fallback for non-Error objects
    toast.error(err.name, {
      description: err.message,
      position: "top-right",
    });
    throw err;
  }
};

const initialState = {
  isPending: false,
  prices: [],
};

const useIapHealStore = create<StoreState>()((set, get) => ({
  ...initialState,
  getPrices: async () => {
    try {
      set({ isPending: true });
      const res = await fetch("/api/iap/prices");

      if (res.ok) {
        const data = await res.json();
        set({ prices: data.data });
      }
    } catch (error) {
    } finally {
      set({ isPending: false });
    }
  },
  getSignature: async (itemId, tokenId) => {
    try {
      set({ isPending: true });
      const res = await fetch("/csrf-token");
      if (!res.ok) {
        throw new Error(`API call failed: ${res.statusText}`);
      }
      const { csrfToken } = await res.json();

      const response = await fetch("/api/iap/purchase", {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          itemId,
          tokenId,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Purchase API failed: ${response.status} ${errorText}`);
      }

      const data: { data: SignatureWithData } = await response.json();
      return data.data;
    } catch (error) {
      handleError(error, "Failed to get signature", "getSignature");
      throw error; // rethrow after handling
    } finally {
      set({ isPending: false });
    }
  },

  processPurchase: async (data) => {
    try {
      set({ isPending: true });
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC1155", {
          description: "State context not available",
          position: "top-right",
        });
        throw new Error("State context not available");
      }

      const { address, publicClient, walletClient, ConnectRecentWallet } =
        stateContext;

      // Connect wallet if not connected
      if (!address || !walletClient) {
        await ConnectRecentWallet();
      }

      if (!address || !walletClient) {
        toast.error("Wallet not connected", {
          position: "top-right",
        });
        throw new Error("Wallet not connected");
      }

      // Simulate contract call to check if it will succeed
      const simulateReq = await publicClient.simulateContract({
        address: data.contractAddress as Address,
        abi: IAP_ABI,
        functionName: "executePurchase",
        args: [
          [
            data.raw.buyer,
            BigInt(data.raw.tokenId),
            data.raw.amount,
            data.raw.token,
            BigInt(data.raw.functionId),
            BigInt(data.raw.nonce),
            BigInt(data.raw.deadline),
          ],
          data.signature,
        ],
        chain,
        account: address,
        value: BigInt(data.raw.amount),
      });

      // Send the transaction
      const hash = await walletClient.writeContract(simulateReq.request);

      toast.info("Heal Purchase transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });

      // Wait for transaction receipt
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        await delay(5000);
        return hash;
      } else {
        toast.error("Heal Purchase transaction failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-center",
        });
        throw new Error("Heal Purchase transaction failed");
      }
    } catch (error) {
      handleError(error, "Failed to process purchase", "processPurchase");
      throw error; // rethrow so caller knows it failed
    } finally {
      set({ isPending: false });
    }
  },

  verifyStatus: async (txHash) => {
    try {
      set({ isPending: true });

      const response = await fetch(`/api/iap/purchase?txHash=${txHash}`);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Purchase API failed: ${response.status} ${errorText}`);
      }

      const data: { data: VerifyStatus } = await response.json();
      return data.data;
    } catch (error) {
      handleError(error, "Failed to verify status", "verifyStatus");
      throw error; // rethrow after handling so caller knows it failed
    } finally {
      set({ isPending: false });
    }
  },

  clearStore: () => {
    set({ ...initialState });
  },
}));

export default useIapHealStore;
