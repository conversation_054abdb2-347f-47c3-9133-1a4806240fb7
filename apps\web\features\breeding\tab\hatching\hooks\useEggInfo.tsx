"use client";

import axios from "@/lib/api";
import { useQuery } from "@tanstack/react-query";
import { IEggInfo } from "../types/egg-info.types";
import { MOCK_EGG_INFO } from "../data/egg-info";
import { useMemo } from "react";

const fetchEggInfo = async (tokenIds: number[]) => {
  /* if (process.env.NODE_ENV === "development") {
    return MOCK_EGG_INFO.data as IEggInfo[];
  } */

  const { data } = await axios.post("/ninuno-rewards/view-chicken-info", {
    chickenTokenIds: tokenIds,
  });
  return data.data as IEggInfo[];
};

const useEggInfo = (tokenIds: number[]) => {
  const eggInfoQuery = useQuery({
    queryKey: ["eggInfo", tokenIds],
    queryFn: () => fetchEggInfo(tokenIds),
    enabled: tokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const eggInfoMap = useMemo(() => {
    if (!eggInfoQuery.data || !tokenIds) {
      return {};
    }
    return tokenIds.reduce(
      (map, tokenId) => {
        map[tokenId] = eggInfoQuery.data.find(
          (eggInfo) => eggInfo.token_id === tokenId
        ) as IEggInfo;
        return map;
      },
      {} as Record<number, IEggInfo>
    );
  }, [eggInfoQuery.data]);

  return { eggInfoQuery, eggInfoMap };
};

export { fetchEggInfo };
export default useEggInfo;
