/**
 * Rewards Inventory Types
 *
 * This file contains TypeScript interfaces for the Rewards Inventory feature.
 */

/**
 * Enum for reward item types
 */
export enum ERewardType {
  CRYSTAL = "CRYSTAL",
  SHARD = "SHARD", 
  CORN = "CORN",
}

/**
 * Interface for reward item information
 */
export interface IRewardItem {
  id: string;
  name: string;
  type: ERewardType;
  quantity: number;
  image: string;
  description: string;
}

/**
 * Interface for reward inventory selection state
 */
export interface IInventorySelection {
  [itemId: string]: boolean;
}

/**
 * Interface for claim inventory request
 */
export interface IClaimInventoryRequest {
  itemIds: string[];
  address: string;
}

/**
 * API Response Types for Game Rewards
 */
export interface IGameRewardItem {
  _id: string;
  chickenId: number;
  owner: string;
  matchId: string;
  tokenId: number;
  contractType: string;
  rewardId: number;
  rewardType: string;
  claimed: boolean;
  createdAt: string;
  __v: number;
  deadChickenId?: string;
}

export interface IGameRewardsResponse {
  success: boolean;
  data: {
    rewardsByChicken: {
      [chickenId: string]: IGameRewardItem[];
    };
    tokenIds: number[];
    totalRewards: number;
    totalChickens: number;
  };
}