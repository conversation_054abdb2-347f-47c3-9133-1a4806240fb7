"use client";

import { IEggInfo } from "../types/egg-info.types";

/**
 * Checks if an egg is in breeding phase
 * @param eggInfo The egg info object
 * @returns True if the egg is in breeding phase, false otherwise
 */
export const isEggInBreedingPhase = (
  eggInfo: IEggInfo | undefined
): boolean => {
  if (!eggInfo) return false;
  // If the egg is already hatched, it's not in breeding phase
  if (eggInfo.is_hatched === 1) return false;

  const now = new Date();
  const breedingEndTime = new Date(eggInfo.parent_breeding_state_time * 1000);
  return breedingEndTime > now;
};

/**
 * Checks if an egg is in hatching phase
 * @param eggInfo The egg info object
 * @returns True if the egg is in hatching phase, false otherwise
 */
export const isEggInHatchingPhase = (
  eggInfo: IEggInfo | undefined
): boolean => {
  if (!eggInfo) return false;
  // If the egg is already hatched, it's not in hatching phase
  if (eggInfo.is_hatched === 1) return false;

  const now = new Date();

  // If the parent breeding state is already passed, the egg is in hatching phase
  const breedingState = new Date(eggInfo.parent_breeding_state_time * 1000);
  return breedingState <= now;
};

/**
 * Checks if an egg is ready to hatch
 * @param eggInfo The egg info object
 * @returns True if the egg is ready to hatch, false otherwise
 */
export const isEggReadyToHatch = (eggInfo: IEggInfo | undefined): boolean => {
  if (!eggInfo) return false;
  // If the egg is already hatched, it's not ready to hatch again
  if (eggInfo.is_hatched === 1) return false;

  const now = new Date();

  // if hatched_at is already passed, the egg is ready to hatch
  const hatchedAt = new Date(eggInfo.hatched_at);
  return hatchedAt <= now;
};

/**
 * Checks if an egg has been hatched
 * @param eggInfo The egg info object
 * @returns True if the egg has been hatched, false otherwise
 */
export const isEggHatched = (eggInfo: IEggInfo | undefined): boolean => {
  if (!eggInfo) return false;
  return eggInfo.is_hatched === 1;
};

/**
 * Gets the remaining seconds until an egg is ready to hatch
 * @param eggInfo The egg info object
 * @returns The remaining seconds and progress, or 0 if the egg is ready to hatch
 */
export const getBreedingPhaseRemainingTime = (
  eggInfo: IEggInfo | undefined
): {
  remainingSeconds: number;
  progress: number;
} => {
  if (!eggInfo) {
    return {
      remainingSeconds: 0,
      progress: 0,
    };
  }

  const now = new Date();
  const breedingEndTime = new Date(eggInfo.parent_breeding_state_time * 1000);

  // If breeding has ended, set progress to 100%
  if (now >= breedingEndTime) {
    return {
      remainingSeconds: 0,
      progress: 100,
    };
  }

  // For breeding phase, we need to calculate progress based on time
  const breedingStartTime = new Date(eggInfo.created_at);

  // Calculate elapsed time and total duration
  const elapsedTime = now.getTime() - breedingStartTime.getTime();
  const totalDuration = breedingEndTime.getTime() - breedingStartTime.getTime();

  // Calculate remaining seconds
  const remainingSeconds = Math.floor(
    (breedingEndTime.getTime() - now.getTime()) / 1000
  );

  // Calculate progress percentage (0-100)
  const progressValue = Math.min(
    100,
    Math.max(0, (elapsedTime / totalDuration) * 100)
  );

  return {
    remainingSeconds,
    progress: Math.round(progressValue),
  };
};

/**
 * Gets the remaining seconds until an egg is ready to hatch
 * @param eggInfo The egg info object
 * @returns The remaining seconds and progress, or 0 if the egg is ready to hatch
 */
export const getHatchingPhaseRemainingTime = (
  eggInfo: IEggInfo | undefined
): {
  remainingSeconds: number;
  progress: number;
} => {
  if (!eggInfo) {
    return {
      remainingSeconds: 0,
      progress: 0,
    };
  }

  const now = new Date();
  const hatchingEndTime = new Date(eggInfo.hatched_at);
  const hatchingStartTime = new Date(eggInfo.parent_breeding_state_time * 1000);

  // If hatching has ended, set progress to 100%
  if (now >= hatchingEndTime) {
    return {
      remainingSeconds: 0,
      progress: 100,
    };
  }

  // Calculate elapsed time since start
  const elapsedTime = now.getTime() - hatchingStartTime.getTime();
  const totalDuration = hatchingEndTime.getTime() - hatchingStartTime.getTime();

  // Calculate remaining seconds
  const remainingSeconds = Math.floor(
    (hatchingEndTime.getTime() - now.getTime()) / 1000
  );

  // Calculate progress percentage (0-100)
  const progressValue = Math.min(
    100,
    Math.max(0, (elapsedTime / totalDuration) * 100)
  );

  return {
    remainingSeconds,
    progress: Math.round(progressValue),
  };
};
