"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "ui";
import { cn } from "@/utils/classes";
import Image from "next/image";
import { Clock, User, Zap, Info } from "lucide-react";
import { Feathers } from "@/components/shared/icons/feathers";
import {
  IRentalWithMetadata,
  formatRentalDuration,
  formatRoninPrice,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
  ERentalStatus,
  getRentalRemainingSeconds,
  calculateMarketplaceFee,
  calculateEarningsAfterFees,
  formatMarketplaceFeePercentage,
} from "../../types/delegation.types";
import { useMarketplaceFee } from "../../hooks/useMarketplaceFee";
import { RentalStatusBadge } from "../shared/rental-status-badge";
import { useStateContext } from "@/providers/app/state";
import { CooldownTimer } from "@/components/common/cooldown-timer";
import { ChickenDetailsModal } from "./chicken-details-modal";

interface IRentalCardProps {
  rental: IRentalWithMetadata;
  onRent?: (rental: IRentalWithMetadata) => void;
  onCancel?: (rental: IRentalWithMetadata) => void;
  isOwner?: boolean;
  isCancelling?: boolean;
  className?: string;
}

export function RentalCard({
  rental,
  onRent,
  onCancel,
  isOwner = false,
  isCancelling = false,
  className,
}: IRentalCardProps) {
  const { address } = useStateContext();
  const { feePercentage } = useMarketplaceFee();
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const chickenType = String(
    rental.chickenMetadata?.attributes?.find(
      (attr) => attr.trait_type === "Type"
    )?.value || "Unknown"
  );

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "genesis":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "legacy":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "ordinary":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  // Determine which address to display and what label to use
  const getAddressInfo = () => {
    const currentAddress = address?.toLowerCase();
    const ownerAddress = rental.ownerAddress?.toLowerCase();

    if (currentAddress && ownerAddress && currentAddress === ownerAddress) {
      // Current user is the owner, show renter address
      return {
        address: rental.renterAddress,
        label: "Renter",
        displayText: rental.renterAddress
          ? `${rental.renterAddress.slice(0, 4)}...${rental.renterAddress.slice(-4)}`
          : "Not Rented",
      };
    } else {
      // Current user is not the owner, show owner address
      return {
        address: rental.ownerAddress,
        label: "Owner",
        displayText: rental.ownerAddress
          ? `${rental.ownerAddress.slice(0, 4)}...${rental.ownerAddress.slice(-4)}`
          : "Unknown",
      };
    }
  };

  const addressInfo = getAddressInfo();

  return (
    <div
      className={cn(
        "group relative bg-gradient-to-b from-stone-800 to-stone-900 border-2 border-stone-700 rounded-xl p-4 hover:border-yellow-500/70 transition-all duration-300 hover:shadow-xl hover:shadow-yellow-500/10 hover:scale-[1.02]",
        className
      )}
    >
      {/* Status Badge - Upper Right Corner */}
      <div className="absolute top-3 right-3 z-10">
        <RentalStatusBadge
          status={rental.status}
          roninPrice={rental.roninPrice}
          className="shadow-lg"
        />
      </div>

      {/* Header with Image and Basic Info */}
      <div className="flex gap-3 mb-3">
        <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gradient-to-br from-stone-700 to-stone-800 flex-shrink-0 ring-2 ring-stone-600 group-hover:ring-yellow-500/50 transition-all duration-300">
          {rental.chickenMetadata?.image ? (
            <Image
              src={rental.chickenMetadata.image}
              alt={
                rental.chickenMetadata.name ||
                `Chicken #${rental.chickenTokenId}`
              }
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500">
              <Zap className="w-6 h-6" />
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-bold text-white text-base truncate">
              {rental.chickenMetadata?.name ||
                `Chicken #${rental.chickenTokenId}`}
            </h3>
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowDetailsModal(true);
              }}
              className="p-1 rounded-full bg-stone-700/50 hover:bg-stone-600/50 transition-colors group/info flex-shrink-0"
              title="View chicken details"
            >
              <Info className="w-4 h-4 text-gray-400 group-hover/info:text-yellow-400 transition-colors" />
            </button>
          </div>
          <span
            className={`inline-block text-xs px-2 py-1 rounded-full border font-medium ${getTypeColor(chickenType)}`}
          >
            {chickenType}
          </span>
        </div>
      </div>

      {/* Price Section - Dedicated Row */}
      <div className="bg-gradient-to-r from-yellow-500/15 to-amber-500/15 border border-yellow-500/30 rounded-lg p-3 mb-3 text-center">
        {(() => {
          const totalPrice = parseFloat(rental.roninPrice) / 1e18;
          const durationInDays = rental.rentalPeriod / 86400;
          const dailyRate =
            durationInDays > 0 ? totalPrice / durationInDays : totalPrice;
          const marketplaceFee = calculateMarketplaceFee(
            totalPrice,
            feePercentage
          );
          const ownerEarnings = calculateEarningsAfterFees(
            totalPrice,
            feePercentage
          );
          const isOwnerOfRental =
            address &&
            rental.ownerAddress.toLowerCase() === address.toLowerCase();

          return (
            <>
              <div className="text-yellow-400 text-xs font-medium mb-1">
                Daily Rate
              </div>
              <div className="text-white font-bold text-xl">
                {dailyRate.toFixed(4)}{" "}
                <span className="text-yellow-400">RON/day</span>
              </div>
              <div className="text-yellow-300 text-xs mt-1">
                Total: {formatRoninPrice(rental.roninPrice)} RON
              </div>
              {isOwnerOfRental && (
                <div className="text-xs mt-2 pt-2 border-t border-yellow-500/20">
                  <div className="text-red-300">
                    Fee ({formatMarketplaceFeePercentage(feePercentage)}): -
                    {marketplaceFee.toFixed(4)} RON
                  </div>
                  <div className="text-green-300 font-medium">
                    Your Earnings: {ownerEarnings.toFixed(4)} RON
                  </div>
                </div>
              )}
            </>
          );
        })()}
      </div>

      {/* Key Metrics - Compact Row */}
      <div className="flex gap-2 mb-3">
        <div className="flex-1 bg-stone-700/30 rounded-lg p-2 text-center">
          <Clock className="w-4 h-4 text-blue-400 mx-auto mb-1" />
          <div className="text-gray-400 text-xs">
            {rental.status === ERentalStatus.RENTED ? "Time Left" : "Duration"}
          </div>
          <div className="text-white font-semibold text-xs">
            {rental.status === ERentalStatus.RENTED && rental.expiresAt ? (
              getRentalRemainingSeconds(rental) > 0 ? (
                <CooldownTimer
                  remainingSeconds={getRentalRemainingSeconds(rental)}
                  className="text-white font-semibold text-xs"
                />
              ) : (
                <span className="text-red-400">Expired</span>
              )
            ) : (
              formatRentalDuration(rental.rentalPeriod)
            )}
          </div>
        </div>

        {rental.dailyFeathers && rental.dailyFeathers > 0 && (
          <div className="flex-1 bg-stone-700/30 rounded-lg p-2 text-center">
            <Feathers size={16} className="text-yellow-400 mx-auto mb-1" />
            <div className="text-gray-400 text-xs">Daily Feathers</div>
            <div className="text-yellow-400 font-bold text-xs">
              {rental.dailyFeathers}
            </div>
          </div>
        )}

        <div className="flex-1 bg-stone-700/30 rounded-lg p-2 text-center">
          <User className="w-4 h-4 text-purple-400 mx-auto mb-1" />
          <div className="text-gray-400 text-xs">{addressInfo.label}</div>
          <div className="text-white font-mono text-xs">
            {addressInfo.displayText}
          </div>
        </div>
      </div>

      {/* Delegation Terms - Badge Style */}
      <div className="mb-4">
        <div className="text-xs text-gray-400 mb-2">Delegation Terms</div>
        <div className="flex flex-wrap gap-2">
          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
            {DELEGATED_TASK_LABELS[rental.delegatedTask]}
          </span>
          {rental.rewardDistribution === 3 && rental.sharedRewardAmount ? (
            <Tooltip delay={0}>
              <Tooltip.Trigger>
                <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300 border border-green-500/30 cursor-help">
                  {REWARD_DISTRIBUTION_LABELS[rental.rewardDistribution]}
                  <span className="ml-1 text-yellow-400 font-bold">
                    ({rental.sharedRewardAmount} 🪶/day)
                  </span>
                </span>
              </Tooltip.Trigger>
              <Tooltip.Content>
                <div className="text-sm">
                  <div className="font-semibold mb-1">Reward Distribution:</div>
                  <div>
                    Renter gets: {rental.sharedRewardAmount} feathers/day
                  </div>
                  <div>
                    Owner gets:{" "}
                    {(rental.dailyFeathers || 0) - rental.sharedRewardAmount}{" "}
                    feathers/day
                  </div>
                </div>
              </Tooltip.Content>
            </Tooltip>
          ) : (
            <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300 border border-green-500/30">
              {REWARD_DISTRIBUTION_LABELS[rental.rewardDistribution]}
              {rental.sharedRewardAmount && (
                <span className="ml-1 text-yellow-400 font-bold">
                  ({rental.sharedRewardAmount} 🪶/day)
                </span>
              )}
            </span>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3">
        {!isOwner && onRent && (
          <Button
            className="flex-1 bg-gradient-to-r from-yellow-500 to-amber-500 hover:from-yellow-600 hover:to-amber-600 text-black font-bold shadow-lg hover:shadow-xl transition-all duration-200"
            onPress={() => onRent(rental)}
            size="small"
          >
            Rent Now
          </Button>
        )}

        {isOwner && onCancel && rental.status === ERentalStatus.AVAILABLE && (
          <Button
            className="flex-1 bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-600/50 hover:border-red-500"
            size="small"
            appearance="outline"
            onPress={() => onCancel(rental)}
            isDisabled={isCancelling}
          >
            {isCancelling ? "Cancelling..." : "Cancel"}
          </Button>
        )}
      </div>

      {/* Chicken Details Modal */}
      <ChickenDetailsModal
        isOpen={showDetailsModal}
        onOpenChange={setShowDetailsModal}
        rental={rental}
      />
    </div>
  );
}
