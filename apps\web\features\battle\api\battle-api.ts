import axios from "@/lib/api";
import {
  IBattleRequestPayload,
  IBattleRequestResponse,
  IBattleVerifyPayload,
  IBattleVerifyResponse,
} from "../types/battle.types";

/**
 * Request battle - generates a message to sign for chicken ownership verification
 * @param payload - Contains chickenTokenId
 * @returns Promise with the message to sign
 */
export const requestBattle = async (
  payload: IBattleRequestPayload
): Promise<IBattleRequestResponse> => {
  try {
    const { data } = await axios.post("/battle/request", payload);
    return data;
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || "Failed to request battle"
    );
  }
};

/**
 * Verify battle - verifies the signed message and confirms chicken ownership
 * @param payload - Contains chickenTokenId and signature
 * @returns Promise with verification result including nonce and signature
 */
export const verifyBattle = async (
  payload: IBattleVerifyPayload
): Promise<IBattleVerifyResponse> => {
  try {
    const { data } = await axios.post("/battle/verify", payload);
    return data;
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || "Failed to verify battle ownership"
    );
  }
};

/**
 * Complete battle flow - handles the full request -> sign -> verify flow
 * @param chickenTokenId - The chicken token ID to battle with
 * @param signMessage - Function to sign the message (from wagmi useSignMessage)
 * @returns Promise with verification result
 */
export const completeBattleFlow = async (
  chickenTokenId: number,
  signMessage: (message: string) => Promise<string>
): Promise<IBattleVerifyResponse> => {
  try {
    // Step 1: Request battle (get message to sign)
    const requestResult = await requestBattle({ chickenTokenId });
    
    if (requestResult.status !== 1) {
      throw new Error(requestResult.message);
    }

    // Step 2: Sign the message
    const signature = await signMessage(requestResult.data);

    // Step 3: Verify the signature
    const verifyResult = await verifyBattle({ chickenTokenId, signature });

    if (verifyResult.status !== 1) {
      throw new Error(verifyResult.message);
    }

    return verifyResult;
  } catch (error: any) {
    throw new Error(error.message || "Battle flow failed");
  }
};

/**
 * Utility function to check if a chicken can battle
 * @param chickenStats - The chicken's battle stats
 * @returns boolean indicating if chicken can battle
 */
export const canChickenBattle = (chickenStats: {
  state?: string;
  stats?: { currentHp?: number; hp?: number };
}): { canBattle: boolean; reason?: string } => {
  // Check if chicken is in a valid state
  if (chickenStats.state === "dead") {
    return { canBattle: false, reason: "Chicken is dead" };
  }
  
  if (chickenStats.state === "faint") {
    return { canBattle: false, reason: "Chicken is fainted" };
  }
  
  if (chickenStats.state === "breeding") {
    return { canBattle: false, reason: "Chicken is breeding" };
  }

  // Check HP requirements
  const currentHp = chickenStats.stats?.currentHp || chickenStats.stats?.hp || 0;
  if (currentHp < 50) {
    return { canBattle: false, reason: "Chicken needs at least 50 HP to battle" };
  }

  return { canBattle: true };
};

/**
 * Format battle verification data for game server
 * @param verificationData - Data from battle verification
 * @param address - User's wallet address
 * @returns Formatted data for game server
 */
export const formatBattleDataForGame = (
  verificationData: IBattleVerifyResponse["data"],
  address: string
) => {
  return {
    fighterId: verificationData.chickenTokenId.toString(),
    address,
    signature: verificationData.signature,
    nonce: verificationData.nonce.toString(),
  };
};

/**
 * Generate game URL with battle parameters
 * @param gameUrl - Base game URL
 * @param matchCode - Match code from matchmaking
 * @param verificationData - Battle verification data
 * @param address - User's wallet address
 * @returns Complete game URL with parameters
 */
export const generateGameUrl = (
  gameUrl: string,
  matchCode: string,
  verificationData: IBattleVerifyResponse["data"],
  address: string
): string => {
  const params = new URLSearchParams({
    code: matchCode,
    fighterId: verificationData.chickenTokenId.toString(),
    address,
    signature: verificationData.signature,
    nonce: verificationData.nonce.toString(),
  });

  return `${gameUrl}?${params.toString()}`;
};
