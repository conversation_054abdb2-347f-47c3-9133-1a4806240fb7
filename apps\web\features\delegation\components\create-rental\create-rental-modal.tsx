"use client";

import { Modal } from "ui";
import { CreateRental } from "./create-rental";

interface ICreateRentalModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  preSelectedChickenId?: number | null;
}

export function CreateRentalModal({
  isOpen,
  onOpenChange,
  preSelectedChickenId,
}: ICreateRentalModalProps) {
  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content
        size="5xl"
        classNames={{
          content: "max-h-none h-auto",
          overlay: "overflow-y-auto",
        }}
      >
        <Modal.Header>
          <Modal.Title>Delegate Chicken</Modal.Title>
          <Modal.Description>
            Set up delegation terms for your chicken - either list it for rent
            or delegate it directly to someone
          </Modal.Description>
        </Modal.Header>
        <Modal.Body className="pb-12">
          <CreateRental
            preSelectedChickenId={preSelectedChickenId}
            onSuccess={() => onOpenChange(false)}
          />
        </Modal.Body>
      </Modal.Content>
    </Modal>
  );
}
