"use client";
import { useMediaQuery } from "@/lib/utils/hook/use-media-query";
import { useStateContext } from "@/providers/app/state";
import useAuthStore from "@/store/auth";
import { cn } from "@/utils/classes";
import {
  IconBrandDiscord,
  IconBrandFacebook,
  IconBrandTiktok,
  IconBrandX,
  IconBrandYoutube,
  IconChevronLgDown,
} from "justd-icons";
import { usePathname } from "next/navigation";
import React from "react";
import { Menu, Navbar, Separator, Skeleton, Tooltip } from "ui";
import { FlameIcon } from "../icons";
import ConnectWallet from "@/features/connect-wallet";

interface Social {
  name: string;
  icon: React.ReactNode;
  path: string;
}

const SOCIALS: Social[] = [
  { name: "Twitter", icon: <IconBrandX />, path: "https://x.com/SabongSaga" },
  {
    name: "Discord",
    icon: <IconBrandDiscord />,
    path: "https://discord.gg/sabongsaga",
  },
  {
    name: "TikTok",
    icon: <IconBrandTiktok />,
    path: "https://www.tiktok.com/@sabongsaga",
  },
  {
    name: "YouTube",
    icon: <IconBrandYoutube />,
    path: "https://www.youtube.com/@SabongSaga",
  },
  {
    name: "Facebook",
    icon: <IconBrandFacebook />,
    path: "https://www.facebook.com/people/Sabong-Saga/61567215403687/",
  },
];

const NAV_LINKS = [
  { label: "Home", path: "/", isCurrent: true, isProtected: false },
  // {
  //   label: "Collection",
  //   path: "https://marketplace.skymavis.com/collections/******************************************",
  //   isExternal: true,
  // },
  {
    label: "Whitepaper",
    path: "https://whitepaper.sabongsaga.com/",
    isExternal: true,
    isProtected: false,
  },
  { label: "Breeding", path: "/breeding", isCurrent: true, isProtected: true },
  { label: "Crafting", path: "/crafting", isCurrent: true, isProtected: true },
  {
    label: "Inventory",
    path: "/inventory/chickens",
    isCurrent: true,
    isProtected: true,
  },
  {
    label: "Rewards",
    path: "/rewards",
    isCurrent: true,
    isProtected: true,
  },
  {
    label: "Delegation",
    path: "/delegation",
    isCurrent: true,
    isProtected: true,
  },
  {
    label: "Leaderboard",
    path: "/leaderboard",
    isCurrent: true,
    isProtected: true,
  },
];

const SocialMenu = () => (
  <Menu>
    <Navbar.Item>
      Socials <IconChevronLgDown data-slot="chevron" />
    </Navbar.Item>
    <Menu.Content items={SOCIALS}>
      {SOCIALS.map(({ name, icon, path }, idx) => (
        <Menu.Item
          className={"hover:bg-primary font-Poppins"}
          id={idx}
          key={idx}
          textValue={name}
          target="_blank"
          href={path}
        >
          {icon} {name}
        </Menu.Item>
      ))}
    </Menu.Content>
  </Menu>
);

const NavLinks = () => {
  const pathname = usePathname();
  const { isConnected } = useStateContext();

  return (
    <>
      {NAV_LINKS.map(({ label, path, isCurrent, isExternal, isProtected }) => {
        if (isProtected && !isConnected) {
          return null;
        } else {
          return (
            <Navbar.Item
              key={path}
              isCurrent={pathname === path}
              href={path}
              target={isExternal ? "_blank" : undefined}
            >
              {label}
            </Navbar.Item>
          );
        }
      })}
    </>
  );
};

const Logo = () => (
  <Navbar.Logo aria-label="Sabong saga logo" href="/">
    <img
      src="/images/sabong-saga-logo.png"
      alt="Sabong saga logo"
      className="h-[56px] w-auto"
    />
  </Navbar.Logo>
);

function AppNavbar(props: React.ComponentProps<typeof Navbar>) {
  const { isConnected, loading } = useStateContext();
  const { streak } = useAuthStore();

  return (
    <Navbar {...props}>
      {/* Desktop */}
      <Navbar.Nav className="bg-transparent font-Poppins font-bold">
        <Logo />
        <Navbar.Section className="ml-auto hidden md:flex font-Geist">
          <NavLinks />
          {/* <SocialMenu /> */}
          {isConnected && !loading.value && (
            <Streak streak={streak} isPending={loading.value} />
          )}

          <ConnectWallet />
        </Navbar.Section>
      </Navbar.Nav>

      {/* Mobile */}
      <Navbar.Compact className="font-Poppins font-bold">
        <Navbar.Nav>
          <Logo />
          <NavLinks />
          {/* <SocialMenu /> */}
        </Navbar.Nav>

        <Navbar.Flex>
          <Navbar.Trigger className="-ml-2" />
          {isConnected && !loading.value && (
            <>
              <Separator orientation="vertical" className="h-6 sm:mx-1" />{" "}
              <Streak streak={streak} isPending={loading.value} />
            </>
          )}
        </Navbar.Flex>
        <Navbar.Flex>
          <ConnectWallet />
        </Navbar.Flex>
      </Navbar.Compact>

      <Navbar.Inset />
    </Navbar>
  );
}

function Streak({ streak, isPending }: { streak: number; isPending: boolean }) {
  return (
    <Tooltip delay={0}>
      {isPending ? (
        <Skeleton className="w-32 h-20" />
      ) : (
        <Tooltip.Trigger aria-label="Daily Streak">
          <div className="flex items-center justify-center">
            <div className="relative">
              {/* Base flame */}
              <FlameIcon
                size={20}
                className={cn(
                  "relative z-10",
                  streak === 0 ? "fill-teal-900" : "fill-[#FF4500]"
                )}
              />

              {/* Pulsing overlay */}
              {streak > 0 && (
                <FlameIcon
                  size={20}
                  className="absolute top-0 left-0 fill-[#FF4500] animate-pulse-up"
                />
              )}
            </div>
            <p
              className={cn(
                "mt-1 font-['Poppins']",
                streak === 0 ? "text-teal-900" : "text-white"
              )}
            >
              {streak} {streak > 1 ? "days" : "day"}
            </p>
          </div>
        </Tooltip.Trigger>
      )}

      <Tooltip.Content>
        <div className="relative">
          <strong className="font-semibold">Daily streak</strong>
        </div>
      </Tooltip.Content>
    </Tooltip>
  );
}

export default AppNavbar;
