"use client";

import { Badge } from "ui";
import {
  ERentalStatus,
  RENTAL_STATUS_LABELS,
} from "../../types/delegation.types";

interface IRentalStatusBadgeProps {
  status: ERentalStatus;
  roninPrice?: string;
  className?: string;
}

export function RentalStatusBadge({
  status,
  roninPrice,
  className,
}: IRentalStatusBadgeProps) {
  // Check if this is a delegation (status is RENTED but price is 0)
  const isDelegated = status === ERentalStatus.RENTED && roninPrice === "0";

  const getDisplayLabel = () => {
    if (isDelegated) {
      return "Delegated";
    }
    return RENTAL_STATUS_LABELS[status];
  };

  const getStatusVariant = (status: ERentalStatus, isDelegated: boolean) => {
    if (isDelegated) {
      return "info"; // Use info variant for delegated status
    }

    switch (status) {
      case ERentalStatus.AVAILABLE:
        return "success";
      case ERentalStatus.RENTED:
        return "warning";
      case ERentalStatus.EXPIRED:
        return "secondary";
      case ERentalStatus.CANCELLED:
        return "danger";
      default:
        return "secondary";
    }
  };

  const getStatusColor = (status: ERentalStatus, isDelegated: boolean) => {
    if (isDelegated) {
      return "bg-blue-600/90 text-white border-blue-500 backdrop-blur-sm";
    }

    switch (status) {
      case ERentalStatus.AVAILABLE:
        return "bg-green-600/90 text-white border-green-500 backdrop-blur-sm";
      case ERentalStatus.RENTED:
        return "bg-yellow-600/90 text-white border-yellow-500 backdrop-blur-sm";
      case ERentalStatus.EXPIRED:
        return "bg-gray-600/90 text-white border-gray-500 backdrop-blur-sm";
      case ERentalStatus.CANCELLED:
        return "bg-red-600/90 text-white border-red-500 backdrop-blur-sm";
      default:
        return "bg-gray-600/90 text-white border-gray-500 backdrop-blur-sm";
    }
  };

  return (
    <span
      className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(status, isDelegated)} ${className}`}
    >
      {getDisplayLabel()}
    </span>
  );
}
