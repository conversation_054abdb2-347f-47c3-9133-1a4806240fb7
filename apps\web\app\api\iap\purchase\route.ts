import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get("jwt")?.value;

    if (!token) {
      return NextResponse.json(
        { status: 0, message: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await request.json();

    const response = await fetch(`${process.env.IAP_API_URL}/api/purchase`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();

    if (!response.ok) {
      const error = await response.json();

      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json({ data: data.data }, { status: 200 });
  } catch (error) {
    console.error("Error purchase", error);
    return NextResponse.json(
      { status: 0, message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get("jwt")?.value;
    const { searchParams } = new URL(request.url);
    const txHash = searchParams.get("txHash");

    if (!token) {
      return NextResponse.json(
        { status: 0, message: "Unauthorized" },
        { status: 401 }
      );
    }

    if (!txHash) {
      return NextResponse.json(
        { status: 0, message: "Transaction Hash is required" },
        { status: 400 }
      );
    }

    const response = await fetch(
      `${process.env.IAP_API_URL}/api/purchase/status?txHash=${txHash}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    const data = await response.json();

    if (!response.ok) {
      const error = await response.json();

      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json({ data: data.data }, { status: 200 });
  } catch (error) {
    console.error("Error purchase status check", error);
    return NextResponse.json(
      { status: false, message: "Internal server error" },
      { status: 500 }
    );
  }
}
