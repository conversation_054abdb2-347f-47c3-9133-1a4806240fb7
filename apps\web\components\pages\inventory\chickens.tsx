"use client";

import { Loading } from "@/components/shared/loading";
import NotReady from "@/components/shared/not-ready";
import { CreateRentalModal } from "@/features/delegation/components/create-rental/create-rental-modal";
import { useChickendOwned } from "@/hooks/useChickendOwned";
import { useStateContext } from "@/providers/app/state";
import { cn } from "@/utils/classes";
import {
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  Heart,
  MoreHorizontal,
  MountainSnow,
  Search,
  Swords,
  Clock,
  Plus,
  Cookie,
  Egg,
} from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import BattleModal from "./battle-modal";
import { FeedModal } from "./feed-modal";
import useFoodCraftingStore from "@/store/food-crafting";
import useDailyFeedStore from "@/store/daily-feeding";
import HealModal from "./heal-modal";
import { ChickenStats } from "@/types/chicken.type";
import { toast } from "sonner";

import { InventoryTabs, InventoryTabType } from "./inventory-tabs";
import {
  DelegationStatusBadge,
  getChickenDelegationStatus,
} from "./delegation-status-badge";
import {
  useDelegatedChickens,
  IDelegatedChicken,
} from "@/features/delegation/hooks/useDelegatedChickens";
import { useChickensForDelegation } from "@/features/delegation/hooks/useChickensForDelegation";
import { useMyRentals } from "@/features/delegation/hooks/useMyRentals";
import { DelegationDetailsDialog } from "./delegation-details-dialog";

interface Chicken {
  tokenId: string;
  image: string;
  attributes?: {
    Type?: string[];
  };
}

type SortableAttribute = "id" | "hp" | "level" | "attack" | "defense" | "speed";

// Create a custom hook for managing favorites
function useFavoriteChickens() {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [isLoaded, setIsLoaded] = useState(false);

  // Load favorites from localStorage
  useEffect(() => {
    try {
      const storedFavorites = localStorage.getItem("favoriteChickens");
      if (storedFavorites) {
        const parsedFavorites = JSON.parse(storedFavorites);
        if (Array.isArray(parsedFavorites)) {
          setFavorites(new Set(parsedFavorites));
        }
      }
    } catch (e) {
      console.error("Error loading favorites from localStorage:", e);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save favorites to localStorage whenever they change
  useEffect(() => {
    // Only save after initial load to prevent overwriting with empty set
    if (isLoaded) {
      try {
        localStorage.setItem(
          "favoriteChickens",
          JSON.stringify([...favorites])
        );
      } catch (e) {
        console.error("Error saving favorites to localStorage:", e);
      }
    }
  }, [favorites, isLoaded]);

  // Toggle a chicken's favorite status
  const toggleFavorite = useCallback((tokenId: string) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(tokenId)) {
        newFavorites.delete(tokenId);
      } else {
        newFavorites.add(tokenId);
      }
      return newFavorites;
    });
  }, []);

  // Check if a chicken is favorited
  const isFavorite = useCallback(
    (tokenId: string) => favorites.has(tokenId),
    [favorites]
  );

  return {
    favorites,
    toggleFavorite,
    isFavorite,
    isLoaded,
  };
}

export default function Chickens() {
  const router = useRouter();
  const { address } = useStateContext();
  const { fetchFoodBalance } = useFoodCraftingStore();
  const { checkApproval } = useDailyFeedStore();
  const [showFeedModal, setShowFeedModal] = useState<boolean>(false);

  // Tab state
  const [activeTab, setActiveTab] = useState<InventoryTabType>("owned");

  // Owned chickens data
  const { isLoading: loading } = useChickendOwned(address as string);

  // Delegated chickens data
  const {
    delegatedChickens,
    isLoading: delegatedLoading,
    error: delegatedError,
  } = useDelegatedChickens();

  // Delegation status for owned chickens
  const {
    chickens: ownedChickensWithStatus,
    isLoading: ownedChickensLoading,
    error: ownedChickensError,
  } = useChickensForDelegation();

  // Fetch rental data for bulk operations instead of individual API calls
  const { ownedRentals, rentedChickens } = useMyRentals(address);

  // Create a map for efficient rental lookup by chicken token ID
  const ownedRentalsMap = useMemo(() => {
    const map = new Map();
    ownedRentals.forEach((rental) => {
      map.set(rental.chickenTokenId, rental);
    });
    return map;
  }, [ownedRentals]);

  const [chickenStats, setChickenStats] = useState<
    Record<string, ChickenStats>
  >({});
  const [statsLoading, setStatsLoading] = useState<Set<string>>(new Set());
  const [selectedChicken, setSelectedChicken] = useState<number>();

  const [searchQuery, setSearchQuery] = useState("");
  const [openMenuId, setOpenMenuId] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState<{
    [key: string]: "top" | "bottom";
  }>({});
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;
  const [battleModalChicken, setBattleModalChicken] = useState<string | null>(
    null
  );

  // HP Cooldown tracking
  const [cooldownTimers, setCooldownTimers] = useState<Record<string, number>>(
    {}
  );
  const cooldownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Recovery countdown tracking for faint chickens
  const [recoveryTimers, setRecoveryTimers] = useState<Record<string, number>>(
    {}
  );
  const recoveryIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const [sortAttribute, setSortAttribute] = useState<SortableAttribute>(() => {
    const savedSort = localStorage.getItem("chickenSortAttribute");
    return (savedSort as SortableAttribute) || "id";
  });

  const [sortOrder, setSortOrder] = useState<"asc" | "desc">(() => {
    const savedOrder = localStorage.getItem("chickenSortOrder");
    return (savedOrder as "asc" | "desc") || "asc";
  });
  const [showSortDropdown, setShowSortDropdown] = useState(false);

  const [healModalChicken, setHealModalChicken] = useState<string | null>(null);
  const [healInfo, setHealInfo] = useState<{
    healsRemaining: number;
    maxHeals: number;
    resetTime: string;
  } | null>(null);
  const [healInfoLoading, setHealInfoLoading] = useState(false);
  const [breedingTimers, setBreedingTimers] = useState<Record<string, number>>(
    {}
  );
  const breedingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const hasRun = useRef(false);
  // Helper function to check if chicken is breeding
  const isBreeding = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    return stats?.state === "breeding";
  };

  // Helper function to format breeding time
  const formatBreedingTime = (seconds: number) => {
    if (seconds <= 0) return "Breeding complete";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  // Helper function to check if chicken is an egg
  const isEgg = (chicken: Chicken) => {
    return chicken?.attributes?.Type?.[0] === "egg";
  };

  // Helper function to check if chicken is faint
  const isFaint = useCallback(
    (tokenId: string) => {
      const stats = chickenStats[tokenId];
      return stats?.state === "faint";
    },
    [chickenStats]
  );

  // Helper function to check if chicken is dead
  const isDead = useCallback(
    (tokenId: string) => {
      const stats = chickenStats[tokenId];
      return stats?.state === "dead";
    },
    [chickenStats]
  );

  // Helper function to format recovery time
  const formatRecoveryTime = (seconds: number) => {
    if (seconds <= 0) return "Ready to recover";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  // Breeding timer effect for breeding chickens
  useEffect(() => {
    if (Object.keys(breedingTimers).length === 0) return;

    breedingIntervalRef.current = setInterval(() => {
      setBreedingTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];

          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            // Timer completed, breeding is done
            hasChanges = true;

            // Update chicken state to normal
            setChickenStats((prevStats) => {
              const currentStats = prevStats[tokenId];
              if (!currentStats) return prevStats;

              return {
                ...prevStats,
                [tokenId]: {
                  ...currentStats,
                  state: "normal",
                  breedingTime: undefined,
                },
              };
            });
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (breedingIntervalRef.current) {
        clearInterval(breedingIntervalRef.current);
      }
    };
  }, [breedingTimers]);

  useEffect(() => {
    if (hasRun.current) return;
    hasRun.current = true;

    checkApproval();
    fetchFoodBalance();
  }, [checkApproval, fetchFoodBalance]);

  const [isNotReadyOpen, setNotReadyOpen] = useState(false);
  const [isDelegateModalOpen, setIsDelegateModalOpen] = useState(false);
  const [selectedChickenForDelegate, setSelectedChickenForDelegate] = useState<
    number | null
  >(null);
  const [delegationDetailsChicken, setDelegationDetailsChicken] =
    useState<IDelegatedChicken | null>(null);

  // Use our custom favorites hook
  const { toggleFavorite, isFavorite } = useFavoriteChickens();

  useEffect(() => {
    localStorage.setItem("chickenSortAttribute", sortAttribute);
    localStorage.setItem("chickenSortOrder", sortOrder);
  }, [sortAttribute, sortOrder]);

  // Helper function to check if a chicken is delegated/rented out
  const isChickenDelegatedOut = useCallback(
    (tokenId: number) => {
      return ownedRentalsMap.has(tokenId);
    },
    [ownedRentalsMap]
  );

  // Get current chickens based on active tab
  const currentChickensData = useMemo(() => {
    if (activeTab === "owned") {
      // For "My Chickens" tab:
      // - Include owned chickens that are NOT delegated/rented out
      // - Include chickens delegated TO me
      // - Include chickens rented TO me

      const myAvailableChickens = ownedChickensWithStatus
        .filter((chicken) => !isChickenDelegatedOut(chicken.tokenId))
        .map((chicken) => ({
          tokenId: chicken.tokenId.toString(),
          image: chicken.image,
          attributes: chicken.metadata?.attributes
            ? {
                Type: [chicken.type || "Unknown"],
              }
            : undefined,
          rentalStatus: chicken.rentalStatus,
          chickenType: "owned" as const,
        }));

      // Add delegated to me chickens
      const delegatedToMeChickens = delegatedChickens.map((chicken) => ({
        tokenId: chicken.tokenId.toString(),
        image: chicken.image,
        attributes: chicken.metadata?.attributes
          ? {
              Type: [chicken.type || "Unknown"],
            }
          : undefined,
        isDelegated: true,
        delegationInfo: {
          delegatedTask: chicken.delegatedTask,
          rewardDistribution: chicken.rewardDistribution,
          ownerAddress: chicken.ownerAddress,
          renterAddress: chicken.renterAddress,
        },
        chickenType: "delegated-to-me" as const,
      }));

      // Add rented to me chickens
      const rentedToMeChickens = rentedChickens
        .filter((rental) => rental.roninPrice !== "0") // Only paid rentals
        .map((rental) => {
          // Extract type from metadata attributes
          const typeAttribute = rental.chickenMetadata?.attributes.find(
            (attr) => attr.trait_type === "Type"
          );

          return {
            tokenId: rental.chickenTokenId.toString(),
            image:
              rental.chickenMetadata?.image ||
              `https://chicken-api-ivory.vercel.app/api/image/${rental.chickenTokenId}.png`,
            attributes: rental.chickenMetadata?.attributes
              ? {
                  Type: [(typeAttribute?.value as string) || "Unknown"],
                }
              : undefined,
            isRented: true,
            rentalInfo: {
              delegatedTask: rental.delegatedTask,
              rewardDistribution: rental.rewardDistribution,
              ownerAddress: rental.ownerAddress,
              renterAddress: rental.renterAddress,
            },
            chickenType: "rented-to-me" as const,
          };
        });

      return [
        ...myAvailableChickens,
        ...delegatedToMeChickens,
        ...rentedToMeChickens,
      ];
    } else {
      // For "Delegated to others" tab:
      // - Show chickens that are delegated out
      // - Show chickens that are rented out

      return ownedChickensWithStatus
        .filter((chicken) => isChickenDelegatedOut(chicken.tokenId))
        .map((chicken) => {
          // Determine the correct chicken type based on rental status
          let chickenType: "delegated-out" | "listed-in-market" =
            "delegated-out";

          if (chicken.rentalStatus?.rentalStatus === "listed") {
            chickenType = "listed-in-market";
          } else if (
            chicken.rentalStatus?.rentalStatus === "rented" ||
            chicken.rentalStatus?.rentalStatus === "delegated"
          ) {
            chickenType = "delegated-out";
          }

          return {
            tokenId: chicken.tokenId.toString(),
            image: chicken.image,
            attributes: chicken.metadata?.attributes
              ? {
                  Type: [chicken.type || "Unknown"],
                }
              : undefined,
            rentalStatus: chicken.rentalStatus,
            chickenType,
            isDisabled: true, // All actions disabled for delegated out chickens
          };
        });
    }
  }, [
    activeTab,
    ownedChickensWithStatus,
    delegatedChickens,
    rentedChickens,
    isChickenDelegatedOut,
  ]);

  // Calculate paginated chickens
  const chickens = useMemo(() => {
    if (!currentChickensData.length) return [];

    const sortedChickens = [...currentChickensData].sort((a, b) => {
      // First handle eggs - always put them at the end
      const aIsEgg = isEgg(a);
      const bIsEgg = isEgg(b);

      if (aIsEgg && !bIsEgg) return 1;
      if (!aIsEgg && bIsEgg) return -1;
      if (aIsEgg && bIsEgg) {
        // If both are eggs, sort by ID
        return sortOrder === "asc"
          ? parseInt(a.tokenId) - parseInt(b.tokenId)
          : parseInt(b.tokenId) - parseInt(a.tokenId);
      }

      // For non-eggs, sort by the selected attribute
      if (sortAttribute === "id") {
        return sortOrder === "asc"
          ? parseInt(a.tokenId) - parseInt(b.tokenId)
          : parseInt(b.tokenId) - parseInt(a.tokenId);
      }

      // For battle stats, we need to access the chickenStats object
      const statsA = chickenStats[a.tokenId];
      const statsB = chickenStats[b.tokenId];

      // If stats aren't loaded yet, sort by ID as fallback
      if (!statsA || !statsB) {
        return sortOrder === "asc"
          ? parseInt(a.tokenId) - parseInt(b.tokenId)
          : parseInt(b.tokenId) - parseInt(a.tokenId);
      }

      // Fix: For all stats, we need to reverse the logic to match the expected behavior
      // When sortOrder is "asc", we want higher stats to come first
      // When sortOrder is "desc", we want lower stats to come first
      if (sortAttribute === "hp") {
        return sortOrder === "asc"
          ? statsB.hp - statsA.hp // Higher HP first for ascending
          : statsA.hp - statsB.hp; // Lower HP first for descending
      } else {
        // For all other stats (attack, defense, speed, level)
        return sortOrder === "asc"
          ? statsB[sortAttribute] - statsA[sortAttribute] // Higher stats first for ascending
          : statsA[sortAttribute] - statsB[sortAttribute]; // Lower stats first for descending
      }
    });

    return sortedChickens;
  }, [currentChickensData, sortOrder, sortAttribute, chickenStats]);

  const ftChickens = useMemo(() => {
    if (searchQuery.trim() === "") {
      return chickens;
    } else {
      const filtered = chickens.filter((chicken) =>
        chicken.tokenId.includes(searchQuery.trim())
      );
      return filtered;
    }
  }, [chickens, searchQuery]);

  // Organize chickens with favorites at the top
  const organizedChickens = useMemo(() => {
    if (ftChickens.length === 0) return [];

    // Split into favorites and non-favorites
    const favoriteChickens = ftChickens.filter((chicken) =>
      isFavorite(chicken.tokenId)
    );

    const nonFavoriteChickens = ftChickens.filter(
      (chicken) => !isFavorite(chicken.tokenId)
    );

    // Combine with favorites at the top
    return [...favoriteChickens, ...nonFavoriteChickens];
  }, [ftChickens, isFavorite]);

  const totalPages = Math.ceil(organizedChickens.length / itemsPerPage);
  const paginatedChickens = organizedChickens.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Function to fetch stats for multiple chickens in batch with error handling and retry
  const fetchChickenStatsBatch = useCallback(
    async (tokenIds: string[]) => {
      // Filter out already loaded or loading chickens
      const idsToFetch = tokenIds.filter(
        (id) => !chickenStats[id] && !statsLoading.has(id)
      );

      if (idsToFetch.length === 0) return;

      // Mark all as loading
      setStatsLoading((prev) => new Set([...prev, ...idsToFetch]));

      // Retry logic
      const maxRetries = 3;
      let retries = 0;
      let success = false;

      while (retries < maxRetries && !success) {
        try {
          // Add timeout to prevent hanging requests
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

          const response = await fetch(
            `https://chicken-api-ivory.vercel.app/api/game/batch`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                ids: idsToFetch.map((id) => parseInt(id, 10)),
              }),
              signal: controller.signal,
            }
          );

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`API responded with status: ${response.status}`);
          }

          const data = await response.json();

          // Validate response structure
          if (!data.chickens || !Array.isArray(data.chickens)) {
            throw new Error("Invalid response format from API");
          }

          // Process the batch response
          const newStats: Record<string, ChickenStats> = {};
          const newCooldowns: Record<string, number> = {};
          const newRecoveryTimers: Record<string, number> = {};
          const newBreedingTimers: Record<string, number> = {};

          data.chickens.forEach((chicken: any) => {
            if (chicken && chicken.id) {
              newStats[chicken.id] = {
                hp: chicken.stats?.currentHp ?? 100,
                maxHp: chicken.stats?.hp ?? 100,
                level: chicken.level ?? 1,
                attack: chicken.stats?.attack ?? 0,
                defense: chicken.stats?.defense ?? 0,
                speed: chicken.stats?.speed ?? 0,
                hpCooldown: chicken.hpCooldown ?? 0,
                regenRate: chicken.regenRate ?? 1,
                state: chicken.state ?? "normal", // Add state from API
                recoverDate: chicken.recoverDate, // Add recovery date from API
                breedingTime: chicken.breedingTime,
              };

              // Set initial cooldown timer if hpCooldown exists
              if (chicken.hpCooldown && chicken.hpCooldown > 0) {
                newCooldowns[chicken.id] = Math.round(chicken.hpCooldown * 60); // Convert minutes to seconds
              }

              // Set initial recovery timer if chicken is faint and has recovery date
              if (chicken.state === "faint" && chicken.recoverDate) {
                const now = new Date();
                const recoverDate = new Date(chicken.recoverDate);
                const timeRemaining = recoverDate.getTime() - now.getTime();

                if (timeRemaining > 0) {
                  newRecoveryTimers[chicken.id] = Math.ceil(
                    timeRemaining / 1000
                  );
                }
              }

              // Set initial breeding timer if chicken is breeding and has breeding time
              if (chicken.state === "breeding" && chicken.breedingTime) {
                const now = Date.now(); // Current time in milliseconds
                const breedingEndTime = chicken.breedingTime * 1000; // Convert seconds to milliseconds
                const timeRemaining = breedingEndTime - now;

                if (timeRemaining > 0) {
                  newBreedingTimers[chicken.id] = Math.ceil(
                    timeRemaining / 1000
                  ); // Convert to seconds for countdown
                }
              }
            }
          });

          setChickenStats((prev) => ({
            ...prev,
            ...newStats,
          }));

          setCooldownTimers((prev) => ({
            ...prev,
            ...newCooldowns,
          }));

          setRecoveryTimers((prev) => ({
            ...prev,
            ...newRecoveryTimers,
          }));

          setBreedingTimers((prev) => ({
            ...prev,
            ...newBreedingTimers,
          }));

          success = true;
        } catch (error) {
          retries++;
          console.error(
            `Error fetching stats for chickens (attempt ${retries}/${maxRetries}):`,
            error
          );

          // Only wait before retrying if we're going to retry
          if (retries < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, 1000 * retries)); // Exponential backoff
          }
        }
      }

      // If all retries failed, set default stats
      if (!success) {
        const defaultStats: Record<string, ChickenStats> = {};
        idsToFetch.forEach((id) => {
          defaultStats[id] = {
            hp: 100,
            maxHp: 100,
            level: 1,
            attack: 0,
            defense: 0,
            speed: 0,
            hpCooldown: 0,
            regenRate: 1,
            state: "normal",
          };
        });

        setChickenStats((prev) => ({
          ...prev,
          ...defaultStats,
        }));
      }

      // Remove from loading set regardless of success/failure
      setStatsLoading((prev) => {
        const newSet = new Set(prev);
        idsToFetch.forEach((id) => newSet.delete(id));
        return newSet;
      });
    },
    [chickenStats, statsLoading]
  );

  async function refetchChickenStats() {
    if (!healModalChicken) {
      return;
    }

    const statsResponse = await fetch(
      `/api/proxy/game?tokenId=${healModalChicken}`
    );
    if (!statsResponse.ok) {
      throw new Error(`Failed to fetch game stats: ${statsResponse.status}`);
    }
    const statsData = await statsResponse.json();
    console.log(statsData);

    setChickenStats((prev) => {
      const newStats: Record<string, ChickenStats> = {};
      const prevChicken = prev[healModalChicken];
      newStats[healModalChicken] = {
        hp: statsData.stats?.currentHp || 100,
        maxHp: prevChicken?.maxHp ?? 100,
        level: prevChicken?.level ?? 1,
        attack: prevChicken?.attack ?? 0,
        defense: prevChicken?.defense ?? 0,
        speed: prevChicken?.speed ?? 0,
        hpCooldown: prevChicken?.hpCooldown ?? 0,
        regenRate: prevChicken?.regenRate ?? 1,
        state: prevChicken?.state ?? "normal", // Add state from API
        recoverDate: prevChicken?.recoverDate, // Add recovery date from API
      };
      return {
        ...prev,
        ...newStats,
      };
    });
  }

  // Recovery timer effect for faint chickens
  useEffect(() => {
    if (Object.keys(recoveryTimers).length === 0) return;

    recoveryIntervalRef.current = setInterval(() => {
      setRecoveryTimers((prev) => {
        const updated: Record<string, number> = {};
        let hasChanges = false;

        for (const tokenId in prev) {
          const timerValue = prev[tokenId];

          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            updated[tokenId] = timerValue - 1;
            hasChanges = true;
          } else if (timerValue === 0) {
            // Timer completed, chicken should be recovered
            hasChanges = true;

            // Update chicken state to normal
            setChickenStats((prevStats) => {
              const currentStats = prevStats[tokenId];
              if (!currentStats) return prevStats;

              return {
                ...prevStats,
                [tokenId]: {
                  ...currentStats,
                  state: "normal",
                  recoverDate: undefined,
                },
              };
            });
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (recoveryIntervalRef.current) {
        clearInterval(recoveryIntervalRef.current);
      }
    };
  }, [recoveryTimers]);

  // Cooldown timer effect with HP regeneration - completely rewritten to avoid type errors
  useEffect(() => {
    if (Object.keys(cooldownTimers).length === 0) return;

    cooldownIntervalRef.current = setInterval(() => {
      setCooldownTimers((prev) => {
        // Create a safe copy of the previous state
        const updated: Record<string, number> = {};
        let hasChanges = false;

        // Process each timer
        for (const tokenId in prev) {
          const timerValue = prev[tokenId];

          // Skip if timer value is invalid
          if (timerValue === undefined || timerValue === null) continue;

          if (timerValue > 0) {
            // Decrement timer
            updated[tokenId] = timerValue - 1;
            hasChanges = true;

            // Simulate HP regeneration every 60 seconds (1 minute)
            if (timerValue % 60 === 0) {
              // Update HP in a separate effect to avoid state update conflicts
              setChickenStats((prevStats) => {
                const currentStats = prevStats[tokenId];
                if (!currentStats) return prevStats;

                if (
                  currentStats.regenRate &&
                  currentStats.hp < currentStats.maxHp
                ) {
                  console.log("HP", currentStats.hp);
                  console.log("regenRate", currentStats.regenRate);
                  const newHp = Math.min(
                    currentStats.hp + (currentStats.regenRate || 1),
                    currentStats.maxHp
                  );
                  console.log("newHP", newHp);
                  return {
                    ...prevStats,
                    [tokenId]: {
                      ...currentStats,
                      hp: newHp,
                    },
                  };
                }
                return prevStats;
              });
            }
          } else if (timerValue === 0) {
            // Timer completed, don't include in updated state
            hasChanges = true;
            // Don't add to updated object, effectively removing it
          }
        }

        return hasChanges ? updated : prev;
      });
    }, 1000);

    return () => {
      if (cooldownIntervalRef.current) {
        clearInterval(cooldownIntervalRef.current);
      }
    };
  }, [cooldownTimers]);

  // Improved useEffect with debouncing to prevent too many API calls
  useEffect(() => {
    if (loading || paginatedChickens.length === 0) return;

    // Debounce the API call to prevent rapid calls during pagination/filtering
    const debounceTimer = setTimeout(() => {
      // Get all token IDs from visible chickens
      const tokenIds = paginatedChickens.map((chicken) => chicken.tokenId);

      // Fetch stats in batch
      fetchChickenStatsBatch(tokenIds);
    }, 300); // 300ms debounce

    return () => clearTimeout(debounceTimer);
  }, [paginatedChickens, loading, fetchChickenStatsBatch]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // Only close if clicking outside any menu button or dropdown
      const target = e.target as HTMLElement;
      if (
        !target.closest(".menu-button") &&
        !target.closest(".menu-dropdown") &&
        !target.closest(".sort-button")
      ) {
        setOpenMenuId(null);
        setMenuPosition({});
        setShowSortDropdown(false);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Handle chicken click
  const handleChickenClick = (tokenId: string) => {
    router.push(`/inventory/chickens/${tokenId}`);
  };

  // Handle battle action
  const handleBattle = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();

    // Check if chicken is faint or dead
    if (isFaint(tokenId) || isDead(tokenId)) {
      return; // Do nothing for faint/dead chickens
    }

    // Get chicken stats
    const stats = chickenStats[tokenId];

    // Check if HP is at least 50
    if (stats && stats.hp >= 50) {
      setBattleModalChicken(tokenId); // Show battle confirmation modal
    } else {
      // Show a notification that HP is too low
      toast.error(
        "Your chicken's HP is too low for battle. Please heal your chicken to at least 50 HP."
      );
    }
  };

  const closeBattleModal = () => {
    setBattleModalChicken(null);
  };

  // Handle breed action
  const handleBreed = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();

    // Check if chicken is faint or dead
    if (isFaint(tokenId) || isDead(tokenId)) {
      return; // Do nothing for faint/dead chickens
    }

    router.push(`/breeding?parent1=${tokenId}`);
  };

  // Toggle menu
  const toggleMenu = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();

    if (openMenuId === tokenId) {
      setOpenMenuId(null);
      setMenuPosition({});
      return;
    }

    // Calculate position based on available space
    const button = e.currentTarget as HTMLElement;
    const rect = button.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const dropdownHeight = 160; // Approximate height of dropdown with 4 items
    const spaceBelow = viewportHeight - rect.bottom;
    const spaceAbove = rect.top;

    // Position dropdown above if there's not enough space below
    const position =
      spaceBelow < dropdownHeight && spaceAbove > dropdownHeight
        ? "top"
        : "bottom";

    setMenuPosition((prev) => ({ ...prev, [tokenId]: position }));
    setOpenMenuId(tokenId);
  };

  // Handle favorite toggle with event stopping
  const handleToggleFavorite = (e: React.MouseEvent, tokenId: string) => {
    e.stopPropagation();
    e.preventDefault();
    toggleFavorite(tokenId);
  };

  // Handle delegation details for owned chickens that are delegated out
  const handleOwnedChickenDelegationDetails = (ownedChicken: any) => {
    // Find the rental data from bulk rental data instead of making individual API call
    const rental = ownedRentalsMap.get(ownedChicken.tokenId);

    if (rental) {
      // Create a compatible IDelegatedChicken object
      const delegatedChickenData: IDelegatedChicken = {
        tokenId: ownedChicken.tokenId,
        image: ownedChicken.image,
        metadata: ownedChicken.metadata,
        dailyFeathers: ownedChicken.dailyFeathers || 0,
        breedCount: ownedChicken.breedCount || 0,
        type: ownedChicken.type,
        level: ownedChicken.level || 1,
        winRate: ownedChicken.winRate || 0,
        // Delegation specific fields from rental data
        delegatedTask: rental.delegatedTask || 0,
        rewardDistribution: rental.rewardDistribution || 0,
        sharedRewardAmount: rental.sharedRewardAmount || null,
        renterAddress: rental.renterAddress || "",
        ownerAddress: rental.ownerAddress || "",
        legendaryCount: 0, // Default value, could be extracted from metadata if needed
        isDelegated: true,
      };

      setDelegationDetailsChicken(delegatedChickenData);
    } else {
      console.warn(
        `No rental data found for owned chicken ${ownedChicken.tokenId}`
      );
    }
  };

  // Handle menu actions
  const handleMenuAction = (
    e: React.MouseEvent,
    action: string,
    tokenId: string
  ) => {
    e.stopPropagation();
    e.preventDefault();
    setOpenMenuId(null);
    setMenuPosition({});

    // Handle different actions
    switch (action) {
      case "feed":
        // Check if chicken is faint or dead
        if (isFaint(tokenId) || isDead(tokenId)) {
          return; // Do nothing for faint/dead chickens
        }

        setSelectedChicken(Number(tokenId));
        setShowFeedModal(true);
        break;
      case "delegate":
        setSelectedChickenForDelegate(Number(tokenId));
        setIsDelegateModalOpen(true);
        break;
      case "release":
        // Show confirmation dialog for release
        // if (confirm(`Are you sure you want to release Chicken #${tokenId}?`)) {
        //   console.log(`Release chicken ${tokenId}`);
        //   // Implement release logic
        // }
        setNotReadyOpen(true);
        break;
      case "delegationDetails": {
        if (activeTab === "delegated-out") {
          // For delegated out chickens, we need to create delegation details from rental data
          const ownedChicken = ownedChickensWithStatus.find(
            (chicken) => chicken.tokenId.toString() === tokenId
          );
          if (ownedChicken) {
            handleOwnedChickenDelegationDetails(ownedChicken);
          }
        } else if (activeTab === "owned") {
          // Check if this is a delegated to me chicken
          const delegatedChicken = delegatedChickens.find(
            (chicken) => chicken.tokenId.toString() === tokenId
          );
          if (delegatedChicken) {
            setDelegationDetailsChicken(delegatedChicken);
          } else {
            // For owned chickens that are delegated out, create a compatible object
            const ownedChicken = ownedChickensWithStatus.find(
              (chicken) => chicken.tokenId.toString() === tokenId
            );
            if (
              ownedChicken &&
              (ownedChicken.rentalStatus?.rentalStatus === "delegated" ||
                ownedChicken.rentalStatus?.rentalStatus === "rented")
            ) {
              // We need to fetch the rental details to get delegation info
              handleOwnedChickenDelegationDetails(ownedChicken);
            }
          }
        }
        break;
      }
      default:
        break;
    }
  };

  const fetchHealInfo = useCallback(async () => {
    if (!address) return;

    setHealInfoLoading(true);
    try {
      const response = await fetch(
        `https://chicken-api-ivory.vercel.app/api/game/heal?address=${address}`
      );

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setHealInfo({
          healsRemaining: data.healsRemaining,
          maxHeals: data.maxHeals,
          resetTime: "00:00:00 UTC",
        });
      } else {
        console.error("Error fetching heal info:", data.error);
        setHealInfo(null);
      }
    } catch (error) {
      console.error("Error fetching heal info:", error);
      setHealInfo(null);
    } finally {
      setHealInfoLoading(false);
    }
  }, [address]);

  // Add this function to handle healing
  const handleHeal = useCallback(
    async (tokenId: string) => {
      if (!address) return false;

      try {
        const res = await fetch("/csrf-token");
        if (!res.ok) {
          throw new Error(`API call failed: ${res.statusText}`);
        }
        const { csrfToken } = await res.json();

        const response = await fetch("/api/proxy/heal", {
          method: "POST",
          headers: {
            "X-CSRF-Token": csrfToken,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: parseInt(tokenId, 10),
            address: address,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            errorData.error || `API responded with status: ${response.status}`
          );
        }

        const data = await response.json();

        if (data.success) {
          // Update chicken stats with the healed data
          setChickenStats((prev) => ({
            ...prev,
            [tokenId]: {
              ...prev[tokenId],
              hp: prev[tokenId]?.maxHp || data.chicken.stats.currentHp,
              maxHp: prev[tokenId]?.maxHp || data.chicken.stats.hp,
              level: data.chicken.level,
              attack: data.chicken.stats.attack,
              defense: data.chicken.stats.defense,
              speed: data.chicken.stats.speed,
            },
          }));

          // Update heal info
          setHealInfo((prev) =>
            prev
              ? {
                  ...prev,
                  healsRemaining: data.healsRemaining,
                }
              : null
          );

          return true;
        } else {
          console.error("Healing failed:", data.error);
          return false;
        }
      } catch (error) {
        console.error("Error healing chicken:", error);
        throw error;
      }
    },
    [address]
  );

  // Add this function to open the heal modal
  const openHealModal = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is faint or dead
      if (isFaint(tokenId) || isDead(tokenId)) {
        return; // Do nothing for faint/dead chickens
      }

      setHealModalChicken(tokenId);
      fetchHealInfo();
    },
    [fetchHealInfo, isFaint, isDead]
  );

  const openFeedModal = useCallback(
    (e: React.MouseEvent, tokenId: string) => {
      e.stopPropagation();
      e.preventDefault();

      // Check if chicken is faint or dead
      if (isFaint(tokenId) || isDead(tokenId)) {
        return; // Do nothing for faint/dead chickens
      }

      setSelectedChicken(Number(tokenId));
      setShowFeedModal(true);
    },
    [isFaint, isDead]
  );

  // Add this function to close the heal modal
  const closeHealModal = useCallback(() => {
    setHealModalChicken(null);
  }, []);

  // Add this function to perform the heal
  const performHeal = useCallback(async () => {
    if (!healModalChicken) return false;
    return await handleHeal(healModalChicken);
  }, [healModalChicken, handleHeal]);

  useEffect(() => {
    // Fetch heal info on component mount
    if (address) {
      fetchHealInfo();
    }

    // Set up a refresh interval (every 5 minutes)
    const refreshInterval = setInterval(
      () => {
        if (address) {
          fetchHealInfo();
        }
      },
      5 * 60 * 1000
    );

    return () => clearInterval(refreshInterval);
  }, [address, fetchHealInfo]);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      // Only close if clicking outside any menu button or dropdown
      const target = e.target as HTMLElement;
      if (
        !target.closest(".menu-button") &&
        !target.closest(".menu-dropdown")
      ) {
        setOpenMenuId(null);
        setMenuPosition({});
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  // Pagination navigation
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Helper function to get HP bar color based on percentage
  const getHpColor = (hp: number, maxHp: number) => {
    const percentage = (hp / maxHp) * 100;
    if (percentage > 70) return "bg-green-500";
    if (percentage > 30) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Format cooldown time
  const formatCooldownTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h:${minutes.toString().padStart(2, "0")}m:${remainingSeconds.toString().padStart(2, "0")}s`;
    } else {
      return `${minutes}m:${remainingSeconds.toString().padStart(2, "0")}s`;
    }
  };

  // Render HP bar with loading state
  const renderHpBar = (tokenId: string) => {
    const stats = chickenStats[tokenId];
    const isLoading = statsLoading.has(tokenId);

    if (!stats && isLoading) {
      // Loading state
      return (
        <div className="w-full bg-stone-600 rounded-full h-4 mt-1 animate-pulse relative overflow-hidden">
          <div className="absolute inset-0 bg-stone-500 "></div>
          <div className="absolute inset-0 flex justify-center items-center text-xs font-bold text-white">
            Loading...
          </div>
        </div>
      );
    }

    const hp = stats?.hp || 0;
    const maxHp = stats?.maxHp || 100;

    return (
      <div className="w-full bg-stone-600 rounded-full h-4 mt-1 relative">
        <div
          className={`${getHpColor(hp, maxHp)} h-4 rounded-full transition-all duration-300`}
          style={{ width: `${(hp / maxHp) * 100}%` }}
        ></div>
        <div className="absolute inset-0 flex justify-center items-center text-xs font-bold text-white">
          HP: {Math.round(hp)}/{Math.round(maxHp)}
        </div>
      </div>
    );
  };

  // Render cooldown timer
  const renderCooldownTimer = (tokenId: string) => {
    const cooldownSeconds = cooldownTimers[tokenId];
    const stats = chickenStats[tokenId];
    const hp = stats?.hp || 0;
    const maxHp = stats?.maxHp || 100;

    if (!cooldownSeconds || cooldownSeconds <= 0) return null;

    if (hp >= maxHp) return null;

    return (
      <div className="absolute top-2 left-1/2 transform -translate-x-1/2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
        {formatCooldownTime(cooldownSeconds)}
      </div>
    );
  };

  // Render recovery timer for faint chickens
  const renderRecoveryTimer = (tokenId: string) => {
    const recoverySeconds = recoveryTimers[tokenId];
    const stats = chickenStats[tokenId];

    if (!isFaint(tokenId) || !stats?.recoverDate) return null;

    if (!recoverySeconds || recoverySeconds <= 0) {
      return (
        <div className="absolute top-2 left-2 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Ready!
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 bg-orange-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        <Clock className="h-3 w-3" />
        {formatRecoveryTime(recoverySeconds)}
      </div>
    );
  };

  // Render faint overlay
  const renderFaintOverlay = (tokenId: string) => {
    if (!isFaint(tokenId)) return null;

    return (
      <div className="absolute inset-0 bg-black bg-opacity-60 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-red-400 font-bold text-lg mb-2">FAINT</div>
        <div className="text-white text-sm text-center px-2">
          {recoveryTimers[tokenId] && recoveryTimers[tokenId] > 0
            ? `Recovers in ${formatRecoveryTime(recoveryTimers[tokenId])}`
            : "Ready to recover!"}
        </div>
      </div>
    );
  };

  // Render dead overlay
  const renderDeadOverlay = (tokenId: string) => {
    if (!isDead(tokenId)) return null;

    return (
      <div className="absolute inset-0 bg-black bg-opacity-80 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-red-600 font-bold text-lg mb-2">DEAD</div>
        <div className="text-gray-400 text-sm text-center px-2">
          This chicken has died. Your chicken&apos;s journey has ended, but the
          bond remains forever.
        </div>
      </div>
    );
  };

  // Render breeding timer for breeding chickens
  const renderBreedingTimer = (tokenId: string) => {
    const breedingSeconds = breedingTimers[tokenId];
    const stats = chickenStats[tokenId];

    if (!isBreeding(tokenId) || !stats?.breedingTime) return null;

    if (!breedingSeconds || breedingSeconds <= 0) {
      return (
        <div className="absolute top-2 left-2 bg-green-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
          Complete!
        </div>
      );
    }

    return (
      <div className="absolute top-2 left-2 bg-pink-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
        <Egg className="h-3 w-3" />
        {formatBreedingTime(breedingSeconds)}
      </div>
    );
  };

  // Render breeding overlay
  const renderBreedingOverlay = (tokenId: string) => {
    if (!isBreeding(tokenId)) return null;

    return (
      <div className="absolute inset-0 bg-pink-900 bg-opacity-70 rounded-lg flex flex-col items-center justify-center z-20">
        <div className="text-pink-300 font-bold text-lg mb-2 flex items-center gap-2">
          <Egg className="h-6 w-6" />
          BREEDING
        </div>
        <div className="text-white text-sm text-center px-2">
          {breedingTimers[tokenId] && breedingTimers[tokenId] > 0
            ? `Completes in ${formatBreedingTime(breedingTimers[tokenId])}`
            : "Breeding complete!"}
        </div>
      </div>
    );
  };

  // Combined loading state
  const isCurrentTabLoading =
    activeTab === "owned" ? ownedChickensLoading : delegatedLoading;

  if (isCurrentTabLoading) return <Loading />;

  if (ownedChickensError && activeTab === "owned")
    return (
      <div className="text-center p-8">
        <p className="text-red-500 mb-2">Error loading chickens</p>
        <p className="text-white text-sm">{ownedChickensError.message}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          Try Again
        </button>
      </div>
    );

  if (delegatedError && activeTab === "delegated-out")
    return (
      <div className="text-center p-8">
        <p className="text-red-500 mb-2">Error loading delegated chickens</p>
        <p className="text-white text-sm">{delegatedError.message}</p>
        <button
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          onClick={() => window.location.reload()}
        >
          Try Again
        </button>
      </div>
    );

  return (
    <div className="space-y-6">
      {/* Inventory Tabs */}
      <InventoryTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        ownedCount={
          // Count for "My Chickens": owned chickens not delegated out + delegated to me + rented to me
          ownedChickensWithStatus.filter(
            (chicken) => !isChickenDelegatedOut(chicken.tokenId)
          ).length +
          delegatedChickens.length +
          rentedChickens.filter((rental) => rental.roninPrice !== "0").length
        }
        delegatedOutCount={
          // Count for "Delegated to others": chickens delegated/rented out
          ownedChickensWithStatus.filter((chicken) =>
            isChickenDelegatedOut(chicken.tokenId)
          ).length
        }
      />

      {/* Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between my-6">
        {/* Sort Control */}
        <div className="relative">
          <button
            className="flex items-center gap-2 bg-stone-700 text-white px-4 py-2 rounded-lg sort-button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setShowSortDropdown(!showSortDropdown);
            }}
          >
            Sort by{" "}
            {sortAttribute.charAt(0).toUpperCase() + sortAttribute.slice(1)}{" "}
            {sortOrder === "desc" ? "↓" : "↑"}
            <ChevronDown className="h-4 w-4" />
          </button>

          {showSortDropdown && (
            <div
              className={cn(
                "absolute right-0 mt-1 w-40 bg-stone-800 border border-stone-600",
                "rounded-lg shadow-lg overflow-hidden z-40 sort-dropdown"
              )}
            >
              <button
                className={cn(
                  "w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors",
                  sortAttribute === "id" && "bg-stone-700"
                )}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortAttribute("id");
                  setShowSortDropdown(false);
                }}
              >
                ID
              </button>
              <button
                className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${sortAttribute === "hp" ? "bg-stone-700" : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortAttribute("hp");
                  setShowSortDropdown(false);
                }}
              >
                HP
              </button>
              <button
                className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${sortAttribute === "level" ? "bg-stone-700" : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortAttribute("level");
                  setShowSortDropdown(false);
                }}
              >
                Level
              </button>
              <button
                className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${sortAttribute === "attack" ? "bg-stone-700" : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortAttribute("attack");
                  setShowSortDropdown(false);
                }}
              >
                Attack
              </button>
              <button
                className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${sortAttribute === "defense" ? "bg-stone-700" : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortAttribute("defense");
                  setShowSortDropdown(false);
                }}
              >
                Defense
              </button>
              <button
                className={`w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors ${sortAttribute === "speed" ? "bg-stone-700" : ""}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortAttribute("speed");
                  setShowSortDropdown(false);
                }}
              >
                Speed
              </button>
              <div className="border-t border-stone-600 my-1"></div>
              <button
                className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"));
                  setShowSortDropdown(false);
                }}
              >
                {sortOrder === "asc" ? "Descending ↓" : "Ascending ↑"}
              </button>
            </div>
          )}
        </div>
        {/* Search */}
        <div className="relative w-full md:w-64">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
            <Search className="h-5 w-5" />
          </div>
          <input
            type="text"
            className="bg-stone-700 text-white pl-10 pr-4 py-2 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Search by ID..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2">
          <div
            className={`px-4 py-2 rounded-lg flex items-center gap-2 ${
              healInfoLoading
                ? "bg-stone-700"
                : "bg-stone-800 border border-stone-700"
            }`}
          >
            {/* <Heart
              className={`h-5 w-5 ${
                healInfo && healInfo.healsRemaining > 0
                  ? "text-red-500"
                  : "text-stone-500"
              }`}
            /> */}

            {healInfoLoading ? (
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-blue-500 animate-spin"></div>
                <span className="text-stone-400">Loading...</span>
              </div>
            ) : healInfo ? (
              <div className="flex flex-col">
                <span className="text-white font-medium">
                  {healInfo.healsRemaining} / {healInfo.maxHeals} Free Heals
                </span>
                <span className="text-xs text-stone-400">
                  Resets at {healInfo.resetTime}
                </span>
              </div>
            ) : (
              <span className="text-stone-400">No heal info</span>
            )}
          </div>

          {/* Add a refresh button */}
          <button
            onClick={() => fetchHealInfo()}
            disabled={healInfoLoading}
            className="p-2 bg-stone-700 hover:bg-stone-600 rounded-lg text-white transition-colors"
            title="Refresh heal count"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`${healInfoLoading ? "animate-spin" : ""}`}
            >
              <path d="M21 12a9 9 0 0 1-9 9c-4.97 0-9-4.03-9-9s4.03-9 9-9h3"></path>
              <path d="M21 3v6h-6"></path>
              <path d="M21 9a9 9 0 0 0-9 3"></path>
            </svg>
          </button>
        </div>
      </div>

      <>
        {ftChickens.length === 0 && !isCurrentTabLoading ? (
          <div className="text-center text-white p-8">
            {activeTab === "owned"
              ? "You don't have any chickens yet."
              : "You don't have any chickens delegated to others yet."}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 overflow-visible">
            {paginatedChickens.map((chicken) => {
              const chickenIsFavorite = isFavorite(chicken.tokenId);
              const chickenIsEgg = isEgg(chicken);
              const chickenIsFaint = isFaint(chicken.tokenId);
              const chickenIsDead = isDead(chicken.tokenId);
              const chickenIsBreeding = isBreeding(chicken.tokenId);

              // Get delegation status for this chicken
              const delegationStatus = getChickenDelegationStatus(
                chicken,
                address
              );

              return (
                <div
                  key={chicken.tokenId}
                  className={cn(
                    "bg-stone-800 border rounded-lg p-4 shadow-md cursor-pointer transition-all relative",
                    chickenIsFavorite && "border-yellow-500",
                    chickenIsBreeding && "border-pink-500",
                    !chickenIsFavorite &&
                      !chickenIsBreeding &&
                      "border-stone-700 hover:border-stone-500",
                    (chickenIsFaint || chickenIsDead || chickenIsBreeding) &&
                      "opacity-80"
                  )}
                  onClick={() => handleChickenClick(chicken.tokenId)}
                >
                  {/* HP Cooldown Timer - only show for non-eggs and non-faint/dead chickens */}
                  {!chickenIsEgg &&
                    !chickenIsFaint &&
                    !chickenIsDead &&
                    renderCooldownTimer(chicken.tokenId)}

                  {/* Recovery Timer - only show for faint chickens */}
                  {chickenIsFaint && renderRecoveryTimer(chicken.tokenId)}
                  {chickenIsBreeding && renderBreedingTimer(chicken.tokenId)}

                  {/* Favorite heart button - overlay on upper right of image */}
                  <button
                    className={`absolute top-2 right-2 p-1 ${
                      chickenIsFavorite
                        ? "bg-yellow-500 text-black"
                        : "bg-black bg-opacity-60 text-white hover:bg-yellow-600"
                    } rounded-full transition-colors z-30`}
                    onClick={(e) => handleToggleFavorite(e, chicken.tokenId)}
                    aria-label={
                      chickenIsFavorite
                        ? "Remove from favorites"
                        : "Add to favorites"
                    }
                    title={
                      chickenIsFavorite
                        ? "Remove from favorites"
                        : "Add to favorites"
                    }
                  >
                    <Heart
                      className={`h-4 w-4 ${chickenIsFavorite ? "fill-black" : ""}`}
                    />
                  </button>

                  {/* Delegation Status Badge */}
                  <div
                    className={`absolute top-2 ${chickenIsFavorite ? "right-2" : "left-2"} z-40`}
                  >
                    <DelegationStatusBadge
                      isDelegated={delegationStatus.isDelegated}
                      isDelegatedOut={delegationStatus.isDelegatedOut}
                      chickenType={
                        "chickenType" in chicken
                          ? chicken.chickenType
                          : undefined
                      }
                    />
                  </div>

                  {/* HP Cooldown Timer - only show for non-eggs */}
                  {!chickenIsEgg && renderCooldownTimer(chicken.tokenId)}

                  <div className="aspect-square relative mb-3 overflow-hidden rounded-lg">
                    {chicken.image ? (
                      <>
                        {/* Loading placeholder */}
                        <div
                          className="absolute inset-0 animate-pulse bg-stone-700/30 rounded z-10"
                          id={`placeholder-${chicken.tokenId}`}
                        ></div>

                        <Image
                          src={chicken.image}
                          alt={`Chicken #${chicken.tokenId}`}
                          fill
                          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                          className={`object-cover z-20 transition-all duration-300 ${
                            chickenIsFaint || chickenIsDead
                              ? "grayscale"
                              : chickenIsBreeding
                                ? "sepia-[0.3] hue-rotate-[300deg] saturate-[1.8]" // Pink tint
                                : ""
                          }`}
                          onLoad={() => {
                            // Hide the placeholder when image loads
                            const placeholder = document.getElementById(
                              `placeholder-${chicken.tokenId}`
                            );
                            if (placeholder) {
                              placeholder.style.display = "none";
                            }
                          }}
                          loading="lazy"
                        />
                      </>
                    ) : (
                      <div className="w-full h-full animate-pulse space-y-4 flex items-center justify-center">
                        <div className="space-y-2 w-full">
                          <div className="h-24 bg-stone-700/30 rounded mx-auto"></div>
                          <div className="h-8 bg-stone-700/30 rounded w-3/4 mx-auto"></div>
                          <div className="h-4 bg-stone-700/30 rounded w-1/2 mx-auto"></div>
                        </div>
                      </div>
                    )}

                    {/* Faint/Dead Overlays */}
                    {renderFaintOverlay(chicken.tokenId)}
                    {renderBreedingOverlay(chicken.tokenId)}
                    {renderDeadOverlay(chicken.tokenId)}
                  </div>

                  {/* Level and HP Bar - only show for non-eggs and non-faint/dead chickens */}
                  {!chickenIsEgg &&
                    !chickenIsFaint &&
                    !chickenIsDead &&
                    !chickenIsBreeding && (
                      <div className="w-full mt-2 mb-3">
                        <div className="text-center text-base font-semibold mb-1">
                          Lvl {chickenStats[chicken.tokenId]?.level || 1}
                        </div>
                        {renderHpBar(chicken.tokenId)}
                      </div>
                    )}

                  {/* Level only for faint/dead chickens - no HP bar */}
                  {!chickenIsEgg &&
                    (chickenIsFaint || chickenIsDead || chickenIsBreeding) && (
                      <div className="w-full mt-2 mb-3">
                        <div className="text-center text-base font-semibold">
                          Lvl {chickenStats[chicken.tokenId]?.level || 1}
                        </div>
                      </div>
                    )}

                  {/* Chicken ID and Action Buttons */}
                  <div className="flex items-center justify-between">
                    <div className="font-medium text-white">
                      Chicken #{chicken.tokenId}
                    </div>

                    <div className="flex items-center gap-1">
                      {!chickenIsEgg &&
                        (() => {
                          const chickenType =
                            "chickenType" in chicken
                              ? chicken.chickenType
                              : "owned";

                          // For delegated-out chickens, disable all actions
                          if (
                            chickenType === "delegated-out" ||
                            ("isDisabled" in chicken && chicken.isDisabled)
                          ) {
                            return null; // No action buttons for delegated out chickens
                          }

                          // Check delegation/rental info
                          const delegationInfo =
                            "delegationInfo" in chicken
                              ? chicken.delegationInfo
                              : null;
                          const rentalInfo =
                            "rentalInfo" in chicken ? chicken.rentalInfo : null;
                          const accessInfo = delegationInfo || rentalInfo;

                          // Determine available actions based on chicken type
                          let canBattle = false;
                          let canBreed = false;
                          let canFeed = false;
                          let canHeal = false;

                          if (chickenType === "owned") {
                            // Owned chickens have full access
                            canBattle = true;
                            canBreed = true;
                            canFeed = true;
                            canHeal = true;
                          } else if (
                            chickenType === "delegated-to-me" ||
                            chickenType === "rented-to-me"
                          ) {
                            // Delegated/rented to me chickens have restricted access
                            // Battle: depends on access (delegatedTask includes GAMEPLAY or BOTH)
                            canBattle =
                              accessInfo?.delegatedTask === 2 ||
                              accessInfo?.delegatedTask === 3; // GAMEPLAY or BOTH
                            // Breed: NO (always hidden for delegated/rented)
                            canBreed = false;
                            // Feed: YES (always available)
                            canFeed = true;
                            // Heal: depends on access (delegatedTask includes DAILY_RUB or BOTH)
                            canHeal =
                              accessInfo?.delegatedTask === 1 ||
                              accessInfo?.delegatedTask === 3; // DAILY_RUB or BOTH
                          }

                          return (
                            <>
                              {/* Battle Button */}
                              {canBattle && (
                                <button
                                  className={`p-1.5 rounded-full text-white transition-colors ${
                                    chickenIsFaint || chickenIsDead
                                      ? "bg-stone-600 cursor-not-allowed opacity-50"
                                      : "bg-stone-700 hover:bg-red-700"
                                  }`}
                                  onClick={(e) =>
                                    handleBattle(e, chicken.tokenId)
                                  }
                                  aria-label="Battle"
                                  title={
                                    chickenIsFaint
                                      ? "Cannot battle - chicken is faint"
                                      : chickenIsDead
                                        ? "Cannot battle - chicken is dead"
                                        : "Battle"
                                  }
                                  disabled={chickenIsFaint || chickenIsDead}
                                >
                                  <Swords className="h-4 w-4" />
                                </button>
                              )}

                              {/* Breed Button */}
                              {canBreed && (
                                <button
                                  className={`p-1.5 rounded-full text-white transition-colors ${
                                    chickenIsFaint || chickenIsDead
                                      ? "bg-stone-600 cursor-not-allowed opacity-50"
                                      : "bg-stone-700 hover:bg-pink-700"
                                  }`}
                                  onClick={(e) =>
                                    handleBreed(e, chicken.tokenId)
                                  }
                                  aria-label="Breed"
                                  title={
                                    chickenIsFaint
                                      ? "Cannot breed - chicken is faint"
                                      : chickenIsDead
                                        ? "Cannot breed - chicken is dead"
                                        : "Breed"
                                  }
                                  disabled={chickenIsFaint || chickenIsDead}
                                >
                                  <MountainSnow className="h-4 w-4" />
                                </button>
                              )}

                              {/* Heal Button - Only show if delegation allows */}
                              {canHeal && (
                                <button
                                  className={`p-1.5 rounded-full text-white transition-colors ${
                                    chickenIsFaint || chickenIsDead
                                      ? "bg-stone-600 cursor-not-allowed opacity-50"
                                      : "bg-stone-700 hover:bg-green-700"
                                  }`}
                                  onClick={(e) =>
                                    openHealModal(e, chicken.tokenId)
                                  }
                                  aria-label="Heal"
                                  title={
                                    chickenIsFaint
                                      ? "Cannot heal - chicken is faint"
                                      : chickenIsDead
                                        ? "Cannot heal - chicken is dead"
                                        : "Heal"
                                  }
                                  disabled={chickenIsFaint || chickenIsDead}
                                >
                                  <Plus className="h-4 w-4" />
                                </button>
                              )}

                              {/* Feed Button - Only show if delegation allows */}
                              {canFeed && (
                                <button
                                  className={`p-1.5 rounded-full text-white transition-colors ${
                                    chickenIsFaint || chickenIsDead
                                      ? "bg-stone-600 cursor-not-allowed opacity-50"
                                      : "bg-stone-700 hover:bg-blue-700"
                                  }`}
                                  onClick={(e) =>
                                    openFeedModal(e, chicken.tokenId)
                                  }
                                  aria-label="Feed"
                                  title={
                                    chickenIsFaint
                                      ? "Cannot feed - chicken is faint"
                                      : chickenIsDead
                                        ? "Cannot feed - chicken is dead"
                                        : "Feed"
                                  }
                                  disabled={chickenIsFaint || chickenIsDead}
                                >
                                  <Cookie className="h-4 w-4" />
                                </button>
                              )}
                            </>
                          );
                        })()}

                      {/* More Options Button */}
                      <div className="relative">
                        <button
                          className={`p-1.5 rounded-full text-white menu-button transition-colors ${
                            chickenIsFaint || chickenIsDead
                              ? "bg-stone-600 cursor-not-allowed opacity-50"
                              : "bg-stone-700 hover:bg-stone-600"
                          }`}
                          onClick={(e) => toggleMenu(e, chicken.tokenId)}
                          aria-label="More options"
                          title={
                            chickenIsFaint
                              ? "Options disabled - chicken is faint"
                              : chickenIsDead
                                ? "Options disabled - chicken is dead"
                                : "More options"
                          }
                          disabled={chickenIsFaint || chickenIsDead}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </button>

                        {/* Expanded Dropdown Menu - only show if chicken is not faint/dead */}
                        {openMenuId === chicken.tokenId &&
                          !chickenIsFaint &&
                          !chickenIsDead && (
                            <div
                              className={cn(
                                "absolute right-0 w-44 bg-stone-800 border border-stone-600",
                                "rounded-lg shadow-lg overflow-hidden z-50 menu-dropdown",
                                menuPosition[chicken.tokenId] === "top"
                                  ? "bottom-full mb-1"
                                  : "top-full mt-1"
                              )}
                            >
                              {(() => {
                                const chickenType =
                                  "chickenType" in chicken
                                    ? chicken.chickenType
                                    : "owned";

                                if (activeTab === "owned") {
                                  // Menu for "My Chickens" tab
                                  if (chickenType === "owned") {
                                    // Owned chickens - full menu
                                    return (
                                      <>
                                        <button
                                          className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                                          onClick={(e) =>
                                            handleMenuAction(
                                              e,
                                              "feed",
                                              chicken.tokenId
                                            )
                                          }
                                        >
                                          Feed
                                        </button>
                                        <button
                                          className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                                          onClick={(e) =>
                                            openHealModal(e, chicken.tokenId)
                                          }
                                        >
                                          Heal
                                        </button>
                                        {/* Only show Delegate button if chicken is not already delegated out */}
                                        {!delegationStatus.isDelegatedOut && (
                                          <button
                                            className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                                            onClick={(e) =>
                                              handleMenuAction(
                                                e,
                                                "delegate",
                                                chicken.tokenId
                                              )
                                            }
                                          >
                                            Delegate
                                          </button>
                                        )}
                                        <button
                                          className="w-full text-left px-4 py-2 text-red-400 hover:bg-stone-700 transition-colors"
                                          onClick={(e) =>
                                            handleMenuAction(
                                              e,
                                              "release",
                                              chicken.tokenId
                                            )
                                          }
                                        >
                                          Release
                                        </button>
                                      </>
                                    );
                                  } else if (
                                    chickenType === "delegated-to-me" ||
                                    chickenType === "rented-to-me"
                                  ) {
                                    // Delegated/rented to me chickens - restricted menu
                                    const accessInfo =
                                      "delegationInfo" in chicken
                                        ? chicken.delegationInfo
                                        : "rentalInfo" in chicken
                                          ? chicken.rentalInfo
                                          : null;

                                    // Heal: depends on access (delegatedTask includes DAILY_RUB or BOTH)
                                    const canHeal =
                                      accessInfo?.delegatedTask === 1 ||
                                      accessInfo?.delegatedTask === 3; // DAILY_RUB or BOTH

                                    return (
                                      <>
                                        {/* Feed: YES (always available) */}
                                        <button
                                          className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                                          onClick={(e) =>
                                            handleMenuAction(
                                              e,
                                              "feed",
                                              chicken.tokenId
                                            )
                                          }
                                        >
                                          Feed
                                        </button>
                                        {/* Heal: depends on access */}
                                        {canHeal && (
                                          <button
                                            className="w-full text-left px-4 py-2 text-white hover:bg-stone-700 transition-colors"
                                            onClick={(e) =>
                                              openHealModal(e, chicken.tokenId)
                                            }
                                          >
                                            Heal
                                          </button>
                                        )}
                                        {/* Delegation Details */}
                                        <button
                                          className="w-full text-left px-4 py-2 text-purple-400 hover:bg-stone-700 transition-colors"
                                          onClick={(e) =>
                                            handleMenuAction(
                                              e,
                                              "delegationDetails",
                                              chicken.tokenId
                                            )
                                          }
                                        >
                                          Delegation Details
                                        </button>
                                        {/* Release: NO (not available) */}
                                      </>
                                    );
                                  }
                                } else {
                                  // Menu for "Delegated to others" tab - show delegation details
                                  return (
                                    <button
                                      className="w-full text-left px-4 py-2 text-purple-400 hover:bg-stone-700 transition-colors"
                                      onClick={(e) =>
                                        handleMenuAction(
                                          e,
                                          "delegationDetails",
                                          chicken.tokenId
                                        )
                                      }
                                    >
                                      Delegation Details
                                    </button>
                                  );
                                }

                                return null;
                              })()}
                            </div>
                          )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </>

      {/* Pagination */}
      {ftChickens.length > itemsPerPage && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <button
            onClick={() => goToPage(currentPage - 1)}
            disabled={currentPage === 1}
            className={`p-2 rounded-lg ${
              currentPage === 1
                ? "bg-stone-700 text-stone-500 cursor-not-allowed"
                : "bg-stone-700 text-white hover:bg-stone-600"
            }`}
            aria-label="Previous page"
          >
            <ChevronLeft className="h-5 w-5" />
          </button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around current page
              let pageToShow: number;
              if (totalPages <= 5) {
                pageToShow = i + 1;
              } else if (currentPage <= 3) {
                pageToShow = i + 1;
              } else if (currentPage >= totalPages - 2) {
                pageToShow = totalPages - 4 + i;
              } else {
                pageToShow = currentPage - 2 + i;
              }

              return (
                <button
                  key={pageToShow}
                  onClick={() => goToPage(pageToShow)}
                  className={`h-8 w-8 flex items-center justify-center rounded-lg ${
                    currentPage === pageToShow
                      ? "bg-blue-600 text-white"
                      : "bg-stone-700 text-white hover:bg-stone-600"
                  }`}
                >
                  {pageToShow}
                </button>
              );
            })}

            {totalPages > 5 && currentPage < totalPages - 2 && (
              <>
                <span className="text-white">...</span>
                <button
                  onClick={() => goToPage(totalPages)}
                  className="h-8 w-8 flex items-center justify-center rounded-lg bg-stone-700 text-white hover:bg-stone-600"
                >
                  {totalPages}
                </button>
              </>
            )}
          </div>

          <button
            onClick={() => goToPage(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`p-2 rounded-lg ${
              currentPage === totalPages
                ? "bg-stone-700 text-stone-500 cursor-not-allowed"
                : "bg-stone-700 text-white hover:bg-stone-600"
            }`}
            aria-label="Next page"
          >
            <ChevronRight className="h-5 w-5" />
          </button>
        </div>
      )}

      {/* Battle Confirmation Modal */}
      <BattleModal
        isOpen={!!battleModalChicken}
        onClose={closeBattleModal}
        chickenId={battleModalChicken || ""}
        chickenImage={
          ftChickens.find((c) => c.tokenId === battleModalChicken)?.image
        }
        chickenStats={{
          level: chickenStats[battleModalChicken || ""]?.level,
          attack: chickenStats[battleModalChicken || ""]?.attack,
          defense: chickenStats[battleModalChicken || ""]?.defense,
          speed: chickenStats[battleModalChicken || ""]?.speed,
          hp: chickenStats[battleModalChicken || ""]?.hp,
          maxHp: chickenStats[battleModalChicken || ""]?.maxHp,
        }}
      />

      <NotReady
        isOpen={isNotReadyOpen}
        onClose={() => setNotReadyOpen(false)}
      />
      <CreateRentalModal
        isOpen={isDelegateModalOpen}
        onOpenChange={setIsDelegateModalOpen}
        preSelectedChickenId={selectedChickenForDelegate}
      />
      <FeedModal
        show={showFeedModal}
        setShow={setShowFeedModal}
        tokenId={selectedChicken!}
      />
      <HealModal
        isOpen={!!healModalChicken}
        onClose={closeHealModal}
        chickenId={healModalChicken || ""}
        chickenImage={
          ftChickens.find((c) => c.tokenId === healModalChicken)?.image
        }
        currentHp={chickenStats[healModalChicken || ""]?.hp}
        maxHp={chickenStats[healModalChicken || ""]?.maxHp}
        onHeal={performHeal}
        healInfo={healInfo}
        isLoading={healInfoLoading}
        refetch={refetchChickenStats}
        onBattle={() => {
          // Close heal modal and open battle modal using existing function
          if (healModalChicken) {
            setBattleModalChicken(healModalChicken);
          }
        }}
      />

      {/* Delegation Details Dialog */}
      <DelegationDetailsDialog
        isOpen={!!delegationDetailsChicken}
        onOpenChange={(open) => {
          if (!open) {
            setDelegationDetailsChicken(null);
          }
        }}
        delegatedChicken={delegationDetailsChicken}
      />
    </div>
  );
}
