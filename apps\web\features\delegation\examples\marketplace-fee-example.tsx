"use client";

import { useMarketplaceFee } from "../hooks/useMarketplaceFee";
import {
  calculateMarketplaceFee,
  calculateEarningsAfterFees,
  formatMarketplaceFeePercentage,
} from "../types/delegation.types";

/**
 * Example component demonstrating how to use the marketplace fee hook
 * and utility functions with the dynamic fee percentage from the contract
 */
export function MarketplaceFeeExample() {
  const { feePercentage, isLoading, error, isFromContract } = useMarketplaceFee();

  // Example rental amount
  const rentalAmount = 100; // 100 RON

  if (isLoading) {
    return <div>Loading marketplace fee percentage...</div>;
  }

  if (error) {
    console.warn("Failed to fetch fee from contract:", error);
  }

  const marketplaceFee = calculateMarketplaceFee(rentalAmount, feePercentage);
  const ownerEarnings = calculateEarningsAfterFees(rentalAmount, feePercentage);
  const feePercentageDisplay = formatMarketplaceFeePercentage(feePercentage);

  return (
    <div className="p-6 bg-gray-800 rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Marketplace Fee Example</h3>
      
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>Fee Source:</span>
          <span className={isFromContract ? "text-green-400" : "text-yellow-400"}>
            {isFromContract ? "Smart Contract" : "Fallback"}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Fee Percentage:</span>
          <span>{feePercentageDisplay}</span>
        </div>
        
        <div className="flex justify-between">
          <span>Rental Amount:</span>
          <span>{rentalAmount} RON</span>
        </div>
        
        <div className="flex justify-between text-red-400">
          <span>Marketplace Fee:</span>
          <span>-{marketplaceFee.toFixed(4)} RON</span>
        </div>
        
        <div className="flex justify-between text-green-400 font-medium border-t border-gray-600 pt-2">
          <span>Owner Earnings:</span>
          <span>{ownerEarnings.toFixed(4)} RON</span>
        </div>
      </div>
      
      {error && (
        <div className="mt-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded text-yellow-400 text-xs">
          Warning: Using fallback fee percentage due to contract read error
        </div>
      )}
    </div>
  );
}
