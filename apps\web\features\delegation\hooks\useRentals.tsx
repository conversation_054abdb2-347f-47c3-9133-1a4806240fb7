"use client";

import { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  IRentalWithMetadata,
  IAvailableRentalsResponse,
  IRental,
} from "../types/delegation.types";
import { DelegationAPI } from "../api/delegation.api";
import { useRentChicken } from "./useRentChicken";
import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { IChickenMetadata } from "@/lib/types/chicken.types";
import { Address } from "viem";

// Real API functions
const fetchAvailableRentals = async (
  page = 1,
  pageSize = 10
): Promise<IAvailableRentalsResponse> => {
  return await DelegationAPI.listAvailableRentals(page, pageSize);
};

// Helper function to create rental with real metadata
const createRentalWithRealMetadata = (
  rental: IRental,
  metadataMap: Record<number, IChickenMetadata>
): IRentalWithMetadata => {
  const metadata = metadataMap[rental.chickenTokenId];

  // Extract daily feathers and legendary count from metadata attributes
  const dailyFeathersAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Daily Feathers"
  );
  const legendaryCountAttr = metadata?.attributes.find(
    (attr) => attr.trait_type === "Legendary Count"
  );

  const dailyFeathers = dailyFeathersAttr?.value
    ? typeof dailyFeathersAttr.value === "string"
      ? parseInt(dailyFeathersAttr.value)
      : Number(dailyFeathersAttr.value)
    : 0;

  const legendaryCount = legendaryCountAttr?.value
    ? typeof legendaryCountAttr.value === "string"
      ? parseInt(legendaryCountAttr.value)
      : Number(legendaryCountAttr.value)
    : 0;

  return {
    ...rental,
    chickenMetadata: metadata,
    dailyFeathers,
    legendaryCount,
  };
};

export function useRentals() {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const { executeRentChicken, isRenting: isRentingBlockchain } =
    useRentChicken();

  // Fetch available rentals
  const {
    data: rentalsResponse,
    isLoading: isLoadingRentals,
    error,
    refetch,
  } = useQuery({
    queryKey: ["available-rentals", currentPage, pageSize],
    queryFn: () => fetchAvailableRentals(currentPage, pageSize),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });

  // Extract token IDs from rental data for metadata fetching
  const tokenIds = useMemo(() => {
    return (
      rentalsResponse?.data.data.map((rental) => rental.chickenTokenId) || []
    );
  }, [rentalsResponse?.data.data]);

  // Fetch chicken metadata for all rentals
  const {
    metadataMap,
    isLoading: isLoadingMetadata,
    error: metadataError,
  } = useChickenMetadata(tokenIds);

  // Convert rentals to include real metadata
  const rentalsWithMetadata: IRentalWithMetadata[] = useMemo(() => {
    if (!rentalsResponse?.data.data) return [];

    return rentalsResponse.data.data.map((rental) =>
      createRentalWithRealMetadata(rental, metadataMap)
    );
  }, [rentalsResponse?.data.data, metadataMap]);

  // Combined loading state
  const isLoading = isLoadingRentals || isLoadingMetadata;

  // Note: Rent chicken mutation is now handled by useRentChicken hook
  // which includes full blockchain interaction

  // Pagination helpers
  const totalPages = rentalsResponse?.data.meta.lastPage || 1;
  const hasNextPage = currentPage < totalPages;
  const hasPreviousPage = currentPage > 1;

  const goToNextPage = () => {
    if (hasNextPage) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (hasPreviousPage) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  // Rent a chicken with full blockchain interaction
  const handleRentChicken = async (rental: IRentalWithMetadata) => {
    try {
      const result = await executeRentChicken(
        rental.id,
        rental.ownerAddress as Address
      );
      return result?.success || false;
    } catch {
      return false;
    }
  };

  return {
    // Data
    rentals: rentalsWithMetadata,
    meta: rentalsResponse?.data.meta,

    // Loading states
    isLoading,
    isRenting: isRentingBlockchain,

    // Error states
    error: error || metadataError,

    // Actions
    refetch,
    rentChicken: handleRentChicken,

    // Pagination
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    goToNextPage,
    goToPreviousPage,
    goToPage,

    // Utilities
    reset: () => {
      setCurrentPage(1);
    },
  };
}
