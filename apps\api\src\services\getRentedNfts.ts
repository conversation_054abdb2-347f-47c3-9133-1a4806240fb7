import axios from "axios";
import { env } from "../env";
import { RentedNFT } from "../types";
import { getRedisStore } from "./redis-store";

const BREEDING_API_URL = env.BREEDING_API_URL || "";

const redisStore = getRedisStore();

export async function getRentedNfts(address: string) {
  try {
    const cachedRentedNFTs = await redisStore.get(`rentedNFTs:${address}`);
    const { data } = await axios.get(
      `${BREEDING_API_URL}/rentals/chickens-by-wallet?walletAddress=${address}`
    );

    if (
      cachedRentedNFTs &&
      JSON.stringify(cachedRentedNFTs) !== JSON.stringify(data.data)
    ) {
      await redisStore.remove(`rentedNFTs:${address}`);
    }

    const rentedNFTs = data.data as RentedNFT[];
    await redisStore.store(`rentedNFTs:${address}`, rentedNFTs);

    return rentedNFTs;
  } catch (error) {
    console.error("getRentedNfts: ", error);
    return [];
  }
}
