"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "ui";
import Image from "next/image";
import {
  Star,
  Trophy,
  Sword,
  Shield,
  Zap,
  Heart,
  Eye,
  Clock,
  Loader2,
} from "lucide-react";
import { Feathers } from "@/components/shared/icons/feathers";
import { IRentalWithMetadata } from "../../types/delegation.types";
import { IAttribute } from "@/lib/types/chicken.types";

interface IChickenDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  rental: IRentalWithMetadata | null;
}

interface IBattleStats {
  wins: number;
  losses: number;
  level?: number;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

const fetchChickenBattleStats = async (
  tokenId: number
): Promise<IBattleStats> => {
  try {
    const response = await fetch(`/api/proxy/game?tokenId=${tokenId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch battle stats: ${response.status}`);
    }
    const data = await response.json();
    return {
      wins: data.wins || 0,
      losses: data.losses || 0,
      level: data.level,
      stats: data.stats,
    };
  } catch (error) {
    console.warn(`Failed to fetch battle stats for chicken ${tokenId}:`, error);
    return {
      wins: 0,
      losses: 0,
    };
  }
};

export function ChickenDetailsModal({
  isOpen,
  onOpenChange,
  rental,
}: IChickenDetailsModalProps) {
  const [battleStats, setBattleStats] = useState<IBattleStats | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen && rental) {
      setLoading(true);
      fetchChickenBattleStats(rental.chickenTokenId)
        .then(setBattleStats)
        .finally(() => setLoading(false));
    }
  }, [isOpen, rental]);

  if (!rental) return null;

  const chickenType = String(
    rental.chickenMetadata?.attributes?.find(
      (attr) => attr.trait_type === "Type"
    )?.value || "Unknown"
  );

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "genesis":
        return "bg-purple-500/20 text-purple-400 border-purple-500/30";
      case "legacy":
        return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "ordinary":
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30";
    }
  };

  const getAttributeValue = (traitType: string): string | number => {
    return (
      rental.chickenMetadata?.attributes?.find(
        (attr: IAttribute) => attr.trait_type === traitType
      )?.value || "N/A"
    );
  };

  const totalBattles = battleStats ? battleStats.wins + battleStats.losses : 0;
  const winRate =
    totalBattles > 0 ? Math.round((battleStats!.wins / totalBattles) * 100) : 0;

  const bodyPartAttributes = [
    "Beak",
    "Comb",
    "Eyes",
    "Feet",
    "Wings",
    "Tail",
    "Body",
  ];

  const isLegendary = (value: string | number): boolean => {
    return (
      typeof value === "string" && value.toLowerCase().includes("legendary")
    );
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content size="xl" isDismissable={true}>
        <Modal.Header>
          <Modal.Title className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-yellow-400" />
            Chicken Details
          </Modal.Title>
        </Modal.Header>

        <Modal.Body className="space-y-4 pb-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin text-yellow-400" />
              <span className="ml-2 text-gray-400">
                Loading chicken stats...
              </span>
            </div>
          ) : (
            <>
              {/* Header with Image and Basic Info */}
              <div className="flex gap-4 p-4 bg-stone-800 rounded-lg">
                <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-stone-700 flex-shrink-0">
                  {rental.chickenMetadata?.image ? (
                    <Image
                      src={rental.chickenMetadata.image}
                      alt={
                        rental.chickenMetadata.name ||
                        `Chicken #${rental.chickenTokenId}`
                      }
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-500">
                      <Zap className="w-8 h-8" />
                    </div>
                  )}
                </div>

                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-2">
                    {rental.chickenMetadata?.name ||
                      `Chicken #${rental.chickenTokenId}`}
                  </h3>
                  <div className="flex items-center gap-2 mb-2">
                    <span
                      className={`inline-block text-xs px-2 py-1 rounded-full border font-medium ${getTypeColor(chickenType)}`}
                    >
                      {chickenType}
                    </span>
                    {battleStats?.level && (
                      <div className="flex items-center gap-1 bg-stone-700 px-2 py-1 rounded-full">
                        <Star className="w-3 h-3 text-yellow-400" />
                        <span className="text-white text-xs font-medium">
                          Level {battleStats.level}
                        </span>
                      </div>
                    )}
                  </div>
                  {totalBattles > 0 && (
                    <div className="flex items-center gap-1 bg-stone-700 px-2 py-1 rounded-full w-fit">
                      <Trophy className="w-3 h-3 text-yellow-400" />
                      <span className="text-white text-xs font-medium">
                        {winRate}% Win Rate
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Battle Stats */}
              {battleStats?.stats && (
                <div className="bg-stone-800 rounded-lg p-4">
                  <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                    <Sword className="w-4 h-4 text-red-400" />
                    Battle Stats
                  </h4>
                  <div className="grid grid-cols-3 gap-3">
                    <div className="bg-stone-700/50 rounded-lg p-3 text-center">
                      <Sword className="w-4 h-4 text-red-400 mx-auto mb-1" />
                      <div className="text-gray-400 text-xs">Attack</div>
                      <div className="text-white font-bold">
                        {battleStats.stats.attack || 0}
                      </div>
                    </div>
                    <div className="bg-stone-700/50 rounded-lg p-3 text-center">
                      <Shield className="w-4 h-4 text-blue-400 mx-auto mb-1" />
                      <div className="text-gray-400 text-xs">Defense</div>
                      <div className="text-white font-bold">
                        {battleStats.stats.defense || 0}
                      </div>
                    </div>
                    <div className="bg-stone-700/50 rounded-lg p-3 text-center">
                      <Zap className="w-4 h-4 text-yellow-400 mx-auto mb-1" />
                      <div className="text-gray-400 text-xs">Speed</div>
                      <div className="text-white font-bold">
                        {battleStats.stats.speed || 0}
                      </div>
                    </div>
                    <div className="bg-stone-700/50 rounded-lg p-3 text-center">
                      <Heart className="w-4 h-4 text-green-400 mx-auto mb-1" />
                      <div className="text-gray-400 text-xs">Cockrage</div>
                      <div className="text-white font-bold">
                        {battleStats.stats.cockrage || 0}
                      </div>
                    </div>
                    <div className="bg-stone-700/50 rounded-lg p-3 text-center">
                      <Zap className="w-4 h-4 text-purple-400 mx-auto mb-1" />
                      <div className="text-gray-400 text-xs">Ferocity</div>
                      <div className="text-white font-bold">
                        {battleStats.stats.ferocity || 0}
                      </div>
                    </div>
                    <div className="bg-stone-700/50 rounded-lg p-3 text-center">
                      <Zap className="w-4 h-4 text-orange-400 mx-auto mb-1" />
                      <div className="text-gray-400 text-xs">Evasion</div>
                      <div className="text-white font-bold">
                        {battleStats.stats.evasion || 0}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Daily Feathers */}
              {rental.dailyFeathers && rental.dailyFeathers > 0 && (
                <div className="bg-stone-800 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Feathers size={16} className="text-yellow-400" />
                      <span className="text-white font-semibold">
                        Daily Feathers
                      </span>
                    </div>
                    <span className="text-yellow-400 font-bold text-lg">
                      {rental.dailyFeathers}
                    </span>
                  </div>
                </div>
              )}

              {/* Chicken Attributes */}
              {rental.chickenMetadata?.attributes && (
                <div className="bg-stone-800 rounded-lg p-4">
                  <h4 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Zap className="w-4 h-4 text-purple-400" />
                    Attributes
                  </h4>
                  <div className="grid grid-cols-2 gap-3">
                    {bodyPartAttributes.map((traitType) => {
                      const value = getAttributeValue(traitType);
                      if (value === "N/A") return null;

                      return (
                        <div
                          key={traitType}
                          className="bg-stone-700/50 rounded-lg p-3 border border-stone-600/30 hover:border-stone-500/50 transition-colors"
                        >
                          <div className="text-gray-400 text-xs font-medium uppercase tracking-wide mb-1">
                            {traitType}
                          </div>
                          <div
                            className={`font-semibold text-sm ${
                              isLegendary(value)
                                ? "text-yellow-400 flex items-center gap-1"
                                : "text-white"
                            }`}
                          >
                            {value}
                            {isLegendary(value) && (
                              <span className="text-yellow-300 text-xs">
                                ⭐
                              </span>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Battle Record */}
              {battleStats && totalBattles > 0 && (
                <div className="bg-stone-800 rounded-lg p-4">
                  <h4 className="text-white font-semibold mb-3 flex items-center gap-2">
                    <Trophy className="w-4 h-4 text-yellow-400" />
                    Battle Record
                  </h4>
                  <div className="grid grid-cols-3 gap-3 text-center">
                    <div>
                      <div className="text-green-400 font-bold text-lg">
                        {battleStats.wins}
                      </div>
                      <div className="text-gray-400 text-xs">Wins</div>
                    </div>
                    <div>
                      <div className="text-red-400 font-bold text-lg">
                        {battleStats.losses}
                      </div>
                      <div className="text-gray-400 text-xs">Losses</div>
                    </div>
                    <div>
                      <div className="text-yellow-400 font-bold text-lg">
                        {winRate}%
                      </div>
                      <div className="text-gray-400 text-xs">Win Rate</div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </Modal.Body>
      </Modal.Content>
    </Modal>
  );
}
