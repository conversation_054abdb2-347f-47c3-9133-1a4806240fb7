"use client";

import { useStateContext } from "@/providers/app/state";
import useBlockchain from "./useBlockchain";
import { useBalance, useReadContract } from "wagmi";
import { formatUnits } from "viem";
import { cockAbi } from "@/providers/web3/abi/cock-abi";
import { itemAbi } from "@/providers/web3/abi/item-abi";

const FEATHERS_TOKEN_ID = 1;

const useTokens = () => {
  const { blockchainQuery } = useBlockchain();
  const { address } = useStateContext();

  const ron = useBalance({
    address,
  });

  const cockDecimal = useReadContract({
    address: blockchainQuery.data?.cock_address,
    abi: cockAbi,
    functionName: "decimals",
  });
  const cock = useReadContract({
    address: blockchainQuery.data?.cock_address,
    abi: cockAbi,
    functionName: "balanceOf",
    args: address ? [address] : undefined,
    query: {
      enabled: !!address && blockchainQuery.isSuccess,
    },
  });

  const feather = useReadContract({
    address: blockchainQuery.data?.items_address,
    abi: itemAbi,
    functionName: "balanceOf",
    args: address ? [address, BigInt(FEATHERS_TOKEN_ID)] : undefined,
    query: {
      enabled: !!address && blockchainQuery.isSuccess,
    },
  });

  return {
    ron: {
      ron,
      decimal: ron.data?.decimals || 18,
      formatted: Number(
        formatUnits(BigInt(`${ron.data?.value || 0}`), ron.data?.decimals || 18)
      ),
      value: ron.data?.value,
      iconImage: "/images/tokens/ron.png",
    },
    cock: {
      cock,
      decimal: cockDecimal.data,
      formatted: Number(
        formatUnits(BigInt(`${cock.data || 0}`), Number(cockDecimal.data))
      ),
      value: cock.data,
      iconImage: "/images/tokens/cock-token.webp",
    },
    feather: {
      feather,
      formatted: feather.isSuccess ? Number(feather.data) : 0,
      value: feather.data,
      iconImage: "/images/tokens/feathers-token.png",
    },
  };
};

export default useTokens;
