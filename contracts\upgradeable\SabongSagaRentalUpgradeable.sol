// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.28;

import "../interfaces/IERC721.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "hardhat/console.sol";

contract SabongSagaRentalUpgradeable is
    Initializable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    PausableUpgradeable
{
    // Roles
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");

    // State variables
    IERC721 public legacyChicken;
    IERC721 public genesisChicken;
    address public signer;
    address public feeWallet;
    uint256 public feePercentage; // Fee percentage (in basis points, e.g., 250 = 2.5%)
    address public revShareAddress;
    uint256 public revSharePercentage; // Rev share percentage (in basis points, e.g., 250 = 2.5%)
    mapping(uint256 => RentalInfo) public rentals;

    // Events
    event ChickenRented(
        uint256 indexed rentId,
        address indexed renter,
        uint256 expiresAt
    );
    event FeePercentageUpdated(uint256 oldPercentage, uint256 newPercentage);
    event EmergencyETHRelease(address indexed to, uint256 amount);

    event ChickenListedForRent(
        uint256 indexed rentId,
        uint256 indexed chickenId,
        address indexed owner
    );
    event ChickenUnlistedForRent(
        uint256 indexed rentId,
        uint256 indexed chickenId,
        address indexed owner
    );

    event InsuranceClaimed(
        uint256 indexed rentId,
        address indexed claimant,
        uint256 amount
    );

    // Errors
    error ErrInvalidSignature();
    error ErrRentIdAlreadyUsed();
    error ErrInvalidPayment();
    error ErrTransferFailed();
    error ErrInvalidFeePercentage();
    error ErrChickenNotOwned();
    error ErrChickenNotDead();
    error ErrChickenDied();
    error ErrInsuranceAlreadyClaimed();
    error ErrRentalAlreadyUnlisted();
    error ErrRentDurationNotExpired();
    error ErrInvalidInsurancePrice();
    error ErrInvalidInsuranceClaim();

    // Structs
    struct RentalInfo {
        uint256 rentId;
        uint256 chickenId;
        uint256 ethPrice;
        uint256 insurancePrice;
        bool insuranceClaimed;
        address owner;
        address renter;
        uint256 rentDuration;
        uint256 expiresAt;
        bool activeListing;
    }

    struct RentChickenParams {
        uint256 rentId;
        uint256 chickenId;
        uint256 ethPrice;
        uint256 insurancePrice;
        address renterAddress;
        address ownerAddress;
        bytes signature;
    }

    /**
     * @dev This empty reserved space is put in place to allow future versions to add new
     * variables without shifting down storage in the inheritance chain.
     */
    uint256[50] private __gap;

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    /**
     * @notice Initializes the contract
     * @param _feeWallet Address to receive rental fees
     * @param _feePercentage Fee percentage in basis points (e.g., 250 = 2.5%)
     * @param _admin Address to be granted admin role
     */
    function initialize(
        address _genesisChicken,
        address _legacyChicken,
        address _feeWallet,
        uint256 _feePercentage,
        address _revShareAddress,
        uint256 _revSharePercentage,
        address _admin
    ) public initializer {
        __AccessControl_init();
        __ReentrancyGuard_init();
        __Pausable_init();

        genesisChicken = IERC721(_genesisChicken);
        legacyChicken = IERC721(_legacyChicken);

        feeWallet = _feeWallet;
        feePercentage = _feePercentage;

        revShareAddress = _revShareAddress;
        revSharePercentage = _revSharePercentage;

        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(PAUSER_ROLE, _admin);
    }

    /**
     * @notice Rent a chicken
     * @param params Struct containing all rental parameters
     */
    function rentChicken(
        RentChickenParams calldata params
    ) external payable nonReentrant whenNotPaused {
        // Verify payment
        if (msg.value != params.ethPrice + params.insurancePrice)
            revert ErrInvalidPayment();

        // Verify signature
        bytes32 messageHash = prefixed(
            keccak256(
                abi.encodePacked(
                    params.rentId,
                    params.chickenId,
                    params.ethPrice,
                    params.insurancePrice,
                    params.renterAddress,
                    msg.sender,
                    params.ownerAddress
                )
            )
        );

        require(
            recoverSigner(messageHash, params.signature) == signer,
            ErrInvalidSignature()
        );

        // Process payment distribution
        _processPayment(params);
    }

    function _processPayment(RentChickenParams calldata params) private {
        // Calculate fee amounts
        uint256 feeAmount = (params.ethPrice * feePercentage) / 10000;
        uint256 revShareAmount = (params.ethPrice * revSharePercentage) / 10000;

        // Calculate insurance fee amounts
        uint256 insuranceFeeAmount = 0;
        uint256 insuranceRevShareAmount = 0;
        uint256 netInsuranceAmount = 0;

        if (params.insurancePrice > 0) {
            insuranceFeeAmount =
                (params.insurancePrice * feePercentage) /
                10000;
            insuranceRevShareAmount =
                (params.insurancePrice * revSharePercentage) /
                10000;
            netInsuranceAmount =
                params.insurancePrice -
                insuranceFeeAmount -
                insuranceRevShareAmount;
        }

        // Calculate total amounts to transfer
        uint256 totalFeeWalletAmount = feeAmount + insuranceFeeAmount;
        uint256 totalRevShareAmount = revShareAmount + insuranceRevShareAmount;
        uint256 ownerAmount = params.ethPrice - (feeAmount + revShareAmount);

        // Update rental information
        rentals[params.rentId].renter = msg.sender;
        rentals[params.rentId].insurancePrice = netInsuranceAmount;
        rentals[params.rentId].expiresAt =
            block.timestamp +
            rentals[params.rentId].rentDuration;

        // Transfer funds
        if (totalFeeWalletAmount > 0) {
            (bool feeSuccess, ) = payable(feeWallet).call{
                value: totalFeeWalletAmount
            }("");
            if (!feeSuccess) revert ErrTransferFailed();
        }

        if (totalRevShareAmount > 0) {
            (bool revShareSuccess, ) = payable(revShareAddress).call{
                value: totalRevShareAmount
            }("");
            if (!revShareSuccess) revert ErrTransferFailed();
        }

        if (ownerAmount > 0) {
            (bool ownerSuccess, ) = payable(params.ownerAddress).call{
                value: ownerAmount
            }("");
            if (!ownerSuccess) revert ErrTransferFailed();
        }

        emit ChickenRented(
            params.rentId,
            msg.sender,
            rentals[params.rentId].expiresAt
        );
    }

    /**
     * @notice Pause the contract
     */
    function pause() external onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @notice Unpause the contract
     */
    function unpause() external onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    /**
     * @notice Update the fee wallet address
     * @param _newFeeWallet New address to receive fees
     */
    function setFeeWallet(
        address _newFeeWallet
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        feeWallet = _newFeeWallet;
    }

    /**
     * @notice Update the signer address
     * @param _newSigner New address for signing transactions
     */
    function setSigner(
        address _newSigner
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        signer = _newSigner;
    }

    /**
     * @notice Emergency function to withdraw ETH
     * @param _to Address to send ETH to
     */
    function emergencyReleaseETH(
        address _to
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_to != address(0), "Invalid address");

        uint256 balance = address(this).balance;
        require(balance > 0, "No ETH to release");

        (bool success, ) = payable(_to).call{value: balance}("");
        require(success, "ETH transfer failed");

        emit EmergencyETHRelease(_to, balance);
    }

    /**
     * @notice Allows the contract to receive ETH
     */
    receive() external payable {}

    /**
     * @notice Update the fee percentage
     * @param _newFeePercentage New fee percentage in basis points (e.g., 250 = 2.5%)
     */
    function setFeePercentage(
        uint256 _newFeePercentage
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        if (_newFeePercentage > 10000) revert ErrInvalidFeePercentage();

        uint256 oldFeePercentage = feePercentage;
        feePercentage = _newFeePercentage;

        emit FeePercentageUpdated(oldFeePercentage, _newFeePercentage);
    }

    function splitSignature(
        bytes memory sig
    ) internal pure returns (uint8, bytes32, bytes32) {
        require(sig.length == 65, "Incorrect signature length");

        bytes32 r;
        bytes32 s;
        uint8 v;

        assembly {
            //first 32 bytes, after the length prefix
            r := mload(add(sig, 0x20))
            //next 32 bytes
            s := mload(add(sig, 0x40))
            //final byte, first of next 32 bytes
            v := byte(0, mload(add(sig, 0x60)))
        }

        return (v, r, s);
    }

    function recoverSigner(
        bytes32 message,
        bytes memory sig
    ) internal pure returns (address) {
        uint8 v;
        bytes32 r;
        bytes32 s;

        (v, r, s) = splitSignature(sig);

        return ecrecover(message, v, r, s);
    }

    function prefixed(bytes32 hash) internal pure returns (bytes32) {
        return
            keccak256(
                abi.encodePacked("\x19Ethereum Signed Message:\n32", hash)
            );
    }

    /**
     * @notice List a chicken for rent
     * @param _chickenId Unique identifier for the chicken
     * @param _rentId Unique identifier for the rental
     * @param _ethPrice Price in ETH
     * @param _insurancePrice Price for insurance
     * @param _rentDuration Duration of the rental in seconds
     * @param _signature Signature from authorized signer
     */
    function listChickenForRent(
        uint256 _chickenId,
        uint256 _rentId,
        uint256 _ethPrice,
        uint256 _insurancePrice,
        uint256 _rentDuration,
        bytes calldata _signature
    ) external {
        // Check if rent ID has been used
        if (rentals[_rentId].rentId != 0) revert ErrRentIdAlreadyUsed();

        // only legacy chicken can have insurance
        if (_chickenId <= 2222 && _insurancePrice > 0) {
            revert ErrInvalidInsurancePrice();
        }

        bytes32 messageHash = prefixed(
            keccak256(
                abi.encodePacked(
                    _chickenId,
                    _rentId,
                    _ethPrice,
                    _insurancePrice,
                    _rentDuration,
                    msg.sender
                )
            )
        );

        require(
            recoverSigner(messageHash, _signature) == signer,
            ErrInvalidSignature()
        );

        if (_chickenId <= 2222) {
            genesisChicken.transferFrom(msg.sender, address(this), _chickenId);
        } else {
            legacyChicken.transferFrom(msg.sender, address(this), _chickenId);
        }

        // Store rental information
        rentals[_rentId] = RentalInfo({
            rentId: _rentId,
            chickenId: _chickenId,
            ethPrice: _ethPrice,
            insurancePrice: _insurancePrice,
            insuranceClaimed: false,
            owner: msg.sender,
            renter: address(0),
            rentDuration: _rentDuration,
            expiresAt: 0,
            activeListing: true
        });

        emit ChickenListedForRent(_rentId, _chickenId, msg.sender);
    }

    /**
     * @notice Unlist a chicken for rent
     * @param _rentId Unique identifier for the rental
     * @param _chickenId Unique identifier for the chicken
     * @param _signature Signature from authorized signer
     */
    function unlistChickenForRent(
        uint256 _rentId,
        uint256 _chickenId,
        bytes calldata _signature
    ) external nonReentrant {
        RentalInfo storage rental = rentals[_rentId];
        if (!rental.activeListing) revert ErrRentalAlreadyUnlisted();

        bytes32 messageHash = prefixed(
            keccak256(abi.encodePacked(_rentId, msg.sender, address(this)))
        );

        require(
            recoverSigner(messageHash, _signature) == signer,
            ErrInvalidSignature()
        );

        if (rental.ethPrice > 0 && rental.expiresAt > block.timestamp) {
            revert ErrRentDurationNotExpired();
        }

        rental.activeListing = false;

        if (_chickenId <= 2222) {
            genesisChicken.transferFrom(address(this), msg.sender, _chickenId);
        } else {
            legacyChicken.transferFrom(address(this), msg.sender, _chickenId);
        }

        emit ChickenUnlistedForRent(_rentId, _chickenId, msg.sender);
    }

    /**
     * @notice Claim insurance
     * @param _rentId Unique identifier for the rental
     */
    function claimInsurance(uint256 _rentId) external nonReentrant {
        if (rentals[_rentId].insuranceClaimed)
            revert ErrInsuranceAlreadyClaimed();

        RentalInfo storage rental = rentals[_rentId];

        if (rental.insurancePrice == 0) {
            revert ErrInvalidInsurancePrice();
        }

        if (rental.expiresAt > block.timestamp) {
            revert ErrRentDurationNotExpired();
        }

        bool insuranceClaimProceed = false;

        if (rental.owner == msg.sender) {
            try legacyChicken.ownerOf(rental.chickenId) {
                revert ErrChickenNotDead();
            } catch (bytes memory) {}

            insuranceClaimProceed = true;
        }

        if (rental.renter == msg.sender) {
            try legacyChicken.ownerOf(rental.chickenId) {} catch (
                bytes memory
            ) {
                revert ErrChickenNotDead();
            }

            insuranceClaimProceed = true;
        }

        if (!insuranceClaimProceed) revert ErrInvalidInsuranceClaim();

        rental.insuranceClaimed = true;

        //send eth to msg.sender
        (bool success, ) = payable(msg.sender).call{
            value: rental.insurancePrice
        }("");
        if (!success) revert ErrTransferFailed();
        emit InsuranceClaimed(_rentId, msg.sender, rental.insurancePrice);
    }

    /**
     * @notice Update the legacy chicken contract address
     * @param _legacyChicken New address for the legacy chicken contract
     */
    function setLegacyChicken(
        address _legacyChicken
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        legacyChicken = IERC721(_legacyChicken);
    }

    /**
     * @notice Update the genesis chicken contract address
     * @param _genesisChicken New address for the genesis chicken contract
     */
    function setGenesisChicken(
        address _genesisChicken
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        genesisChicken = IERC721(_genesisChicken);
    }

    /**
     * @notice Update the treasury address
     * @param _treasury New address for the treasury
     */
    function setTreasuryAddress(
        address _treasury
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        feeWallet = _treasury;
    }

    /**
     * @notice Update the rev share address
     * @param _revShareAddress New address for the rev share
     */
    function setRevShareAddress(
        address _revShareAddress
    ) external onlyRole(DEFAULT_ADMIN_ROLE) {
        revShareAddress = _revShareAddress;
    }

    /**
     * @notice Get rental info for multiple rentals
     * @param _rentIds Array of rental IDs
     * @return Array of rental info
     */
    function getRentalInfoBulk(
        uint256[] calldata _rentIds
    ) external view returns (RentalInfo[] memory) {
        RentalInfo[] memory rentalInfo = new RentalInfo[](_rentIds.length);
        for (uint256 i = 0; i < _rentIds.length; i++) {
            rentalInfo[i] = rentals[_rentIds[i]];
        }
        return rentalInfo;
    }
}
