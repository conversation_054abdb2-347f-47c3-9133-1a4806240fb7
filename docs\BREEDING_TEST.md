# SabongSagaBreeding Hardhat Test Suite

This repository contains a Hardhat test suite for the **SabongSagaBreeding** smart contract, including mock token deployments and detailed breeding scenarios.

## Overview

The **SabongSagaBreeding** contract enables players to breed chickens (NFTs), leveraging ERC20 tokens as breeding fees, with cooldown and breed count limits. This test suite verifies core functionalities such as:

- Chicken ownership and minting
- Token transfer and approval flows
- Breeding mechanics with cryptographic signatures
- Breed count and cooldown enforcement
- Re<PERSON> claiming (Ninuno balance)
- Error handling and constraints

---

## 📁 Files Structure

```
/test
  └── sabong-saga-upgradeable.test.js  // Main test file for upgradeable breeding logic
/contracts
  ├── MockERC20.sol         // Mock $COCK token
  ├── ERC721Common.sol      // Common interface for NFTs
  ├── ERC1155Common.sol     // Common interface for items
  └── upgradeable/SabongSagaBreedingUpgradeable.sol  // Main upgradeable breeding contract
```

---

## ✅ Test Cases Summary

### 1. **Setup & Deployment**

- Deploy mock contracts:
  - ERC20 ($COCK token)
  - ERC721 (Genesis and Legacy Chickens)
  - ERC1155 (Feathers and Resources)
- Deploy `SabongSagaBreeding` with cooldown of 3600 seconds (1 hour)
- Set up owner, player, treasury, and signer addresses

#### Contract Deployment & Initialization

- Deploy using `upgrades.deployProxy` with transparent proxy pattern
- Initialize with correct token addresses and parameters:
  - COCK token
  - Genesis chickens
  - Legacy chickens
  - Feathers
  - Resources
  - Treasury address
  - Signer address

#### Referral System Tests

```javascript
it("Should update Referral Address", async function () {
  const referralAddress = "******************************************";
  await breeding.connect(owner).updateReferral(referralAddress);
  expect(await breeding.referral()).to.equal(referralAddress);
});
```

#### Dev Wallet Operations

```javascript
it("Should impersonate account and test breeding", async function () {
  let devAddress = "******************************************";
  await hre.network.provider.request({
    method: "hardhat_impersonateAccount",
    params: [devAddress],
  });
  // ... [rest of test implementation]
});
```

#### Signature Validation Updates

- Updated signature generation to match new contract requirements
- Enhanced validation for breeding parameters
- Added feathers and resources data hashing

### 2. **Asset Minting & Balance**

- Mint Genesis and Legacy chickens to player account
- Mint and transfer $COCK tokens to player
- Verify initial token balances
- Approve breeding contract for $COCK spending

### 3. **Breeding Mechanics**

- Signature generation with feathers and resources data
- Validate breeding event emission
- Track child token ID through event logs
- Handle breeding cooldown periods

### 4. **Breed Count & Time Tracking**

- Verify initial breed count is zero
- Track breeding counts correctly after operations
- Handle multiple tokens in batch count queries
- Verify breeding timestamp updates

### 5. **Genesis Identification**

- Correctly identify Genesis tokens (1-2222)
- Correctly identify Legacy tokens (2223+)
- Batch verification of token types

### 6. **Batch Breeding**

- Support breeding multiple pairs in single transaction
- Enforce maximum batch size (10 pairs)
- Validate array length matching across parameters
- Handle batch breeding parameters:
  - Parent token IDs
  - Fee amounts
  - Cooldown times
  - Feathers data
  - Resources data
  - Signatures

### 7. **Error Handling**

- Invalid array lengths in batch operations
- Mismatched parameter arrays
- Maximum batch size exceeded

## 📜 Example Helper Functions

### Signature Generation

```javascript
async function getSignature(
  userAddress,
  parent1,
  parent2,
  totalAmount,
  amountToVault,
  amountToNinuno,
  breedingCooldownTime,
  feathersData,
  resourcesData
) {
  // Hash the resources and feathers data separately
  const resourcesHash = ethers.keccak256(
    ethers.AbiCoder.defaultAbiCoder().encode(["uint256[][]"], [resourcesData])
  );
  const feathersHash = ethers.keccak256(
    ethers.AbiCoder.defaultAbiCoder().encode(["uint256[][]"], [feathersData])
  );

  const messageHash = ethers.solidityPackedKeccak256(
    [
      "address",
      "uint256",
      "uint256",
      "uint256",
      "uint256",
      "uint256",
      "uint256",
      "bytes32",
      "bytes32",
    ],
    [
      userAddress,
      parent1,
      parent2,
      totalAmount,
      amountToVault,
      amountToNinuno,
      breedingCooldownTime,
      feathersHash,
      resourcesHash,
    ]
  );

  return signer.signMessage(ethers.getBytes(messageHash));
}
```

### Batch Breeding Example

```javascript
const params = {
  chickenLeftTokenIds: [1n, 2n],
  chickenRightTokenIds: [2223n, 2224n],
  totalAmounts: [ethers.parseEther("10"), ethers.parseEther("10")],
  amountsToVault: [ethers.parseEther("3"), ethers.parseEther("3")],
  amountsToNinuno: [ethers.parseEther("3.5"), ethers.parseEther("3.5")],
  breedingCooldownTimes: [BigInt(cooldown), BigInt(cooldown)],
  feathersData: [[], []], // [[tokenId, amount], ...]
  resourcesData: [[], []], // [[index, tokenId, amount], ...]
  signatures: [],
};

// Generate signatures for each breeding pair
for (let i = 0; i < 2; i++) {
  const signature = await getSignature(
    player.address,
    params.chickenLeftTokenIds[i],
    params.chickenRightTokenIds[i],
    params.totalAmounts[i],
    params.amountsToVault[i],
    params.amountsToNinuno[i],
    params.breedingCooldownTimes[i],
    params.feathersData[i],
    params.resourcesData[i]
  );
  params.signatures.push(signature);
}
```

## ⚠️ Errors & Custom Reverts Handled

- `InvalidArrayLength`: Mismatched or invalid array lengths in batch operations
- `BreedTime`: Breeding attempted during cooldown period
- `InvalidParents`: Same chicken used for both parents
- `InvalidSignature`: Invalid breeding authorization signature

---

## ⚙️ Running the Tests

### Prerequisites:

- Node.js & npm
- Hardhat framework

### Install Dependencies:

```bash
npm install
```

### Run Test Suite:

```bash
npx hardhat test tests/sabong-saga.test.js --network hardhat
```

---

## 👥 Actors

| Role         | Description                               |
| ------------ | ----------------------------------------- |
| **Owner**    | Contract deployer, controls initial setup |
| **Player**   | End-user who breeds chickens              |
| **Signer**   | Address authorized to sign breeding txns  |
| **Treasury** | Receives part of breeding fees            |

---

## 📦 Dependencies

- **Hardhat** for Ethereum development and testing
- **Ethers.js** for interacting with contracts
- **Chai** for assertions in test cases
- **@nomicfoundation/hardhat-network-helpers** for time manipulation

---

## 💡 Notes

- The test suite uses time manipulation (`time.increase()`) to simulate cooldown periods
- Mock contracts simulate real ERC20/721/1155 standards
- Genesis tokens are IDs 1-2222, Legacy tokens start from 2223
- Breeding operations require valid signatures with hashed feathers and resources data

## Detailed Test Cases

### Contract Setup & Deployment

```javascript
beforeEach:
- Deploy Mock ERC20 (COCK token)
- Deploy Mock ERC721 (Genesis and Legacy)
- Deploy Mock ERC1155 (Feathers and Resources)
- Deploy Breeding Contract via upgrades.deployProxy with:
  - cooldown: 3600 (1 hour)
  - Parameters: [
    cock.address,
    genesis.address,
    legacy.address,
    feathers.address,
    resources.address,
    treasury.address,
    signer.address
  ]
```

### Basic Breeding Tests

```javascript
"Should allow player to breed chickens with valid signature":
- Mint COCK tokens to devWallet (1,000,000)
- Approve breeding contract for COCK tokens
- Set approvals for feathers and resources
- Breeding parameters:
  {
    chickenLeftTokenId: 100n,
    chickenRightTokenId: 10001n,
    totalAmount: ethers.parseEther("10"),
    amountToVault: ethers.parseEther("3"),
    amountToNinuno: ethers.parseEther("3.5"),
    breedingCooldownTime: cooldown,
    feathersData: [],
    resourcesData: []
  }
- Generate signature
- Verify Breed event emission

"Should initially track breed count and time":
- Check initial breed count (0)
- Verify initial breed time (0)
- Test with unused tokenId (10000)
```

### Batch Breeding Tests

```javascript
"Should revert batch breeding with invalid array lengths":
- Test parameters with mismatched lengths:
  {
    chickenLeftTokenIds: [1],
    chickenRightTokenIds: [2223, 2224], // Mismatched
    totalAmounts: [ethers.parseEther("10")],
    amountsToVault: [ethers.parseEther("3")],
    amountsToNinuno: [ethers.parseEther("3.5")],
    breedingCooldownTimes: [cooldown],
    feathersData: [[]],
    resourcesData: [[]],
    signatures: []
  }
- Expect InvalidArrayLength error
```

### Breeding Time Tracking

```javascript
"Should track breeding times correctly":
- Increase time by cooldown + 1
- Test breeding with parameters:
  {
    chickenLeftTokenId: 101n,
    chickenRightTokenId: 10008n,
    totalAmount: ethers.parseEther("10"),
    amountToVault: ethers.parseEther("3"),
    amountToNinuno: ethers.parseEther("3.5"),
    breedingCooldownTime: cooldown,
    feathersData: [],
    resourcesData: []
  }
- Verify breeding time updates
```

### Resource Management Tests

```javascript
"Should handle breeding with feathers":
- Test parameters:
  {
    chickenLeftTokenId: 1002n,
    chickenRightTokenId: 10004n,
    totalAmount: ethers.parseEther("2479.08"),
    amountToVault: ethers.parseEther("2231.172"),
    amountToNinuno: ethers.parseEther("247.908"),
    breedingCooldownTime: 172800n,
    feathersData: [[1n, 2n]], // [tokenId, amount]
    resourcesData: []
  }
- Verify feathers consumption
```

### Dev Wallet Operations

```javascript
"Should handle dev wallet breeding":
- Impersonate dev address
- Mint COCK tokens (10000)
- Mint NFTs (Genesis ID 20, Legacy ID 2240)
- Test breeding with:
  {
    chickenLeftTokenId: 100n,
    chickenRightTokenId: 10003n,
    totalAmount: ethers.parseEther("2479.08"),
    amountToVault: ethers.parseEther("2231.172"),
    amountToNinuno: ethers.parseEther("247.908"),
    breedingCooldownTime: 172800n,
    feathersData: [[1n, 60n]],
    resourcesData: []
  }
```

### Ninuno Balance Claiming

```javascript
describe("Ninuno Balance Claiming":
beforeEach:
- Set withdrawalRequestId = 1n
- Set claimAmount = ethers.parseEther("100")
- Mint COCK tokens to contract
- Generate claim signature

Test Cases:
1. "Should allow claiming with valid signature":
   - Track initial balance
   - Claim with valid signature
   - Verify NinunoBalanceClaimed event
   - Check balance update

2. "Should revert with invalid signature":
   - Generate invalid signature
   - Expect InvalidSignature error

3. "Should revert with insufficient balance":
   - Attempt to claim more than available
   - Expect InsufficientNinunoBalance error
```

### Error Cases

```javascript
Common Error Scenarios:
- InvalidParents: Same chicken for both parents
- NonceAlreadyUsed: Duplicate nonce
- ERC20InsufficientBalance: Insufficient COCK tokens
- InvalidSignature: Wrong or unauthorized signature
- InsufficientNinunoBalance: Not enough balance for claim
- WithdrawalRequestIdAlreadyUsed: Duplicate withdrawal
- InvalidArrayLength: Mismatched array lengths in batch
```

### Event Verification

```javascript
Events Tested:
1. Breed:
   - Parent token IDs
   - New token ID
   - Feathers/Resources data

2. BatchBreed:
   - Array of parent IDs
   - Array of new token IDs
   - Batch resource data

3. NinunoBalanceClaimed:
   - User address
   - Withdrawal request ID
   - Amount claimed
```

## Contract Interaction Examples

### Breeding Operations

#### Single Breeding Example

```javascript
// 1. Setup breeding parameters
const breedingParams = {
  chickenLeftTokenId: 101n,
  chickenRightTokenId: 10008n,
  totalAmount: ethers.parseEther("10"),
  amountToVault: ethers.parseEther("3"),
  amountToNinuno: ethers.parseEther("3.5"),
  breedingCooldownTime: BigInt(3600), // 1 hour cooldown
  feathersData: [],
  resourcesData: [],
};

// 2. Generate signature
const signature = await getSignature(
  playerAddress,
  breedingParams.chickenLeftTokenId,
  breedingParams.chickenRightTokenId,
  breedingParams.totalAmount,
  breedingParams.amountToVault,
  breedingParams.amountToNinuno,
  breedingParams.breedingCooldownTime,
  breedingParams.feathersData,
  breedingParams.resourcesData
);

// 3. Approve COCK token spending
await cockToken.approve(breedingContract.address, breedingParams.totalAmount);

// 4. Execute breeding
const tx = await breedingContract.breed(
  breedingParams,
  signature,
  "referralCode"
);

// 5. Wait for confirmation and get event
const receipt = await tx.wait();
const breedEvent = receipt.events.find((e) => e.event === "Breed");
const newTokenId = breedEvent.args.newTokenId;
```

#### Batch Breeding Example

```javascript
// 1. Setup batch breeding parameters
const batchParams = {
  chickenLeftTokenIds: [1n, 2n],
  chickenRightTokenIds: [2223n, 2224n],
  totalAmounts: [ethers.parseEther("10"), ethers.parseEther("10")],
  amountsToVault: [ethers.parseEther("3"), ethers.parseEther("3")],
  amountsToNinuno: [ethers.parseEther("3.5"), ethers.parseEther("3.5")],
  breedingCooldownTimes: [3600n, 3600n],
  feathersData: [[], []],
  resourcesData: [[], []],
  signatures: [],
};

// 2. Generate signatures for each pair
for (let i = 0; i < 2; i++) {
  const signature = await getSignature(
    playerAddress,
    batchParams.chickenLeftTokenIds[i],
    batchParams.chickenRightTokenIds[i],
    batchParams.totalAmounts[i],
    batchParams.amountsToVault[i],
    batchParams.amountsToNinuno[i],
    batchParams.breedingCooldownTimes[i],
    batchParams.feathersData[i],
    batchParams.resourcesData[i]
  );
  batchParams.signatures.push(signature);
}

// 3. Approve total COCK token spending
const totalAmount = batchParams.totalAmounts.reduce((a, b) => a + b);
await cockToken.approve(breedingContract.address, totalAmount);

// 4. Execute batch breeding
const tx = await breedingContract.breedBatch(batchParams, "referralCode");

// 5. Wait for confirmation and get events
const receipt = await tx.wait();
const breedEvents = receipt.events.filter((e) => e.event === "Breed");
const newTokenIds = breedEvents.map((e) => e.args.newTokenId);
```

### Ninuno Balance Claiming

#### Single Claim Example

```javascript
// 1. Setup claim parameters
const withdrawalRequestId = 1n;
const claimAmount = ethers.parseEther("100");

// 2. Generate signature
const messageHash = ethers.solidityPackedKeccak256(
  ["address", "uint256", "uint256"],
  [playerAddress, withdrawalRequestId, claimAmount]
);
const signature = await signer.signMessage(ethers.getBytes(messageHash));

// 3. Execute claim
const tx = await breedingContract.claimNinunoBalance(
  withdrawalRequestId,
  claimAmount,
  signature
);

// 4. Wait for confirmation and get event
const receipt = await tx.wait();
const claimEvent = receipt.events.find(
  (e) => e.event === "NinunoBalanceClaimed"
);
```

### Error Handling Examples

```javascript
// Example of handling breeding errors
try {
  await breedingContract.breed(breedingParams, signature, "referralCode");
} catch (error) {
  if (error.message.includes("InvalidParents")) {
    console.error("Cannot breed chicken with itself");
  } else if (error.message.includes("InsufficientBalance")) {
    console.error("Insufficient COCK token balance");
  } else {
    console.error("Breeding failed:", error);
  }
}

// Example of handling claim errors
try {
  await breedingContract.claimNinunoBalance(
    withdrawalRequestId,
    claimAmount,
    signature
  );
} catch (error) {
  if (error.message.includes("InsufficientNinunoBalance")) {
    console.error("Insufficient balance in contract");
  } else if (error.message.includes("WithdrawalRequestIdAlreadyUsed")) {
    console.error("Withdrawal request already processed");
  } else {
    console.error("Claim failed:", error);
  }
}
```
