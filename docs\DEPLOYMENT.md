# Sabong Saga Smart Contracts Deployment Guide

This guide provides instructions for deploying the Sabong Saga smart contracts using Hardhat.

## Prerequisites

- Node.js (v20.x or later)
- npm or yarn
- Access to a blockchain network (local or testnet/mainnet)
- Private key with sufficient funds for deployment

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd <repository-directory>
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

Using `.env` file:

```bash
cp .env.example .env
```

Edit `.env` and configure these required variables:

```bash
# Network RPC URLs
RONIN_RPC="https://api.roninchain.com/rpc"

# Deployer Private Keys
SAIGON_DEPLOYER_ACCOUNT="your_saigon_private_key"
RONIN_DEPLOYER_ACCOUNT="your_ronin_private_key"

# Signer Account
SAIGON_SIGNER_ACCOUNT="your_signer_private_key"
```

Hardhat environment variables:

```bash
# Set Saigon testnet deployer private key
npx hardhat vars set SAIGON_DEPLOYER_ACCOUNT
# Enter your private key when prompted

# Set Ronin mainnet deployer private key
npx hardhat vars set RONIN_DEPLOYER_ACCOUNT
# Enter your private key when prompted

# Set Saigon signer account private key
npx hardhat vars set SAIGON_SIGNER_ACCOUNT
# Enter your private key when prompted

# Set Ronin RPC URL
npx hardhat vars set RONIN_RPC
# Enter the RPC URL when prompted
```

To verify the variables are set correctly:

```bash
npx hardhat vars list
```

Note:

- Hardhat vars are encrypted and stored in `.env.vars.json`
- Add `.env.vars.json` to your `.gitignore` file
- Never commit private keys to version control
- Make sure your deployer accounts have sufficient funds for deployment

## Contract Deployment

### 1. Update Contract Addresses

Before deployment, ensure the `scripts/deploy/abi.json` file contains the correct contract addresses:

```json
{
  "sabong_saga_cock_address": "0x...",
  "sabong_saga_genesis_address": "0x...",
  "sabong_saga_legacy_address": "0x...",
  "sabong_saga_items_address": "0x...",
  "sabong_saga_resources_address": "0x..."
}
```

### 2. Deploy Contracts

#### Local Development Network

For testing on local network:

```bash
npx hardhat node
npx hardhat deploy --network localhost --tags SabongSagaBreedingProxy
```

#### Testnet (Saigon)

For deployment to Saigon testnet:

```bash
npx hardhat deploy --network saigon --tags SabongSagaBreedingProxy
```

#### Mainnet (Ronin)

For deployment to Ronin mainnet:

```bash
npx hardhat deploy --network ronin --tags SabongSagaBreedingProxy
```

### 3. Contract Verification

The contracts will be automatically verified on Sourcify when deploying to Saigon testnet or Ronin mainnet. The verification is handled by the `VerifyContracts` deployment script, which submits the contracts to:

```
https://sourcify.roninchain.com/server
```

No additional steps are required for verification as it's integrated into the deployment process.

## Deployment Process

The deployment script will execute the following steps in order:

1. Deploy ProxyAdmin contract
2. Deploy Logic contract (Implementation)
3. Deploy Transparent Proxy contract with initialization parameters
4. Verify contracts on Sourcify (for Saigon and Ronin networks)

The deployment creates three contracts:

- `SabongSagaBreedingProxyAdmin`
- `SabongSagaBreedingLogic` (Implementation)
- `SabongSagaBreedingProxy` (Proxy)

## Post-Deployment

After successful deployment, the following files will be updated:

1. Contract addresses will be saved in the `deployments/<network>` directory
2. Deployment artifacts will be stored in the `artifacts` directory

## Deployment Verification

To verify your deployment:

1. Check the proxy contract has been initialized correctly
2. Verify the implementation contract address
3. Confirm the ProxyAdmin ownership
4. Test basic contract functionality
5. Verify contracts are published on Sourcify explorer

## Upgrading the Contract

To upgrade the contract in the future:

```bash
npx hardhat run scripts/breeding/upgrade_breeding.ts --network <network_name>
```

## Contract Addresses

After deployment, you can find the contract addresses in:

- `deployments/<network>/SabongSagaBreedingProxy.json`
- `deployments/<network>/SabongSagaBreedingLogic.json`
- `deployments/<network>/SabongSagaBreedingProxyAdmin.json`

## Troubleshooting

Common issues and solutions:

1. **Nonce too low**: Clear the deployment cache:

```bash
npx hardhat clean
```

2. **Gas estimation failed**: Ensure your account has sufficient funds

3. **Sourcify verification failed**:
   - Check if the network RPC is accessible
   - Ensure contract source code matches deployed bytecode
   - Verify compiler settings in hardhat.config.ts match deployment

## Security Considerations

- Ensure the deployer account is secure and has sufficient funds
- Verify all contract addresses in `abi.json` are correct
- Keep the private keys secure and never commit them to version control
- Review the initialization parameters before deployment
- Test thoroughly on testnet before mainnet deployment
- Ensure the ProxyAdmin ownership is properly set
