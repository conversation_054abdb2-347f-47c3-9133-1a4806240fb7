import apiClient from "@/lib/api";
import {
  ICreateRentalRequest,
  ICreateRentalResponse,
  ICreateRentalFormData,
  IRentChickenRequest,
  IRentChickenResponse,
  IAvailableRentalsResponse,
  IMyRentalsResponse,
  IRentalHistoryResponse,
  IChickenDelegationInfo,
  ERewardDistributionType,
  IRental,
} from "../types/delegation.types";

/**
 * API response interface with snake_case fields (as returned by the backend)
 */
interface IRentalApiResponse {
  id: number;
  chicken_token_id: number;
  owner_address: string;
  renter_address: string | null;
  ronin_price: string;
  rental_period: number;
  rented_at: string | null;
  expires_at: string | null;
  status: number;
  signature: string | null;
  reward_distribution: number;
  delegated_task: number;
  shared_reward_amount: number | null;
  created_at: string;
  updated_at: string;
  rub_streak_benefactor?: number;
  legendary_feather_benefactor?: number;
}

/**
 * API response interface for available rentals with snake_case fields
 */
interface IAvailableRentalsApiResponse {
  status: number;
  data: {
    meta: {
      total: number;
      per_page: number;
      current_page: number;
      last_page: number;
      first_page: number;
      first_page_url: string;
      last_page_url: string;
      next_page_url: string | null;
      previous_page_url: string | null;
    };
    data: IRentalApiResponse[];
  };
}

/**
 * Transforms snake_case API response to camelCase interface format
 */
function transformRentalApiResponse(apiRental: IRentalApiResponse): IRental {
  return {
    id: apiRental.id,
    chickenTokenId: apiRental.chicken_token_id,
    ownerAddress: apiRental.owner_address,
    renterAddress: apiRental.renter_address,
    roninPrice: apiRental.ronin_price,
    rentalPeriod: apiRental.rental_period,
    rentedAt: apiRental.rented_at,
    expiresAt: apiRental.expires_at,
    status: apiRental.status,
    signature: apiRental.signature,
    rewardDistribution: apiRental.reward_distribution,
    delegatedTask: apiRental.delegated_task,
    sharedRewardAmount: apiRental.shared_reward_amount,
    createdAt: apiRental.created_at,
    updatedAt: apiRental.updated_at,
  };
}

/**
 * Transforms snake_case API meta response to camelCase interface format
 */
function transformMetaApiResponse(
  apiMeta: IAvailableRentalsApiResponse["data"]["meta"]
): IAvailableRentalsResponse["data"]["meta"] {
  return {
    total: apiMeta.total,
    perPage: apiMeta.per_page,
    currentPage: apiMeta.current_page,
    lastPage: apiMeta.last_page,
    firstPage: apiMeta.first_page,
    firstPageUrl: apiMeta.first_page_url,
    lastPageUrl: apiMeta.last_page_url,
    nextPageUrl: apiMeta.next_page_url,
    previousPageUrl: apiMeta.previous_page_url,
  };
}

/**
 * Helper function to handle API errors consistently
 */
function handleApiError(error: unknown, defaultMessage: string): never {
  if (error && typeof error === "object" && "response" in error) {
    const axiosError = error as { response?: { data?: { message?: string } } };
    if (axiosError.response?.data?.message) {
      throw new Error(axiosError.response.data.message);
    }
  }
  const errorMessage = error instanceof Error ? error.message : defaultMessage;
  throw new Error(errorMessage);
}

/**
 * Delegation and Rental API Service
 * Handles all API calls related to chicken delegation and rental functionality
 */
export class DelegationAPI {
  /**
   * Creates a new rental listing or direct delegation
   */
  static async createRental(
    formData: ICreateRentalFormData
  ): Promise<ICreateRentalResponse> {
    // Validate required fields
    if (!formData.chickenTokenId) {
      throw new Error("Chicken token ID is required");
    }

    // Convert form data to API request format
    let requestData: ICreateRentalRequest;

    // Handle direct delegation vs rental listing
    if (formData.isDirectDelegation) {
      if (!formData.renterAddress) {
        throw new Error("Renter address is required for direct delegation");
      }
      requestData = {
        chickenTokenId: formData.chickenTokenId,
        roninPrice: "0", // Free delegation
        rentalPeriod: formData.rentalPeriod,
        rewardDistribution: formData.rewardDistribution,
        delegatedTask: formData.delegatedTask,
        renterAddress: formData.renterAddress,
      };
    } else {
      if (!formData.roninPrice || parseFloat(formData.roninPrice) <= 0) {
        throw new Error(
          "Valid daily rental rate is required for rental listings"
        );
      }
      // Calculate total price: daily rate × duration in days
      const dailyRateInRon = parseFloat(formData.roninPrice);
      const durationInDays = formData.rentalPeriod / 86400;
      const totalPriceInRon = dailyRateInRon * durationInDays;
      // Convert total price from RON to wei (multiply by 1e18)
      const totalPriceInWei = (totalPriceInRon * 1e18).toString();
      requestData = {
        chickenTokenId: formData.chickenTokenId,
        roninPrice: totalPriceInWei,
        rentalPeriod: formData.rentalPeriod,
        rewardDistribution: formData.rewardDistribution,
        delegatedTask: formData.delegatedTask,
      };
    }

    // Include shared reward amount if using shared distribution
    if (formData.rewardDistribution === ERewardDistributionType.SHARED) {
      if (
        formData.sharedRewardAmount < 1 ||
        formData.sharedRewardAmount > 100
      ) {
        throw new Error(
          "Shared reward amount must be between 1 and 100 feathers"
        );
      }
      requestData.sharedRewardAmount = formData.sharedRewardAmount;
    }

    try {
      const response = await apiClient.post<ICreateRentalResponse>(
        "/rentals/create",
        requestData
      );
      return response.data;
    } catch (error: unknown) {
      // Handle API errors
      if (error && typeof error === "object" && "response" in error) {
        const axiosError = error as {
          response?: {
            data?: { message?: string; errors?: Array<{ message: string }> };
          };
        };
        if (axiosError.response?.data?.message) {
          throw new Error(axiosError.response.data.message);
        }
        if (axiosError.response?.data?.errors) {
          // Handle validation errors
          const validationErrors = axiosError.response.data.errors
            .map((err) => err.message)
            .join(", ");
          throw new Error(`Validation errors: ${validationErrors}`);
        }
      }
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create rental";
      throw new Error(errorMessage);
    }
  }

  /**
   * Lists available rental listings with pagination
   */
  static async listAvailableRentals(
    page: number = 1,
    pageSize: number = 10
  ): Promise<IAvailableRentalsResponse> {
    try {
      const response = await apiClient.get<IAvailableRentalsApiResponse>(
        "/rentals/available",
        {
          params: { page, pageSize },
        }
      );

      // Transform snake_case API response to camelCase interface format
      const transformedResponse: IAvailableRentalsResponse = {
        status: response.data.status,
        data: {
          meta: transformMetaApiResponse(response.data.data.meta),
          data: response.data.data.data.map(transformRentalApiResponse),
        },
      };

      return transformedResponse;
    } catch (error: unknown) {
      handleApiError(error, "Failed to fetch available rentals");
    }
  }

  /**
   * Rents a chicken (generates signature for blockchain transaction)
   */
  static async rentChicken(rentalId: number): Promise<IRentChickenResponse> {
    try {
      const requestData: IRentChickenRequest = { rentalId };
      const response = await apiClient.post<IRentChickenResponse>(
        "/rentals/rent",
        requestData
      );
      return response.data;
    } catch (error: unknown) {
      handleApiError(error, "Failed to rent chicken");
    }
  }

  /**
   * Gets user's rental history and active rentals
   */
  static async getMyRentals(): Promise<IMyRentalsResponse> {
    try {
      const response = await apiClient.get<{
        status: number;
        data: {
          ownedRentals: IRentalApiResponse[];
          rentedChickens: IRentalApiResponse[];
        };
      }>("/rentals/my-rentals");

      // Transform snake_case API response to camelCase interface format
      const transformedResponse: IMyRentalsResponse = {
        status: response.data.status,
        data: {
          ownedRentals: response.data.data.ownedRentals.map(
            transformRentalApiResponse
          ),
          rentedChickens: response.data.data.rentedChickens.map(
            transformRentalApiResponse
          ),
        },
      };

      return transformedResponse;
    } catch (error: unknown) {
      handleApiError(error, "Failed to fetch your rentals");
    }
  }

  /**
   * Gets user's rental history with pagination
   */
  static async getRentalHistory(
    page: number = 1,
    pageSize: number = 10
  ): Promise<IRentalHistoryResponse> {
    try {
      const response = await apiClient.get<{
        status: number;
        data: {
          meta: {
            total: number;
            per_page: number;
            current_page: number;
            last_page: number;
            first_page: number;
            first_page_url: string;
            last_page_url: string;
            next_page_url: string | null;
            previous_page_url: string | null;
          };
          data: IRentalApiResponse[];
        };
      }>("/rentals/history", {
        params: { page, pageSize },
      });

      // Transform snake_case API response to camelCase interface format
      const transformedResponse: IRentalHistoryResponse = {
        status: response.data.status,
        data: {
          meta: {
            total: response.data.data.meta.total,
            perPage: response.data.data.meta.per_page,
            currentPage: response.data.data.meta.current_page,
            lastPage: response.data.data.meta.last_page,
            firstPage: response.data.data.meta.first_page,
            firstPageUrl: response.data.data.meta.first_page_url,
            lastPageUrl: response.data.data.meta.last_page_url,
            nextPageUrl: response.data.data.meta.next_page_url,
            previousPageUrl: response.data.data.meta.previous_page_url,
          },
          data: response.data.data.data.map(transformRentalApiResponse),
        },
      };

      return transformedResponse;
    } catch (error: unknown) {
      handleApiError(error, "Failed to fetch rental history");
    }
  }

  /**
   * Cancels an active rental or listing
   */
  static async cancelRental(rentalId: number): Promise<{
    status: number;
    message: string;
    data: {
      rentalId: number;
      chickenTokenId: number;
      signature: string;
    };
  }> {
    try {
      const response = await apiClient.post<{
        status: number;
        message: string;
        data: {
          rentalId: number;
          chickenTokenId: number;
          signature: string;
        };
      }>("/rentals/cancel", {
        rentalId,
      });
      return response.data;
    } catch (error: unknown) {
      handleApiError(error, "Failed to cancel rental");
    }
  }

  /**
   * Gets active rental information for a specific chicken (public endpoint)
   */
  static async getChickenRental(
    chickenTokenId: number
  ): Promise<{ status: number; data: IRental | null }> {
    try {
      const response = await apiClient.get<{
        status: number;
        data: IRentalApiResponse | null;
      }>(`/rentals/chicken/${chickenTokenId}`);

      // Transform the response if data exists
      const transformedResponse = {
        status: response.data.status,
        data: response.data.data
          ? transformRentalApiResponse(response.data.data)
          : null,
      };

      return transformedResponse;
    } catch (error: unknown) {
      handleApiError(error, "Failed to fetch chicken rental info");
    }
  }

  /**
   * Gets active rental information for multiple chickens in bulk (public endpoint)
   */
  static async getChickenRentalsBulk(
    chickenTokenIds: number[]
  ): Promise<{ status: number; data: Record<number, IRental | null> }> {
    try {
      const response = await apiClient.post<{
        status: number;
        data: Record<number, IRentalApiResponse | null>;
      }>("/rentals/chickens-info-bulk", {
        chickenTokenIds,
      });

      // Transform the response data
      const transformedData: Record<number, IRental | null> = {};

      Object.entries(response.data.data).forEach(([tokenId, rentalData]) => {
        const numericTokenId = parseInt(tokenId, 10);
        transformedData[numericTokenId] = rentalData
          ? transformRentalApiResponse(rentalData)
          : null;
      });

      return {
        status: response.data.status,
        data: transformedData,
      };
    } catch (error: unknown) {
      handleApiError(error, "Failed to fetch chicken rentals bulk info");
    }
  }

  /**
   * Gets rental information for chickens owned by a specific wallet (public endpoint)
   */
  static async getChickensByWallet(
    walletAddress: string
  ): Promise<{ status: number; data: IChickenDelegationInfo[] }> {
    try {
      const response = await apiClient.get("/rentals/chickens-by-wallet", {
        params: { walletAddress },
      });
      return response.data;
    } catch (error: unknown) {
      handleApiError(error, "Failed to fetch chickens by wallet");
    }
  }
}

// Export individual functions for convenience
export const {
  createRental,
  listAvailableRentals,
  rentChicken,
  getMyRentals,
  getRentalHistory,
  cancelRental,
  getChickenRental,
  getChickenRentalsBulk,
  getChickensByWallet,
} = DelegationAPI;
