# 🐓 SabongSagaBreedingUpgradeable Smart Contract

An upgradeable version of the SabongSaga breeding system that enables secure NFT breeding between Genesis and Legacy chickens.

---

## ⚙️ Features

- ✅ **Upgradeable Architecture** using OpenZeppelin's proxy pattern
- ✅ **Secure NFT Breeding** between Genesis and Legacy chickens
- ✅ **Dynamic Breeding Cooldown** with per-operation configurable times
- ✅ **Cock Token (ERC20) payment system** with vault and ninuno allocation
- ✅ **Feathers burning (ERC1155)** required for breeding
- ✅ **Resources burning (ERC1155)** required for breeding
- ✅ **Signature-based authorization** for controlled breeding operations
- ✅ **Flexible fee distribution** between vault and ninuno
- ✅ **Event emissions** for tracking and indexing off-chain
- ✅ **Batch breeding support** for multiple pairs
- ✅ **Storage gap** for future upgrades
- ✅ **Referral system integration** for breeding operations
- ✅ **Ninuno balance claiming** with withdrawal request system

---

## 📜 Contract Details

| Feature                  | Description                                                         |
| ------------------------ | ------------------------------------------------------------------- |
| **Contract Type**        | Upgradeable (Transparent Proxy Pattern)                             |
| **Base Contracts**       | `Initializable`, `OwnableUpgradeable`, `ReentrancyGuardUpgradeable` |
| **ERC721 NFTs**          | Genesis & Legacy Chickens                                           |
| **ERC20 Token**          | $COCK Token (payment for breeding)                                  |
| **ERC1155 Feathers**     | Feathers used and burned during breeding                            |
| **ERC1155 Resources**    | Resources used and burned during breeding                           |
| **Breeding Cooldown**    | Dynamic time delay between breedings (set per operation)            |
| **Fee Distribution**     | Configurable split between vault and ninuno                         |
| **Signature Validation** | Ensures breeding requests are authorized                            |
| **Batch Operations**     | Support for breeding up to 10 pairs in one transaction              |
| **Referral System**      | Integration with IReferral contract                                 |

---

## 🚀 Initialization

The contract must be initialized after deployment with:

```solidity
function initialize(
    address _cockAddress,      // ERC20 token address
    address _genesisAddress,   // Genesis NFT address
    address _legacyAddress,    // Legacy NFT address
    address _feathersAddress,  // Feathers ERC1155 address
    address _resourcesAddress, // Resources ERC1155 address
    address _treasury,         // Treasury address
    address _signer           // Signature validator address
)
```

---

## 🔄 Core Functions

### Single Breeding

```solidity
function breed(
    BreedingParams calldata params,
    bytes calldata _sig,
    string calldata referralCode
)
```

### Batch Breeding

```solidity
function breedBatch(
    BatchBreedingParams calldata params,
    string calldata referralCode
)
```

### Ninuno Balance Claiming

```solidity
function claimNinunoBalance(
    uint256 withdrawalRequestId,
    uint256 amount,
    bytes calldata _sig
)
```

---

## 🔑 Important State Variables

| Variable                   | Type                          | Purpose                                  |
| -------------------------- | ----------------------------- | ---------------------------------------- |
| `chickenBreedTime`         | `mapping(uint256 => uint256)` | Tracks cooldown period per chicken       |
| `chickenBreedCount`        | `mapping(uint256 => uint256)` | Tracks number of times each chicken bred |
| `nonceTracker`             | `uint256`                     | Current nonce for signature validation   |
| `usedNonces`               | `mapping(uint256 => bool)`    | Prevents signature replay attacks        |
| `usedWithdrawalRequestIds` | `mapping(uint256 => bool)`    | Tracks used withdrawal request IDs       |
| `referral`                 | `IReferral`                   | Referral system contract interface       |
| `__gap`                    | `uint256[49]`                 | Storage gap for future upgrades          |

---

## 📡 Events

### Breed Event

```solidity
event Breed(
    uint256 indexed chickenLeftTokenId,
    uint256 indexed chickenRightTokenId,
    uint256 indexed newTokenId,
    uint256 amountToNinuno,
    uint256[][] feathersData,
    uint256[][] resourcesData,
    uint256 breedingCooldownTime
);
```

### NinunoBalanceClaimed Event

```solidity
event NinunoBalanceClaimed(
    address indexed user,
    uint256 indexed withdrawalRequestId,
    uint256 amount
);
```

---

## ⚠️ Errors

| Error                            | Description                                   |
| -------------------------------- | --------------------------------------------- |
| `BreedTime`                      | Chicken is still on cooldown                  |
| `InvalidArrayLength`             | Array lengths don't match in batch operations |
| `ERC1155InsufficientBalance`     | Not enough feathers or resources              |
| `ERC20InsufficientBalance`       | Not enough $COCK tokens                       |
| `InsufficientNinunoBalance`      | Ninuno balance insufficient                   |
| `NonceAlreadyUsed`               | Signature nonce already used                  |
| `InvalidParents`                 | Same chicken used as both parents             |
| `InvalidSignature`               | Signature verification failed                 |
| `UnauthorizedOwner`              | Caller doesn't own parent chickens            |
| `WithdrawalRequestIdAlreadyUsed` | Withdrawal request ID already used            |
| `ErrERC20TransferFailed`         | ERC20 transfer operation failed               |

---

## 🔐 Upgradeability Notes

1. Uses OpenZeppelin's upgradeable contracts pattern
2. Includes 49-slot storage gap for future upgrades
3. Constructor is disabled via `_disableInitializers()`
4. All initializations must be done via `initialize()`
5. Supports transparent proxy upgrades
6. First storage slot from gap used for IReferral

---

## 💡 Implementation Details

1. **Breeding Process**:

   - Validates signature and ownership
   - Checks cooldown periods
   - Burns required resources and feathers
   - Transfers $COCK tokens
   - Mints new Legacy chicken
   - Updates breeding counts and times
   - Handles referral code
   - Emits events

2. **Security Features**:

   - Reentrancy protection
   - Signature-based authorization
   - Nonce tracking
   - Ownership verification
   - Cooldown enforcement
   - Withdrawal request ID tracking

3. **Upgrade Safety**:
   - Storage gap for new variables
   - Initializable pattern
   - Proper inheritance chain
   - Careful storage slot management
