// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts-upgradeable/access/extensions/AccessControlEnumerableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ContextUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Burnable.sol";
import "../interfaces/ISabongSaga.sol";

contract LaunchpadPresetUpgradeable is
    Initializable,
    ContextUpgradeable,
    ReentrancyGuardUpgradeable,
    PausableUpgradeable,
    AccessControlEnumerableUpgradeable
{
    error ErrUnauthorizedAccount(address account, bytes32 neededRole);

    /// @dev Role that allows pausing and unpausing the contract.
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");

    /// @dev Address to which collected fees (ETH) are sent.
    address public feeWallet;

    /// @dev ERC20 token instance used in the contract.
    IERC20 public COCK;

    /// @dev ERC1155Burnable token instance used in the contract.
    ERC1155Burnable public FEATHERS;

    /// @dev Legacy interface instance that provides minting functionality.
    ISabongSaga public LEGACY;

    /**
     * @dev Reserved storage gap to allow for future state variable additions.
     */
    uint256[50] private __gap;

   

    /**
     * @notice Initializes the contract.
     * @dev This function sets up the contract state and should only be called once.
     * @param _COCK Address of the ERC20 token.
     * @param _FEATHERS Address of the ERC1155Burnable token.
     * @param _feeWallet Address of the fee or treasury wallet.
     * @param _legacy Address of the legacy minting contract.
     */
    function initialize(
        address _COCK,
        address _FEATHERS,
        address _feeWallet,
        address _legacy
    ) public virtual initializer {
        __LaunchpadPresetUpgradeable_init(
            _COCK,
            _FEATHERS,
            _feeWallet,
            _legacy
        );
    }

    /**
     * @notice Internal initialization function.
     * @dev Initializes inherited contracts and delegates to __LaunchpadPresetUpgradeable_init_unchained.
     * @param _COCK Address of the ERC20 token.
     * @param _FEATHERS Address of the ERC1155 token.
     * @param _feeWallet Address of the fee wallet.
     * @param _legacy Address of the legacy contract.
     */
    function __LaunchpadPresetUpgradeable_init(
        address _COCK,
        address _FEATHERS,
        address _feeWallet,
        address _legacy
    ) internal onlyInitializing {
        __Pausable_init_unchained();
        __ReentrancyGuard_init_unchained();
        __LaunchpadPresetUpgradeable_init_unchained(
            _COCK,
            _FEATHERS,
            _feeWallet,
            _legacy
        );
    }

    /**
     * @notice Further initializes state variables.
     * @dev This function sets important state variables and grants the DEFAULT_ADMIN_ROLE.
     * @param _COCK Address of the ERC20 token.
     * @param _FEATHERS Address of the ERC1155 token.
     * @param _devWallet Address of the fee treasury wallet.
     * @param _legacy Address of the legacy minting contract.
     */
    function __LaunchpadPresetUpgradeable_init_unchained(
        address _COCK,
        address _FEATHERS,
        address _devWallet,
        address _legacy
    ) internal onlyInitializing {
        feeWallet = _devWallet;
        FEATHERS = ERC1155Burnable(_FEATHERS);
        LEGACY = ISabongSaga(_legacy);
        COCK = IERC20(_COCK);
        _grantRole(DEFAULT_ADMIN_ROLE, _msgSender());
    }

    /**
     * @notice Pauses contract functionality.
     * @dev Only accounts with the PAUSER_ROLE can pause the contract.
     */
    function pause() public virtual {
        address sender = _msgSender();
        if (!hasRole(PAUSER_ROLE, sender))
            revert ErrUnauthorizedAccount(sender, PAUSER_ROLE);
        _pause();
    }

    /**
     * @notice Unpauses contract functionality.
     * @dev Only accounts with the PAUSER_ROLE can unpause the contract.
     */
    function unpause() public virtual {
        address sender = _msgSender();
        if (!hasRole(PAUSER_ROLE, sender))
            revert ErrUnauthorizedAccount(sender, PAUSER_ROLE);
        _unpause();
    }
}
