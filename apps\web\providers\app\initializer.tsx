"use client";

import { useEffect } from "react";
import { useStateContext } from "./state";
import useAuthStore from "@/store/auth";
import useClient from "@/lib/hooks/useClient";
import { useGlobalStatePersist } from "@/lib/store/persist";

interface IAppInitializerProps {
  children: React.ReactNode;
}

export default function AppInitializer({ children }: IAppInitializerProps) {
  const gStateP = useGlobalStatePersist();
  const { isConnected, publicClient, address } = useStateContext();
  const { onInit } = useAuthStore();
  const { isClient } = useClient();

  // Handle referral code
  const handleReferralCode = (): void => {
    // Only run in browser environment
    if (typeof window === "undefined") return;

    // check if breeding loading is still loading after refresh
    if (gStateP.breeding.loading.value) {
      gStateP.breeding.loading.set(false);
    }

    // Get referral code from URL
    const urlParams = new URLSearchParams(window.location.search);
    const referralCode = urlParams.get("r");

    // Save referral code if present
    if (referralCode) {
      localStorage.setItem("referralCode", referralCode);
    }
  };

  useEffect(() => {
    if (!isConnected) return;

    onInit(isConnected, publicClient, address);
    handleReferralCode();
  }, [isConnected]);

  return <>{isClient && children}</>;
}
