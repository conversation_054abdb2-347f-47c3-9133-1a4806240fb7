"use client";

import { useContractRead } from "@/lib/hooks/useContractRead";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { Address } from "viem";
import { MARKETPLACE_CONFIG } from "../types/delegation.types";

/**
 * Hook to fetch the marketplace fee percentage from the rental contract
 * Falls back to the configured fallback percentage if contract read fails
 */
export const useMarketplaceFee = () => {
  const { blockchainQuery } = useBlockchain();

  // Get rental contract address and ABI from blockchain config
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);
  const rentalAbi = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_abi
    : undefined;

  // Read fee percentage from contract
  const {
    data: contractFeePercentage,
    isLoading,
    error,
  } = useContractRead<bigint>({
    address: rentalAddress,
    abi: rentalAbi,
    functionName: "feePercentage",
    args: [],
    enabled: !!rentalAddress && !!rentalAbi && blockchainQuery.isSuccess,
    staleTime: 5 * 60 * 1000, // 5 minutes - fee percentage doesn't change often
  });

  // Convert bigint to number percentage
  const feePercentage = contractFeePercentage
    ? Number(contractFeePercentage) / 100 // Contract returns basis points (e.g., 250 for 2.5%)
    : MARKETPLACE_CONFIG.FALLBACK_FEE_PERCENTAGE;

  return {
    feePercentage,
    isLoading,
    error,
    isFromContract: !!contractFeePercentage,
  };
};
