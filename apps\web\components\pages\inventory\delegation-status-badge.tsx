"use client";

import { cn } from "@/utils/classes";
import { Users, UserCheck, Store } from "lucide-react";

export interface IDelegationStatusBadgeProps {
  isDelegated?: boolean; // True if this chicken is delegated TO the current user
  isDelegatedOut?: boolean; // True if this chicken is delegated OUT by the current user
  isRented?: boolean; // True if this chicken is rented TO the current user
  chickenType?:
    | "owned"
    | "delegated-to-me"
    | "rented-to-me"
    | "delegated-out"
    | "listed-in-market";
  className?: string;
}

export function DelegationStatusBadge({
  isDelegated = false,
  isDelegatedOut = false,
  isRented = false,
  chickenType,
  className,
}: IDelegationStatusBadgeProps) {
  // Determine the badge content and styling based on chickenType
  const getBadgeConfig = () => {
    if (chickenType === "delegated-to-me") {
      return {
        label: "Delegated to me",
        icon: <UserCheck className="w-3 h-3" />,
        bgColor: "bg-purple-600/80",
        borderColor: "border-purple-400/60",
        textColor: "text-purple-100",
        description: "This chicken is delegated to you",
      };
    }

    if (chickenType === "rented-to-me") {
      return {
        label: "Rented to me",
        icon: <UserCheck className="w-3 h-3" />,
        bgColor: "bg-orange-600/80",
        borderColor: "border-orange-400/60",
        textColor: "text-orange-100",
        description: "This chicken is rented to you",
      };
    }

    if (chickenType === "listed-in-market") {
      return {
        label: "Listed in Market",
        icon: <Store className="w-3 h-3" />,
        bgColor: "bg-green-600/80",
        borderColor: "border-green-400/60",
        textColor: "text-green-100",
        description: "This chicken is listed in the rental marketplace",
      };
    }

    if (chickenType === "delegated-out" || isDelegatedOut) {
      return {
        label: "Delegated Out",
        icon: <Users className="w-3 h-3" />,
        bgColor: "bg-blue-600/80",
        borderColor: "border-blue-400/60",
        textColor: "text-blue-100",
        description: "This chicken is delegated to someone else",
      };
    }

    // Fallback to legacy logic
    if (isDelegated) {
      return {
        label: "Delegated",
        icon: <UserCheck className="w-3 h-3" />,
        bgColor: "bg-purple-600/80",
        borderColor: "border-purple-400/60",
        textColor: "text-purple-100",
        description: "This chicken is delegated to you",
      };
    }

    return null;
  };

  const config = getBadgeConfig();
  if (!config) return null;

  return (
    <div
      className={cn(
        "inline-flex items-center gap-1 px-2 py-1 rounded-full border text-xs font-medium shadow-lg backdrop-blur-sm",
        config.bgColor,
        config.borderColor,
        config.textColor,
        className
      )}
      title={config.description}
    >
      {config.icon}
      <span>{config.label}</span>
    </div>
  );
}

// Utility function to determine delegation status for a chicken
export function getChickenDelegationStatus(
  chicken: any,
  currentUserAddress?: string
): {
  isDelegated: boolean;
  isDelegatedOut: boolean;
} {
  // If chicken has isDelegated property (from delegated chickens hook)
  if (chicken.isDelegated) {
    return {
      isDelegated: true,
      isDelegatedOut: false,
    };
  }

  // Check if chicken is delegated out (has rental status)
  if (chicken.rentalStatus) {
    const isRentedOrDelegated =
      chicken.rentalStatus.rentalStatus === "rented" ||
      chicken.rentalStatus.rentalStatus === "delegated";

    return {
      isDelegated: false,
      isDelegatedOut: isRentedOrDelegated,
    };
  }

  // Default: no delegation status
  return {
    isDelegated: false,
    isDelegatedOut: false,
  };
}
