import erc1155Abi from "@/abi/Erc1155.abi.json";
import FoodCraftingAbi from "@/abi/FoodCrafting.abi.json";
import { ConnectorErrorType } from "@sky-mavis/tanto-connect";
import { toast } from "sonner";
import {
  Address,
  ContractFunctionExecutionError,
  erc20Abi,
  formatEther,
  parseEventLogs,
  UserRejectedRequestError,
  WriteContractParameters,
} from "viem";
import { ronin, saigon } from "viem/chains";
import { create } from "zustand";
import useAuthStore from "./auth";
import { getRecipeForItem } from "@/utils/getRecipeForItem";

interface ItemsCraftedEvent {
  args: {
    tokenId: bigint[];
    amount: bigint[];
  };
}
interface ItemsCraftedByRecipeEvent {
  args: {
    sender: string;
    tokenId: bigint;
    amount: bigint;
  };
}

const CHAIN_ID = Number(process.env.NEXT_PUBLIC_CHAINDID || "2021");
const COCK_TOKEN_ADDRESS = process.env.NEXT_PUBLIC_COCK_CONTRACT as Address;
const FEATHERS_TOKEN_ADDRESS = process.env
  .NEXT_PUBLIC_FEATHERS_CONTRACT as Address;
const RESOURCES_ADDRESS = process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address;
const FOOD_CRAFTING_CONTRACT = process.env
  .NEXT_PUBLIC_FOODCRAFTING_CONTRACT as Address;
const chain = CHAIN_ID === 2020 ? ronin : saigon;

interface CraftingMaterial {
  tokenType: number; // 0 = ERC20, 1 = ERC1155
  tokenAddress: `0x${string}`;
  tokenId: bigint;
  amount: bigint;
}

interface CraftingRecipe {
  tokenId: number;
  materials: CraftingMaterial[];
  exists: boolean;
}
type FoodCraftingState = {
  isPending: boolean;
  approvingCock: boolean;
  approvingFeathers: boolean;
  randomCockPrice: bigint;
  randomFeatherPrice: bigint;
  fetchingPrice: boolean;
  foodBalances: Record<number, bigint>;
  craftableItems: CraftingRecipe[];
  fetchingRecipes: boolean;
};

type Actions = {
  craftFoods: (
    amt: number,
    referralCode?: string
  ) => Promise<{ tokenIds: bigint[]; amounts: bigint[] } | undefined>;
  approveCock: (amt: number) => Promise<void>;
  approveFeathers: () => Promise<void>;
  approveResources: () => Promise<void>;
  fetchRandomCookiePrice: () => Promise<void>;
  fetchFoodBalance: () => Promise<void>;
  fetchCraftingRecipes: () => Promise<void>;
  craftItems: (
    itemId: number,
    amount: number,
    referralCode: string
  ) => Promise<{ tokenIds: bigint[]; amounts: bigint[] } | undefined>;
};

type StoreState = FoodCraftingState & Actions;

const handleError = (
  error: unknown,
  defaultMessage: string,
  operation: string
) => {
  if (error instanceof UserRejectedRequestError) {
    toast.error("Transaction rejected", {
      description: "You rejected the transaction in your wallet",
      position: "top-right",
    });
    throw error;
  }
  if (error instanceof ContractFunctionExecutionError) {
    toast.error(error.name, {
      description: error.shortMessage,
      position: "top-right",
    });
    throw error;
  }

  // Handle specific error types
  if (error instanceof Error) {
    if (error.name === ConnectorErrorType.PROVIDER_NOT_FOUND) {
      window.open("https://wallet.roninchain.com", "_blank");
      toast.error("Wallet not found", {
        description: "Please install Ronin Wallet to continue",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("user rejected")) {
      toast.error("Transaction rejected", {
        description: "You rejected the transaction in your wallet",
        position: "top-right",
      });
      throw error;
    }

    if (error.message.includes("insufficient funds")) {
      toast.error("Insufficient funds", {
        description: "You don't have enough funds to complete this transaction",
        position: "top-right",
      });
      throw error;
    }
  } else {
    const err = error as Error;

    // Fallback for non-Error objects
    toast.error(err.name, {
      description: err.message,
      position: "top-right",
    });
    throw err;
  }
};

const useFoodCraftingStore = create<StoreState>()((set, get) => {
  return {
    isPending: false,
    approvingCock: false,
    approvingFeathers: false,
    randomFeatherPrice: 0n,
    randomCockPrice: 0n,
    fetchingPrice: true,
    foodBalances: {},
    craftableItems: [],
    fetchingRecipes: false,
    fetchFoodBalance: async () => {
      // Get state context values at call time, not store creation time
      const stateContext = window.stateContext;
      if (!stateContext || !stateContext.address) {
        return;
      }
      const { address, publicClient } = stateContext;

      try {
        // Create an array of token IDs to check (0-9)
        const tokenIds = Array.from({ length: 18 }, (_, i) => i);

        // Create an array of promises for each balance check
        const balancePromises = tokenIds.map((tokenId) =>
          publicClient.readContract({
            address: process.env.NEXT_PUBLIC_GAMEITEMS_CONTRACT as Address,
            abi: erc1155Abi,
            functionName: "balanceOf",
            args: [address, BigInt(tokenId)],
          })
        );

        // Execute all balance checks in parallel
        const balances = await Promise.all(balancePromises);

        // Create a map of token ID to balance
        const foodBalances = tokenIds.reduce(
          (acc, tokenId, index) => {
            acc[tokenId] = balances[index] as bigint;
            return acc;
          },
          {} as Record<number, bigint>
        );

        // Update your store with the balances
        set({ foodBalances });
      } catch (error) {
        console.error("Error fetching food balances:", error);
        handleError(error, "Failed to fetch food balances", "balance check");
      }
    },

    fetchCraftingRecipes: async () => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        console.error("State context not available");
        return;
      }

      set({ fetchingRecipes: true });

      const { publicClient } = stateContext;

      try {
        // Define the token IDs you want to check (0-9)
        const craftableTokenIds = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];

        // Single batch call returning parallel arrays
        const [tokenIds, materials, exists] = (await publicClient.readContract({
          address: FOOD_CRAFTING_CONTRACT,
          abi: FoodCraftingAbi,
          functionName: "getCraftingRecipeBatch",
          args: [craftableTokenIds.map((id) => BigInt(id))],
        })) as [bigint[], CraftingMaterial[][], boolean[]];

        // Combine parallel arrays into recipe objects
        const recipes: CraftingRecipe[] = tokenIds.map((tokenId, index) => ({
          tokenId: Number(tokenId),
          materials: materials[index] || [],
          exists: exists[index] || false,
        }));

        // Filter existing recipes
        const existingRecipes = recipes.filter((recipe) => recipe.exists);

        // Update the store with all recipes (both existing and non-existing)
        set({ craftableItems: existingRecipes });
      } catch (error) {
        handleError(
          error,
          "Failed to fetch crafting recipes",
          "recipe fetching"
        );
      } finally {
        set({ fetchingRecipes: false });
      }
    },

    fetchRandomCookiePrice: async () => {
      // Get state context values at call time, not store creation time
      const stateContext = window.stateContext;
      if (!stateContext) {
        set({ fetchingPrice: false });
        return;
      }
      const { publicClient } = stateContext;
      try {
        const feathersPrice = (await publicClient.readContract({
          address: FOOD_CRAFTING_CONTRACT,
          abi: FoodCraftingAbi,
          functionName: "FEATHERS_CRAFT_PRICE",
        })) as bigint;

        const cockPrice = (await publicClient.readContract({
          address: FOOD_CRAFTING_CONTRACT,
          abi: FoodCraftingAbi,
          functionName: "COCK_CRAFT_PRICE",
        })) as bigint;
        set({ randomCockPrice: cockPrice, randomFeatherPrice: feathersPrice });

        await get().fetchFoodBalance();
      } catch (error) {
      } finally {
        set({ fetchingPrice: false });
      }
    },
    craftItems: async (itemId, amount, referralCode = "0x") => {
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot craft item", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const {
        checkApprovals,
        fetchBalances,
        isFeathersApprovedForAll,
        isResourcesApprovedForAll,
        cockAllowance,
      } = useAuthStore.getState();

      const recipe = getRecipeForItem(itemId, get().craftableItems);
      console.log(get().craftableItems);

      console.log(recipe);

      if (!recipe) {
        toast.error("Cannot craft item", {
          description: `Item is not craftable`,
          position: "top-right",
        });
        return;
      }

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot craft item", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });

        // Check required approvals for each material in the recipe
        let needsResourcesApproval = false;
        let needsFeathersApproval = false;
        let totalCockRequired = 0n;

        for (const material of recipe.materials) {
          const requiredAmount = material.amount * BigInt(amount);

          if (material.tokenType === 0) {
            // ERC20 token
            if (
              material.tokenAddress.toLowerCase() ===
              COCK_TOKEN_ADDRESS.toLowerCase()
            ) {
              totalCockRequired += requiredAmount;
            }
          } else if (material.tokenType === 1) {
            // ERC1155 token
            if (
              material.tokenAddress.toLowerCase() ===
              RESOURCES_ADDRESS.toLowerCase()
            ) {
              needsResourcesApproval = true;
            } else if (
              material.tokenAddress.toLowerCase() ===
              FEATHERS_TOKEN_ADDRESS.toLowerCase()
            ) {
              needsFeathersApproval = true;
            }
          }
        }
        // Handle approvals
        if (needsResourcesApproval && !isResourcesApprovedForAll) {
          toast.info("Resources approval required", {
            description: "Approving resources for crafting...",
            position: "top-right",
          });
          await get().approveResources();
        }

        if (needsFeathersApproval && !isFeathersApprovedForAll) {
          toast.info("Feathers approval required", {
            description: "Approving feathers for crafting...",
            position: "top-right",
          });
          await get().approveFeathers();
        }

        if (totalCockRequired > 0n && cockAllowance < totalCockRequired) {
          toast.info("COCK approval required", {
            description: "Approving COCK tokens for crafting...",
            position: "top-right",
          });
          // Convert back to number for approveCock function (it expects amount, not total price)
          const cockPricePerItem = get().randomCockPrice;
          const cockAmountNeeded = Number(totalCockRequired / cockPricePerItem);
          await get().approveCock(cockAmountNeeded);
        }

        // Add simulation retry logic
        let simulationSuccess = false;
        let request: WriteContractParameters | undefined;
        let simulationError;
        const r =
          referralCode === undefined || !referralCode || referralCode === ""
            ? "0x"
            : referralCode;

        for (let attempt = 1; attempt <= 3; attempt++) {
          try {
            const simulateReq = await publicClient.simulateContract({
              address: FOOD_CRAFTING_CONTRACT,
              abi: FoodCraftingAbi,
              functionName: "craftItems",
              args: [BigInt(itemId), BigInt(amount), referralCode],
              chain,
              account: address,
            });

            request = simulateReq.request;
            simulationSuccess = true;
            break; // Simulation succeeded, exit retry loop
          } catch (error) {
            simulationError = error;
            request = undefined;

            if (attempt < 3) {
              // Wait before retrying (exponential backoff)
              await new Promise((resolve) =>
                setTimeout(resolve, 1000 * attempt)
              );
            }
          }
        }

        if (!simulationSuccess || !request) {
          throw simulationError; // All retries failed
        }

        // Estimate gas for the transaction
        const gasEstimate = await publicClient.estimateContractGas({
          address: FOOD_CRAFTING_CONTRACT,
          abi: FoodCraftingAbi,
          functionName: "craftItems",
          args: [BigInt(itemId), BigInt(amount), referralCode],
          account: address,
        });

        // Add 15% buffer to gas estimate
        const gasWithBuffer = (gasEstimate * 115n) / 100n;

        // Add gas to request
        request.gas = gasWithBuffer;

        // Execute the crafting transaction
        const hash = await walletClient.writeContract(request);
        toast.info("Crafting transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-center",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // Refresh balances after successful craft
          await Promise.all([
            checkApprovals(isConnected, publicClient, address),
            fetchBalances(isConnected, publicClient, address),
            get().fetchFoodBalance(),
          ]);

          // Parse event logs to get crafted items info
          const logs = parseEventLogs({
            abi: FoodCraftingAbi,
            eventName: "ItemsCraftedByRecipe",
            logs: receipt.logs,
          });

          if (logs.length) {
            const eventData = logs[0] as unknown as ItemsCraftedByRecipeEvent;
            if (eventData) {
              return {
                tokenIds: [eventData.args.tokenId],
                amounts: [eventData.args.amount],
              };
            } else {
              return {
                tokenIds: [],
                amounts: [],
              };
            }
          }
        } else {
          toast.error("Crafting transaction failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-center",
          });
          throw new Error("Crafting transaction failed");
        }
      } catch (error) {
        handleError(error, "Failed to craft item", "item crafting");
      } finally {
        set({ isPending: false });
      }
    },
    craftFoods: async (amt, referralCode = "0x") => {
      // Get state context values at call time, not store creation time
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot craft cookie", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const {
        checkApprovals,
        fetchBalances,
        isFeathersApprovedForAll,
        cockAllowance,
      } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot craft cookie", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ isPending: true });

        const cockPrice = get().randomCockPrice;
        const amtBigInt = BigInt(amt);
        const amtWithPrice = cockPrice * amtBigInt;

        if (!isFeathersApprovedForAll) {
          await get().approveFeathers();
        }
        if (cockAllowance < amtWithPrice) {
          await get().approveCock(amt);
        }

        const res = await fetch("/api/crafting-nonce", { method: "GET" });
        const craftData = await res.json();

        // Add simulation retry logic
        let simulationSuccess = false;
        let request: WriteContractParameters | undefined;
        let simulationError;
        const r =
          referralCode === undefined || !referralCode || referralCode === ""
            ? "0x"
            : referralCode;
        for (let attempt = 1; attempt <= 3; attempt++) {
          try {
            const simulateReq = await publicClient.simulateContract({
              address: FOOD_CRAFTING_CONTRACT,
              abi: FoodCraftingAbi,
              functionName: "craftFoods",
              args: [
                amt,
                craftData.deadline,
                craftData.seed,
                craftData.signature,
                r,
              ],
              chain,
              account: address,
            });

            request = simulateReq.request;
            simulationSuccess = true;
            break; // Simulation succeeded, exit retry loop
          } catch (error) {
            simulationError = error;
            request = undefined;

            if (attempt < 3) {
              // Wait before retrying (exponential backoff)
              await new Promise((resolve) =>
                setTimeout(resolve, 1000 * attempt)
              );
            }
          }
        }

        if (!simulationSuccess || !request) {
          throw simulationError; // All retries failed
        }

        // Estimate gas for the transaction
        const gasEstimate = await publicClient.estimateContractGas({
          address: FOOD_CRAFTING_CONTRACT,
          abi: FoodCraftingAbi,
          functionName: "craftFoods",
          args: [
            amt,
            craftData.deadline,
            craftData.seed,
            craftData.signature,
            r,
          ],
          account: address,
        });

        // Add 10% buffer to gas estimate
        const gasWithBuffer = (gasEstimate * 115n) / 100n;

        // Add gas to request
        request.gas = gasWithBuffer;

        // Continue with transaction if simulation succeeded
        const hash = await walletClient.writeContract(request);
        toast.info("Crafting transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-center",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          // Refresh balances after successful mint
          await Promise.all([
            checkApprovals(isConnected, publicClient, address),
            fetchBalances(isConnected, publicClient, address),
            get().fetchFoodBalance(),
          ]);

          const logs = parseEventLogs({
            abi: FoodCraftingAbi,
            eventName: "ItemsCrafted",
            logs: receipt.logs,
          });
          if (logs.length) {
            const eventData = logs[0] as unknown as ItemsCraftedEvent;
            if (eventData) {
              const tokenIds = eventData.args.tokenId;
              const amounts = eventData.args.amount;

              return {
                tokenIds,
                amounts,
              };
            }
          } else {
            return {
              tokenIds: [],
              amounts: [],
            };
          }
        } else {
          toast.error("Crafting transaction failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-center",
          });
          throw Error("Crafting transaction failed");
        }
      } catch (error) {
        console.log(error);

        handleError(error, "Failed to craft foods", "crafting");
      } finally {
        set({ isPending: false });
      }
    },

    approveResources: async () => {
      // Get state context values at call time, not store creation time
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve resources", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC1155", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ approvingFeathers: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all resources tokens",
          position: "top-right",
        });

        // Get the current chain
        const chain = CHAIN_ID === 2020 ? ronin : saigon;

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: RESOURCES_ADDRESS,
          abi: erc1155Abi,
          functionName: "setApprovalForAll",
          args: [FOOD_CRAFTING_CONTRACT, true],
          chain, // Explicitly specify the chain
          account: address, // Explicitly specify the account
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all resources tokens for spending",
            position: "top-right",
          });

          // Update the approval status after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(
          error,
          "Failed to approve ERC1155 token",
          "ERC1155 approval"
        );
      } finally {
        set({ approvingFeathers: false });
      }
    },
    approveFeathers: async () => {
      // Get state context values at call time, not store creation time
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC1155", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        if (!address || !walletClient) {
          toast.error("Cannot approve ERC1155", {
            description: "Wallet not connected",
            position: "top-right",
          });
          return;
        }

        set({ approvingFeathers: true });
        toast.info("Preparing approval transaction...", {
          description: "Setting approval for all Feathers tokens",
          position: "top-right",
        });

        // Get the current chain
        const chain = CHAIN_ID === 2020 ? ronin : saigon;

        // Set approval for all for ERC1155 token
        const hash = await walletClient.writeContract({
          address: FEATHERS_TOKEN_ADDRESS,
          abi: erc1155Abi,
          functionName: "setApprovalForAll",
          args: [FOOD_CRAFTING_CONTRACT, true],
          chain, // Explicitly specify the chain
          account: address, // Explicitly specify the account
        });

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC1155 approval successful", {
            description:
              "Successfully approved all Feathers tokens for spending",
            position: "top-right",
          });

          // Update the approval status after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC1155 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(
          error,
          "Failed to approve ERC1155 token",
          "ERC1155 approval"
        );
      } finally {
        set({ approvingFeathers: false });
      }
    },

    approveCock: async (amt) => {
      // Get state context values at call time, not store creation time
      const stateContext = window.stateContext;
      if (!stateContext) {
        toast.error("Cannot approve ERC20", {
          description: "State context not available",
          position: "top-right",
        });
        return;
      }

      const {
        address,
        isConnected,
        publicClient,
        walletClient,
        ConnectRecentWallet,
      } = stateContext;

      const { checkApprovals } = useAuthStore.getState();

      try {
        await ConnectRecentWallet();

        set({ approvingCock: true });

        const cockPrice = get().randomCockPrice;
        const amtBigInt = BigInt(amt);
        const amtWithPrice = cockPrice * amtBigInt;

        toast.info("Preparing approval transaction...", {
          description: `Approving ${formatEther(amtWithPrice)} COCK tokens for spending`,
          position: "top-right",
        });

        const { request } = await publicClient.simulateContract({
          address: COCK_TOKEN_ADDRESS,
          abi: erc20Abi,
          functionName: "approve",
          args: [FOOD_CRAFTING_CONTRACT, amtWithPrice],
          chain, // Explicitly specify the chain
          account: address, // Explicitly specify the account
        });
        // Approve amount for ERC20 token
        const hash = await walletClient!.writeContract(request);

        toast.info("Approval transaction sent", {
          description: `Transaction hash: ${hash}`,
          position: "top-right",
        });

        // Wait for transaction to be mined
        const receipt = await publicClient.waitForTransactionReceipt({ hash });

        if (receipt.status === "success") {
          toast.success("ERC20 approval successful", {
            description: `Approved ${formatEther(amtWithPrice)} COCK tokens for spending`,
            position: "top-right",
          });

          // Update the allowance after successful approval
          await checkApprovals(isConnected, publicClient, address);
        } else {
          toast.error("ERC20 approval failed", {
            description: "The transaction was processed but failed on-chain",
            position: "top-right",
          });
        }
      } catch (error) {
        handleError(error, "Failed to approve ERC20 token", "ERC20 approval");
      } finally {
        set({ approvingCock: false });
      }
    },
  };
});

export default useFoodCraftingStore;
