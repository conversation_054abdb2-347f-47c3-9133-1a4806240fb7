import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import SABONG_CONFIG from 'Config/sabong'
import Application from '@ioc:Adonis/Core/Application'
import Chicken, { EChickenIsHatchedStatus } from 'App/Models/Chicken'

export default class ApisController {
  public async blockchain({ response }: HttpContextContract) {
    return response.json({
      chicken_genesis_address: SABONG_CONFIG.CONTRACTS.CHICKEN_GENESIS_ADDRESS,
      chicken_genesis_abi: SABONG_CONFIG.ABIS.CHICKEN_GENESIS_ABI,
      chicken_legacy_abi: SABONG_CONFIG.ABIS.CHICKEN_LEGACY_ABI,
      chicken_legacy_address: SABONG_CONFIG.CONTRACTS.CHICKEN_LEGACY_ADDRESS,
      cock_address: SABONG_CONFIG.CONTRACTS.COCK_ADDRESS,
      cock_abi: SABONG_CONFIG.ABIS.COCK_ABI,
      items_address: SABONG_CONFIG.CONTRACTS.ITEMS_ADDRESS,
      items_abi: SABONG_CONFIG.ABIS.ITEMS_ABI,
      breeding_address: SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS,
      breeding_abi: SABONG_CONFIG.ABIS.BREEDING_ABI,
      breeding_cooldown_max_count_duration: SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION,
      chicken_genesis_threshold: SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD,
      chicken_legacy_threshold: SABONG_CONFIG.CHICKEN_LEGACY_THRESHOLD,
      resources_address: SABONG_CONFIG.CONTRACTS.RESOURCES_ADDRESS,
      resources_abi: SABONG_CONFIG.ABIS.RESOURCES_ABI,
      referral_address: SABONG_CONFIG.CONTRACTS.REFERRAL_ADDRESS,
      referral_abi: SABONG_CONFIG.ABIS.REFERRAL_ABI,
      rental_address: SABONG_CONFIG.CONTRACTS.RENTAL_ADDRESS,
      rental_abi: SABONG_CONFIG.ABIS.RENTAL_ABI,
    })
  }

  public async image({ request, response }: HttpContextContract) {
    const { filename } = request.params()

    const findChicken = await Chicken.query().where('tokenId', filename.split('.')[0]).first()

    if (findChicken && findChicken.isHatched === EChickenIsHatchedStatus.NO) {
      return response.download(`${Application.tmpPath('build')}/images/egg.png`)
    }

    return response.download(`${Application.tmpPath('build')}/images/${filename}`)
  }

  public async metadata({ request, response }: HttpContextContract) {
    const { filename } = request.params()

    const findChicken = await Chicken.query().where('tokenId', filename).first()

    if (findChicken && findChicken.isHatched === EChickenIsHatchedStatus.NO) {
      return response.json({
        name: 'Egg',
        description: '',
        image: `${SABONG_CONFIG.API_URL}/api/image/egg.png`,
        attributes: [
          {
            trait_type: 'Type',
            value: 'Egg',
            display_type: 'string',
          },
        ],
      })
    }

    return response.download(`${Application.tmpPath('build')}/json/${filename}.json`)
  }

  public async me({ auth, response }: HttpContextContract) {
    if (!auth.user)
      return response.status(401).json({
        status: 0,
        message: 'Unauthorized',
      })

    return response.json({
      status: 1,
      data: {
        ...auth.user.toJSON(),
        is_admin: SABONG_CONFIG.ADMIN_ADDRESSES.includes(auth.user.blockchainAddress.toLowerCase()),
      },
    })
  }
}
