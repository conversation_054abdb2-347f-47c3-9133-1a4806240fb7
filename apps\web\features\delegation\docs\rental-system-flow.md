# Rental System Flow Documentation

## Overview

The Sabong Saga rental system allows chicken owners to list their chickens for rent or delegate them directly to other players. The system includes comprehensive insurance mechanisms and dual verification for chicken death status to ensure fair and secure transactions.

## System Architecture

### Core Components

- **Backend API**: Handles rental creation, validation, and signature generation
- **Smart Contracts**: Manages on-chain rental execution and insurance claims
- **Database**: Stores rental records and status tracking
- **Dual Verification**: API + Blockchain verification for chicken death status

### Smart Contract Functions

1. `listChickenForRent()` - Owner lists chicken for rent
2. `rentChicken()` - Renter rents a listed chicken
3. `unlistChickenForRent()` - Owner removes listing (before rental)
4. `claimInsurance()` - Claim insurance based on chicken status

## Detailed Flow Documentation

### 1. Listing a Chicken in the Rental Marketplace

**Prerequisites:**

- Owner must own the chicken (verified via `ownerOf` contract call)
- Chicken must not be fainted, dead, or breeding
- Chicken must not already be listed or rented

**Flow Steps:**

1. **API Request**: `POST /rentals`

   ```json
   {
     "chickenTokenId": 12345,
     "roninPrice": "1000000000000000000", // 1 ETH in wei
     "rentalPeriod": 86400, // 1 day in seconds
     "insurancePrice": "500000000000000000", // 0.5 ETH in wei
     "rewardDistribution": 1, // DELEGATOR_ONLY
     "delegatedTask": 3, // BOTH (daily rub + gameplay)
     "sharedRewardAmount": null
   }
   ```

2. **Backend Validation**:

   - Verify chicken ownership via contract `ownerOf` call
   - Check chicken is not fainted/dead via battle stats API
   - Validate rental parameters

3. **Database Record Creation**:

   - Create rental entry with `PENDING` status
   - Store all rental terms and conditions

4. **Signature Generation**:

   - Generate hash: `keccak256(chickenId, rentId, ethPrice, insurancePrice, rentDuration, ownerAddress)`
   - Sign with backend private key

5. **Frontend Contract Call**:

   ```typescript
   await rentalContract.write.listChickenForRent([
     chickenId,
     rentId,
     ethPrice,
     insurancePrice,
     rentDuration,
     signature,
   ]);
   ```

6. **Status Update**: Rental status becomes `AVAILABLE` in marketplace

### 2. Direct Delegation (Private Rental)

**Flow Steps:**

1. **API Request**: `POST /rentals` with `renterAddress` and `roninPrice = "0"`
2. **Direct Assignment**: Creates rental with specific renter, no marketplace listing
3. **Immediate Activation**: Rental becomes active without blockchain listing
4. **Delegation Terms**: Reward distribution and task permissions are applied

**Key Difference**: No marketplace involvement, direct owner-to-delegatee relationship.

### 3. Canceling/Unlisting a Rental (Owner Only)

**Current Implementation Limitations:**

- Only the **owner** can cancel rentals
- Cannot cancel **active rentals** that are already rented (status = `RENTED`) with price > 0
- Can only cancel **listings** (status = `AVAILABLE`) or **free delegations**

**Prerequisites:**

- Caller must be the rental owner
- Rental must NOT be in `RENTED` status with roninPrice > 0
- Chicken must still be owned by the original owner

**Flow Steps:**

1. **API Request**: `POST /rentals/cancel`

   ```json
   {
     "rentalId": 123
   }
   ```

2. **Backend Validation** (`RentalsController.cancelRental`):

   - Verify caller is authenticated
   - Find rental by ID
   - Check chicken ownership via contract `ownerOf` call
   - Verify caller is the rental owner
   - **Restriction**: Cannot cancel if `status === RENTED && roninPrice !== 0`

3. **Signature Generation**:

   - Generate hash: `keccak256(rentalId, chickenTokenId)`
   - Sign with backend private key

4. **Response**:

   ```json
   {
     "status": 1,
     "message": "Rental cancelling pending transaction onchain",
     "data": {
       "rentalId": 123,
       "chickenTokenId": 456,
       "signature": "0x..."
     }
   }
   ```

5. **Frontend Contract Call**:

   ```typescript
   await rentalContract.write.unlistChickenForRent([
     rentalId,
     chickenTokenId,
     signature,
   ]);
   ```

6. **Database Update**: Status becomes `CANCELLED`
7. **Marketplace Removal**: Chicken no longer appears in available listings

### 4. Renting a Chicken from Marketplace

**Flow Steps:**

1. **Browse Marketplace**: `GET /rentals/available` - View available chickens
2. **Select Chicken**: Renter chooses desired chicken
3. **Rent Request**: `POST /rentals/:id/rent`
4. **Backend Validation**:

   - Verify renter is not the owner
   - Confirm rental is still available
   - Check chicken is still alive and available

5. **Signature Generation**:

   - Generate hash: `keccak256(rentId, chickenId, ethPrice, insurancePrice, renterAddress, renterWallet, ownerAddress)`
   - Sign with backend private key

6. **Payment & Contract Call**:

   ```typescript
   await rentalContract.write.rentChicken({
     value: ethPrice + insurancePrice, // Total payment
     args: [
       {
         rentId,
         chickenId,
         ethPrice,
         insurancePrice,
         renterAddress,
         ownerAddress,
         signature,
       },
     ],
   });
   ```

7. **Blockchain Execution**:

   - Contract validates signature
   - Transfers rental fee to owner
   - Holds insurance in escrow
   - Sets rental as active

8. **Database Update**:
   - Status becomes `RENTED`
   - Set `rentedAt` and `expiresAt` timestamps

### 4. Enhanced Chicken Death Verification System

**Dual Verification Approach:**

#### Primary Check: Battle Stats API

```typescript
const battleStats = await fetch(`/api/proxy/game?tokenId=${tokenId}`);
const isDeadByAPI = battleStats.state === "dead";
```

#### Secondary Check: Smart Contract ownerOf

```typescript
let isDeadByContract = false;

// Only check Legacy chickens since Genesis chickens cannot die
if (tokenId > CHICKEN_GENESIS_THRESHOLD) {
  try {
    await chickenLegacyContract.read.ownerOf([BigInt(tokenId)]);
    isDeadByContract = false; // ownerOf succeeded = chicken alive
  } catch (error) {
    isDeadByContract = true; // ownerOf failed = chicken burned/dead
  }
} else {
  // Genesis chickens cannot die, so always alive from contract perspective
  isDeadByContract = false;
}
```

#### Verification Logic

```typescript
const verifyChickenDeath = (
  tokenId: number,
  apiStatus: boolean,
  contractStatus: boolean
) => {
  // Genesis chickens cannot die, so if API says dead, it's likely an error
  if (tokenId <= CHICKEN_GENESIS_THRESHOLD && apiStatus) {
    return {
      isDead: false,
      isVerified: false,
      confidence: "low",
      error: "Genesis chickens cannot die - API error suspected",
      requiresReview: true,
    };
  }

  // For Legacy chickens, use dual verification
  if (tokenId > CHICKEN_GENESIS_THRESHOLD) {
    if (apiStatus && contractStatus) {
      return { isDead: true, isVerified: true, confidence: "high" };
    }
    if (!apiStatus && !contractStatus) {
      return { isDead: false, isVerified: true, confidence: "high" };
    }
    // Mismatch - requires manual review
    return {
      isDead: contractStatus, // Prefer contract result
      isVerified: false,
      confidence: "low",
      requiresReview: true,
    };
  }

  // Genesis chickens - always alive
  return { isDead: false, isVerified: true, confidence: "high" };
};
```

### 5. Insurance System After Duration Ends

**Insurance Logic:**

- **Insurance paid upfront** by renter when renting
- **Held in escrow** by smart contract during rental period
- **Distribution based on chicken status** at rental end

**Scenarios:**

#### Scenario A: Chicken Alive

- **Insurance recipient**: Renter (gets insurance back)
- **Rental fee**: Owner keeps the rental payment
- **Chicken status**: Returns to owner, available for use

#### Scenario B: Chicken Dead

- **Insurance recipient**: Owner (compensation for loss)
- **Rental fee**: Owner still keeps rental payment
- **Chicken status**: Remains dead, owned by original owner

### 6. Insurance Claiming Process

**Enhanced Claiming Flow:**

1. **Rental Expiration**: `expiresAt` timestamp passes
2. **Dual Status Check**:

   ```typescript
   const verification = await verifyChickenDeathForInsurance(tokenId);
   ```

3. **Claim Eligibility**:

   - **High confidence alive**: Renter can claim insurance
   - **High confidence dead**: Owner can claim insurance
   - **Low confidence/mismatch**: Requires admin review

4. **Claim Execution**:

   ```typescript
   // Only proceed if verification confidence is high
   if (verification.isVerified) {
     await rentalContract.write.claimInsurance([rentId]);
   }
   ```

5. **Smart Contract Distribution**:
   - Contract checks internal death status
   - Distributes insurance to appropriate party
   - Updates `insuranceClaimed` flag

### 7. Chicken Death During Rental Period

**Scenarios:**

#### Death During Active Rental

- **Chicken state**: `battleStats.state` becomes `"dead"`
- **Contract state**: `ownerOf` calls start failing
- **Rental continues**: Period runs until `expiresAt`
- **Renter responsibility**: Renter was responsible for chicken care
- **Insurance impact**: Will go to owner when claimed

#### Rental Expiration with Dead Chicken

- **Ownership**: Dead chicken remains with original owner
- **Insurance claim**: Owner eligible for insurance compensation
- **No transfer needed**: Dead chicken doesn't "return" (already owned)

### 8. Error Handling and Edge Cases

#### Network Issues

- **Contract call fails**: Fall back to API verification with warning (Legacy chickens only)
- **API unavailable**: Use contract verification only (Legacy chickens only)
- **Both fail**: Require manual admin intervention

#### Verification Mismatches

- **Legacy chickens**:
  - **API says dead, contract says alive**: Prefer contract, flag for review
  - **API says alive, contract says dead**: Prefer contract, investigate API lag
  - **Inconsistent results**: Pause insurance claims, require admin review
- **Genesis chickens**:
  - **API says dead**: Flag as error, Genesis chickens cannot die
  - **Always treat as alive**: No contract verification needed

#### Gas Optimization

- **ownerOf calls**: View functions, no gas cost
- **Selective checking**: Only check Legacy contract (tokenId > threshold)
- **Batch verification**: Check multiple Legacy chickens in single call
- **Caching**: Cache verification results for short periods

## Technical Implementation Notes

### Database Schema

```sql
-- Current rental table structure (existing implementation)
-- Basic cancellation support via status field only
-- Status: 0=AVAILABLE, 1=RENTED, 2=EXPIRED, 3=CANCELLED, 4=PENDING

-- Future enhancements could include:
-- ALTER TABLE rentals ADD COLUMN insurance_price BIGINT DEFAULT 0;
-- ALTER TABLE rentals ADD COLUMN insurance_claimed BOOLEAN DEFAULT FALSE;
-- ALTER TABLE rentals ADD COLUMN death_verification_method VARCHAR(20); -- 'api', 'contract', 'dual'
```

### API Endpoints

- `GET /rentals/available` - List marketplace chickens
- `POST /rentals/create` - Create rental/delegation
- `POST /rentals/rent` - Rent a chicken
- `POST /rentals/cancel` - Cancel rental (owner only, limited conditions)
- `GET /rentals/my-rentals` - Get user's owned and rented chickens
- `GET /rentals/history` - Get rental history
- `POST /rentals/:id/claim-insurance` - Claim insurance (future implementation)
- `GET /rentals/:id/death-status` - Get verified death status (future implementation)

### Smart Contract Integration

- Use existing `initializeContracts()` helper
- **Legacy chickens only**: Use threshold logic to determine when to check contract
- **Genesis chickens**: Skip contract death verification (cannot die)
- Implement proper error handling for contract calls

## Security Considerations

1. **Signature Verification**: All contract calls require backend-signed messages
2. **Dual Verification**: Prevents single point of failure in death detection
3. **Insurance Escrow**: Funds held securely in smart contract
4. **Ownership Validation**: Multiple layers of ownership verification
5. **Admin Override**: Manual intervention capability for edge cases

## Current Cancellation Limitations

### Existing Implementation

**What Works:**

- Owner can cancel **listings** (status = `AVAILABLE`)
- Owner can cancel **free delegations** (roninPrice = 0)
- Cancellation updates status to `CANCELLED`
- Frontend integration exists (`useMyRentals` hook)

**What Doesn't Work:**

- **Cannot cancel active paid rentals** (status = `RENTED` with roninPrice > 0)
- **No renter cancellation** capability
- **No penalty system** implemented
- **No insurance claiming** mechanism
- **No mutual cancellation** process

### Current Cancellation Rules

1. **Owner-Only Cancellation**:

   - Must be the rental owner
   - Can only cancel if NOT (status = `RENTED` AND roninPrice > 0)
   - Uses `unlistChickenForRent` contract function
   - No penalties or refunds implemented

2. **Restrictions**:
   - Renters cannot cancel
   - Active paid rentals cannot be cancelled
   - No dispute resolution mechanism

## Future Enhancements

1. **Automated Claiming**: Auto-claim insurance after verification period
2. **Dispute Resolution**: Formal process for verification mismatches
3. **Insurance Tiers**: Variable insurance rates based on chicken value
4. **Rental Extensions**: Allow extending rental periods
5. **Bulk Operations**: Batch rental and claiming operations
6. **Cancellation Analytics**: Track cancellation patterns and reasons
7. **Reputation System**: Rate owners/renters based on cancellation history
