const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");
const { time } = require("@nomicfoundation/hardhat-network-helpers");

describe("SabongSagaRentalUpgradeable Contract", function () {
  let rental;
  let genesisChicken, legacyChicken;
  let owner, admin, renter, chickenOwner, feeWallet, revShareWallet, signer;
  const feePercentage = 450; // 4.5%
  const revSharePercentage = 50; // 0.5%

  beforeEach(async function () {
    [owner, admin, renter, chickenOwner, feeWallet, revShareWallet, signer] = await ethers.getSigners();
    
    // Deploy mock NFT contracts
    const MockERC721 = await ethers.getContractFactory("MockERC721");
    genesisChicken = await MockERC721.deploy("Genesis Chicken", "GCHICKEN", 0);
    await genesisChicken.waitForDeployment();
    
    legacyChicken = await MockERC721.deploy("Legacy Chicken", "LCHICKEN", 2222);
    await legacyChicken.waitForDeployment();
    
    // Mint NFTs to chickenOwner
    await genesisChicken.mint(chickenOwner.address); // ID 1
    await legacyChicken.mint(chickenOwner.address);  // ID 2223

    const genesisChickenAddress = await genesisChicken.getAddress();
    const legacyChickenAddress = await legacyChicken.getAddress();
    
    // Deploy Rental Contract using upgrades plugin
    const Rental = await ethers.getContractFactory("SabongSagaRentalUpgradeable");
    rental = await upgrades.deployProxy(Rental, [
      genesisChickenAddress,
      legacyChickenAddress,
      feeWallet.address,
      feePercentage,
      revShareWallet.address,
      revSharePercentage,
      admin.address
    ], {
      initializer: 'initialize',
      kind: 'transparent'
    });
    await rental.waitForDeployment();
    
    // Set signer address
    await rental.connect(admin).setSigner(signer.address);
    
    // Approve rental contract to transfer NFTs
    await genesisChicken.connect(chickenOwner).setApprovalForAll(await rental.getAddress(), true);
    await legacyChicken.connect(chickenOwner).setApprovalForAll(await rental.getAddress(), true);
    
    await genesisChicken.connect(chickenOwner).setApprovalForAll(chickenOwner.address, true);
    await legacyChicken.connect(chickenOwner).setApprovalForAll(chickenOwner.address, true);
    
  });

  // Helper function to generate signature for listing
  async function getListingSignature(chickenId, rentId, ethPrice, insurancePrice, rentDuration, ownerAddress) {
    const messageHash = ethers.solidityPackedKeccak256(
      ["uint256", "uint256", "uint256", "uint256", "uint256", "address"],
      [chickenId, rentId, ethPrice, insurancePrice, rentDuration, ownerAddress]
    );
    const messageHashBinary = ethers.getBytes(messageHash);
    return signer.signMessage(ethers.getBytes(messageHashBinary));
  }

  // Helper function to generate signature for unlisting
  async function getUnlistingSignature(rentId, ownerAddress, contractAddress) {
    const messageHash = ethers.solidityPackedKeccak256(
      ["uint256", "address", "address"],
      [rentId, ownerAddress, contractAddress]
    );
    const messageHashBinary = ethers.getBytes(messageHash);
    return signer.signMessage(ethers.getBytes(messageHashBinary));
  }

  // Helper function to generate signature for renting
  async function getRentingSignature(rentId, chickenId, ethPrice, insurancePrice, renterAddress, renterWallet, ownerAddress) {
    const messageHash = ethers.solidityPackedKeccak256(
      ["uint256", "uint256", "uint256", "uint256", "address", "address", "address"],
      [rentId, chickenId, ethPrice, insurancePrice, renterAddress, renterWallet, ownerAddress]
    );

    const messageHashBinary = ethers.getBytes(messageHash);
    return signer.signMessage(ethers.getBytes(messageHashBinary));
  }

  describe("End-to-End Rental Flow", function() {
    it("Should complete full rental lifecycle", async function() {
      // 1. List a chicken for rent
      const rentId = 1;
      const chickenId = 1; // Genesis chicken
      const ethPrice = ethers.parseEther("0.1");
      const insurancePrice = 0n;
      const rentDuration = 86400; // 1 day in seconds
      
      const listingSignature = await getListingSignature(
        chickenId, 
        rentId, 
        ethPrice,
        insurancePrice,
        rentDuration,
        chickenOwner.address
      );
      
      await expect(
        rental.connect(chickenOwner).listChickenForRent(
          chickenId,
          rentId,
          ethPrice,
          insurancePrice,
          rentDuration,
          listingSignature
        )
      ).to.emit(rental, "ChickenListedForRent")
        .withArgs(rentId, chickenId, chickenOwner.address);
      
      // Verify chicken ownership transferred to contract
      expect(await genesisChicken.ownerOf(chickenId)).to.equal(await rental.getAddress());
      
      // 2. Rent the chicken
      const renterAddress = ethers.Wallet.createRandom().address;

      const rentSignature = await getRentingSignature(
        rentId,
        chickenId,
        ethPrice,
        insurancePrice,
        renterAddress,
        renter.address,
        chickenOwner.address
      );
      
      const feeAmount = (ethPrice * BigInt(feePercentage)) / 10000n;
      const revShareAmount = (ethPrice * BigInt(revSharePercentage)) / 10000n;
      const ownerAmount = ethPrice - feeAmount - revShareAmount;

      const feeInsuranceAmount = (insurancePrice * BigInt(feePercentage)) / 10000n;
      const revShareInsuranceAmount = (insurancePrice * BigInt(revSharePercentage)) / 10000n;

      const feeWalletBalanceBefore = await ethers.provider.getBalance(feeWallet.address);
      const revShareBalanceBefore = await ethers.provider.getBalance(revShareWallet.address);
      const ownerBalanceBefore = await ethers.provider.getBalance(chickenOwner.address);

      const rentParams = {
        rentId: rentId,
        chickenId: chickenId,
        ethPrice: ethPrice,
        insurancePrice: insurancePrice,
        renterAddress: renterAddress,
        ownerAddress: chickenOwner.address,
        signature: rentSignature
      };
      
      await rental.connect(renter).rentChicken(
        rentParams,
        { value: ethPrice + insurancePrice }
      ).then(async (tx) => {
        const receipt = await tx.wait();
        const rentalInfo = await rental.rentals(rentId);
        const expiresAt = rentalInfo.expiresAt;
        
        await expect(tx)
          .to.emit(rental, "ChickenRented")
          .withArgs(rentId, renter.address, expiresAt);
      });
      
      // Verify fee distribution
      const feeWalletBalanceAfter = await ethers.provider.getBalance(feeWallet.address);
      const revShareBalanceAfter = await ethers.provider.getBalance(revShareWallet.address);
      const ownerBalanceAfter = await ethers.provider.getBalance(chickenOwner.address);
      
      expect(feeWalletBalanceAfter - feeWalletBalanceBefore).to.equal(feeAmount + feeInsuranceAmount);
      expect(revShareBalanceAfter - revShareBalanceBefore).to.equal(revShareAmount + revShareInsuranceAmount);
      expect(ownerBalanceAfter - ownerBalanceBefore).to.equal(ownerAmount);
      
      // 3. Wait for rental period to end
      await time.increase(rentDuration + 1);
      
      // 4. Unlist the chicken
      const unlistingSignature = await getUnlistingSignature(
        rentId,
        chickenOwner.address,
        await rental.getAddress()
      );
      
      await expect(
        rental.connect(chickenOwner).unlistChickenForRent(
          rentId,
          chickenId,
          unlistingSignature
        )
      ).to.emit(rental, "ChickenUnlistedForRent")
        .withArgs(rentId, chickenId, chickenOwner.address);
      
      // Verify chicken ownership returned to original owner
      expect(await genesisChicken.ownerOf(chickenId)).to.equal(chickenOwner.address);
      
      // Verify rental is no longer active
      const rentalInfo = await rental.rentals(rentId);
      expect(rentalInfo.activeListing).to.be.false;
    });
    
    it("Should handle insurance claims", async function() {
      // 1. List a legacy chicken for rent
      const rentId = 2;
      const chickenId = 2223; // Legacy chicken
      const ethPrice = ethers.parseEther("0.1");
      const insurancePrice = ethers.parseEther("0.05");
      const rentDuration = 86400; // 1 day in seconds

      // Create a new random wallet and connect it to the provider
      const newChickenOwner = ethers.Wallet.createRandom().connect(ethers.provider);

      //transfer ETH to newChickenOwner 
      await owner.sendTransaction({
        to: newChickenOwner.address,
        value: ethers.parseEther("100")
      });

      //send chicken to new owner
      await legacyChicken.connect(chickenOwner).transferFrom(chickenOwner.address, newChickenOwner.address, chickenId);
      
      // Approve rental contract to transfer NFTs
      await legacyChicken.connect(newChickenOwner).setApprovalForAll(await rental.getAddress(), true);

      const listingSignature = await getListingSignature(
        chickenId, 
        rentId, 
        ethPrice,
        insurancePrice,
        rentDuration,
        newChickenOwner.address
      );
      
      await rental.connect(newChickenOwner).listChickenForRent(
        chickenId,
        rentId,
        ethPrice,
        insurancePrice,
        rentDuration,
        listingSignature
      );
      
      // 2. Rent the chicken
      const renterAddress = ethers.Wallet.createRandom().address;
      const rentSignature = await getRentingSignature(
        rentId,
        chickenId,
        ethPrice,
        insurancePrice,
        renterAddress,
        renter.address,
        newChickenOwner.address
      );
      
      const rentParams = {
        rentId: rentId,
        chickenId: chickenId,
        ethPrice: ethPrice,
        insurancePrice: insurancePrice,
        renterAddress: renterAddress,
        ownerAddress: newChickenOwner.address,
        signature: rentSignature
      };
      
      await rental.connect(renter).rentChicken(
        rentParams,
        { value: ethPrice + insurancePrice }
      );
      
      // 3. Simulate chicken death (burn the NFT)
      await legacyChicken.burn(chickenId);
      
      // 4. Wait for rental period to end
      await time.increase(rentDuration + 1);
      
      // 5. Owner claims insurance
      const ownerBalanceBefore = await ethers.provider.getBalance(newChickenOwner.address);

      let netInsurancePrice = insurancePrice - (insurancePrice * BigInt(feePercentage) / 10000n) - (insurancePrice * BigInt(revSharePercentage) / 10000n);

      const claimTx = await rental.connect(newChickenOwner).claimInsurance(rentId);
      const receipt = await claimTx.wait();
      const gasCost = receipt.gasUsed * receipt.gasPrice;
      
      await expect(claimTx)
        .to.emit(rental, "InsuranceClaimed")
        .withArgs(rentId, newChickenOwner.address, netInsurancePrice);
      
      const ownerBalanceAfter = await ethers.provider.getBalance(newChickenOwner.address);
      
      // Account for gas costs in the balance comparison
      expect(ownerBalanceAfter - ownerBalanceBefore + gasCost).to.be.eq(netInsurancePrice);
      
      // 6. Verify insurance is marked as claimed
      const rentalInfo = await rental.rentals(rentId);
      expect(rentalInfo.insuranceClaimed).to.be.true;
      
      // 7. Verify second claim attempt fails
      await expect(
        rental.connect(newChickenOwner).claimInsurance(rentId)
      ).to.be.revertedWithCustomError(rental, "ErrInsuranceAlreadyClaimed");
    });

    it("Should allow renter to claim insurance if the chicken is alive", async function() {
      // 1. List a legacy chicken for rent
      const rentId = 3;
      const chickenId = 2224; // Legacy chicken
      const ethPrice = ethers.parseEther("0.1");
      const insurancePrice = ethers.parseEther("0.05");
      const rentDuration = 86400; // 1 day in seconds

      // Mint a new chicken for this test
      await legacyChicken.mint(chickenOwner.address);
      
      // Approve rental contract to transfer NFTs
      await legacyChicken.connect(chickenOwner).setApprovalForAll(await rental.getAddress(), true);

      const listingSignature = await getListingSignature(
        chickenId, 
        rentId, 
        ethPrice,
        insurancePrice,
        rentDuration,
        chickenOwner.address
      );
      
      await rental.connect(chickenOwner).listChickenForRent(
        chickenId,
        rentId,
        ethPrice,
        insurancePrice,
        rentDuration,
        listingSignature
      );
      
      // 2. Rent the chicken
      const renterAddress = ethers.Wallet.createRandom().address;
      const rentSignature = await getRentingSignature(
        rentId,
        chickenId,
        ethPrice,
        insurancePrice,
        renterAddress,
        renter.address,
        chickenOwner.address
      );
      
      const rentParams = {
        rentId: rentId,
        chickenId: chickenId,
        ethPrice: ethPrice,
        insurancePrice: insurancePrice,
        renterAddress: renterAddress,
        ownerAddress: chickenOwner.address,
        signature: rentSignature
      };
      
      await rental.connect(renter).rentChicken(
        rentParams,
        { value: ethPrice + insurancePrice }
      );
      
      // 3. Wait for rental period to end
      await time.increase(rentDuration + 1);
      
      // 4. Renter claims insurance (chicken is still alive)
      const renterBalanceBefore = await ethers.provider.getBalance(renter.address);
      
      let netInsurancePrice = insurancePrice - (insurancePrice * BigInt(feePercentage) / 10000n) - (insurancePrice * BigInt(revSharePercentage) / 10000n);
      
      const claimTx = await rental.connect(renter).claimInsurance(rentId);
      const receipt = await claimTx.wait();
      const gasCost = receipt.gasUsed * receipt.gasPrice;
      
      await expect(claimTx)
        .to.emit(rental, "InsuranceClaimed")
        .withArgs(rentId, renter.address, netInsurancePrice);
      
      const renterBalanceAfter = await ethers.provider.getBalance(renter.address);
      
      // Account for gas costs in the balance comparison
      expect(renterBalanceAfter - renterBalanceBefore + gasCost).to.be.eq(netInsurancePrice);
    });

    it("Should handle delegated rental with zero ethPrice", async function() {
      // 1. List a chicken for rent with zero price (delegated rental)
      const rentId = 4;
      const chickenId = 1; // Genesis chicken
      const ethPrice = 0n;
      const insurancePrice = 0n; // Small insurance fee
      const rentDuration = 86400; // 1 day in seconds
      
      const listingSignature = await getListingSignature(
        chickenId, 
        rentId, 
        ethPrice,
        insurancePrice,
        rentDuration,
        chickenOwner.address
      );
      
      await expect(
        rental.connect(chickenOwner).listChickenForRent(
          chickenId,
          rentId,
          ethPrice,
          insurancePrice,
          rentDuration,
          listingSignature
        )
      ).to.emit(rental, "ChickenListedForRent")
        .withArgs(rentId, chickenId, chickenOwner.address);
      
      // 2. Rent the chicken with zero price
      const renterAddress = ethers.Wallet.createRandom().address;
      const rentSignature = await getRentingSignature(
        rentId,
        chickenId,
        ethPrice,
        insurancePrice,
        renterAddress,
        renter.address,
        chickenOwner.address
      );
      
      const feeInsuranceAmount = (insurancePrice * BigInt(feePercentage)) / 10000n;
      const revShareInsuranceAmount = (insurancePrice * BigInt(revSharePercentage)) / 10000n;
      
      const feeWalletBalanceBefore = await ethers.provider.getBalance(feeWallet.address);
      const revShareBalanceBefore = await ethers.provider.getBalance(revShareWallet.address);
      
      const rentParams = {
        rentId: rentId,
        chickenId: chickenId,
        ethPrice: ethPrice,
        insurancePrice: insurancePrice,
        renterAddress: renterAddress,
        ownerAddress: chickenOwner.address,
        signature: rentSignature
      };
      
    await rental.connect(renter).rentChicken(
      rentParams,
      { value: ethPrice + insurancePrice }
    ).then(async (tx) => {
      const receipt = await tx.wait();
      const rentalInfo = await rental.rentals(rentId);
      const expiresAt = rentalInfo.expiresAt;
      
      await expect(tx)
        .to.emit(rental, "ChickenRented")
        .withArgs(rentId, renter.address, expiresAt);
    });
      
      // Verify fee distribution for insurance only
      const feeWalletBalanceAfter = await ethers.provider.getBalance(feeWallet.address);
      const revShareBalanceAfter = await ethers.provider.getBalance(revShareWallet.address);
      
      expect(feeWalletBalanceAfter - feeWalletBalanceBefore).to.equal(feeInsuranceAmount);
      expect(revShareBalanceAfter - revShareBalanceBefore).to.equal(revShareInsuranceAmount);
      
      // 3. Wait for rental period to end
      await time.increase(rentDuration + 1);
      
      // 4. Unlist the chicken
      const unlistingSignature = await getUnlistingSignature(
        rentId,
        chickenOwner.address,
        await rental.getAddress()
      );
      
      await expect(
        rental.connect(chickenOwner).unlistChickenForRent(
          rentId,
          chickenId,
          unlistingSignature
        )
      ).to.emit(rental, "ChickenUnlistedForRent")
        .withArgs(rentId, chickenId, chickenOwner.address);
      
      // Verify chicken ownership returned to original owner
      expect(await genesisChicken.ownerOf(chickenId)).to.equal(chickenOwner.address);
    });
    
    it("Should prevent Genesis chickens from having insurance", async function() {
      // 1. Try to list a Genesis chicken with insurance
      const rentId = 5;
      const chickenId = 1; // Genesis chicken
      const ethPrice = ethers.parseEther("0.1");
      const insurancePrice = ethers.parseEther("0.05"); // Should be rejected
      const rentDuration = 86400; // 1 day in seconds
      
      const listingSignature = await getListingSignature(
        chickenId, 
        rentId, 
        ethPrice,
        insurancePrice,
        rentDuration,
        chickenOwner.address
      );
      
      await expect(
        rental.connect(chickenOwner).listChickenForRent(
          chickenId,
          rentId,
          ethPrice,
          insurancePrice,
          rentDuration,
          listingSignature
        )
      ).to.be.revertedWithCustomError(rental, "ErrInvalidInsurancePrice");
    });
    
    it("Should allow bulk retrieval of rental info", async function() {
      // 1. List multiple chickens for rent
      const rentIds = [6, 7];
      const chickenIds = [1, 2223]; // Genesis and Legacy
      const ethPrices = [ethers.parseEther("0.1"), ethers.parseEther("0.2")];
      const insurancePrices = [0n, ethers.parseEther("0.05")];
      const rentDurations = [86400, 172800]; // 1 day and 2 days
      
      // Mint another Genesis chicken
      await genesisChicken.mint(chickenOwner.address); // ID 2
      
      for (let i = 0; i < rentIds.length; i++) {
        const listingSignature = await getListingSignature(
          chickenIds[i], 
          rentIds[i], 
          ethPrices[i],
          insurancePrices[i],
          rentDurations[i],
          chickenOwner.address
        );
        
        await rental.connect(chickenOwner).listChickenForRent(
          chickenIds[i],
          rentIds[i],
          ethPrices[i],
          insurancePrices[i],
          rentDurations[i],
          listingSignature
        );
      }
      
      // Retrieve bulk rental info
      const rentalInfos = await rental.getRentalInfoBulk(rentIds);
      
      // Verify rental info
      for (let i = 0; i < rentIds.length; i++) {
        expect(rentalInfos[i].rentId).to.equal(rentIds[i]);
        expect(rentalInfos[i].chickenId).to.equal(chickenIds[i]);
        expect(rentalInfos[i].ethPrice).to.equal(ethPrices[i]);
        expect(rentalInfos[i].insurancePrice).to.equal(insurancePrices[i]);
        expect(rentalInfos[i].owner).to.equal(chickenOwner.address);
        expect(rentalInfos[i].rentDuration).to.equal(rentDurations[i]);
        expect(rentalInfos[i].activeListing).to.be.true;
      }
    });
    
    it("Should allow admin to update contract parameters", async function() {
      // Test updating fee percentage
      const newFeePercentage = 500; // 5%
      await rental.connect(admin).setFeePercentage(newFeePercentage);
      expect(await rental.feePercentage()).to.equal(newFeePercentage);
      
      // Test updating fee wallet
      const newFeeWallet = ethers.Wallet.createRandom().address;
      await rental.connect(admin).setTreasuryAddress(newFeeWallet);
      expect(await rental.feeWallet()).to.equal(newFeeWallet);
      
      // Test updating rev share address
      const newRevShareAddress = ethers.Wallet.createRandom().address;
      await rental.connect(admin).setRevShareAddress(newRevShareAddress);
      expect(await rental.revShareAddress()).to.equal(newRevShareAddress);
      
      // Test updating signer
      const newSigner = ethers.Wallet.createRandom().address;
      await rental.connect(admin).setSigner(newSigner);
      expect(await rental.signer()).to.equal(newSigner);
    });
    
    it("Should allow emergency ETH release", async function() {
      // Send some ETH to the contract
      await owner.sendTransaction({
        to: await rental.getAddress(),
        value: ethers.parseEther("1.0")
      });
      
      const recipientAddress = ethers.Wallet.createRandom().address;
      const recipientBalanceBefore = await ethers.provider.getBalance(recipientAddress);
      const contractBalanceBefore = await ethers.provider.getBalance(await rental.getAddress());
      
      await expect(
        rental.connect(admin).emergencyReleaseETH(recipientAddress)
      ).to.emit(rental, "EmergencyETHRelease")
        .withArgs(recipientAddress, contractBalanceBefore);
      
      const recipientBalanceAfter = await ethers.provider.getBalance(recipientAddress);
      const contractBalanceAfter = await ethers.provider.getBalance(await rental.getAddress());
      
      expect(recipientBalanceAfter - recipientBalanceBefore).to.equal(contractBalanceBefore);
      expect(contractBalanceAfter).to.equal(0);
    });
  });
});
