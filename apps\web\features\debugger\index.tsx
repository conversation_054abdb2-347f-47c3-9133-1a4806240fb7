"use client";

import AppNavbar from "@/components/shared/navbar";
import { useQueryState } from "nuqs";
import { useMemo, useState } from "react";
import { Address } from "viem";
import useChickens from "../breeding/tab/breeding/hooks/useChickens";
import useEggInfo from "../breeding/tab/hatching/hooks/useEggInfo";
import { useEggPhases } from "../breeding/tab/hatching/hooks/useEggPhases";
import { DebuggerTabs, type IStatsData } from "./components";

export default function Debugger() {
  // Address-based debugger state
  const [address, setAddress] = useQueryState("address", { defaultValue: "" });
  const [inputAddress, setInputAddress] = useState(address || "");
  const [searchTokenId, setSearchTokenId] = useState("");

  // Token ID-based debugger state
  const [tokenId, setTokenId] = useQueryState("tokenId", { defaultValue: "" });
  const [inputTokenId, setInputTokenId] = useState(tokenId || "");
  const [submittedTokenId, setSubmittedTokenId] = useState<string | null>(
    tokenId || null
  );

  // Tab selection state
  const [selectedTab, setSelectedTab] = useState<string>(
    tokenId ? "token-based" : "address-based"
  );

  // Create a wrapper function for setSelectedTab to handle string | number
  const handleTabChange = (tab: string | number) => {
    setSelectedTab(String(tab));
  };
  const chickensQuery = useChickens(address as Address);

  // Get egg info for all eggs
  const eggTokenIds =
    chickensQuery.eggs?.map((egg) => Number(egg.tokenId)) || [];
  const { eggInfoMap } = useEggInfo(eggTokenIds);

  // Get eggs in different phases
  const { breedingPhaseEggs = [], hatchingPhaseEggs = [] } = useEggPhases(
    eggTokenIds,
    eggInfoMap
  ) as { breedingPhaseEggs: number[]; hatchingPhaseEggs: number[] };

  const handleAddressSubmit = (e: React.FormEvent): void => {
    e.preventDefault();
    setAddress(inputAddress.toLowerCase());
  };

  const handleTokenIdSubmit = (e: React.FormEvent): void => {
    e.preventDefault();
    setTokenId(inputTokenId);
    setSubmittedTokenId(inputTokenId);
  };

  // Function to navigate from token ID view to address view
  const handleViewOwnerData = (ownerAddress: string): void => {
    setInputAddress(ownerAddress);
    setAddress(ownerAddress);

    setSelectedTab("address-based"); // Select the address lookup tab

    setTokenId(""); // Clear tokenId to switch to address view
    setSubmittedTokenId(null); // Reset submitted token ID
  };

  // Filter data based on search input
  const filterDataByTokenId = <T extends { tokenId: string }>(
    data: T[]
  ): T[] => {
    if (!searchTokenId.trim()) return data;
    return data.filter(
      (item) =>
        item.tokenId && item.tokenId.toString().includes(searchTokenId.trim())
    );
  };

  const filteredChickens = filterDataByTokenId(chickensQuery.chickens || []);
  const filteredEggs = filterDataByTokenId(chickensQuery.eggs || []);
  const filteredNullAttributes = filterDataByTokenId(
    chickensQuery.chickensNullAttributes || []
  );

  // Calculate total filtered and total items
  const totalFilteredItems =
    filteredChickens.length +
    filteredEggs.length +
    filteredNullAttributes.length;
  const totalItems =
    (chickensQuery.chickens?.length || 0) +
    (chickensQuery.eggs?.length || 0) +
    (chickensQuery.chickensNullAttributes?.length || 0);

  // Calculate statistics from chicken data
  const chickenStats = useMemo(() => {
    if (!chickensQuery.chickens || chickensQuery.chickens.length === 0) {
      return null;
    }

    const stats: IStatsData = {
      totalChickens: chickensQuery.chickens.length,
      totalEggs: chickensQuery.eggs.length,
      totalNullAttributes: chickensQuery.chickensNullAttributes.length,
      typeDistribution: {} as Record<string, number>,
      averageStats: {
        innateAttack: 0,
        innateDefense: 0,
        innateHealth: 0,
        innateSpeed: 0,
        gritAttack: 0,
        gritDefense: 0,
        gritHealth: 0,
        gritSpeed: 0,
      },
    };

    // Process chickens
    let chickensInCooldown = 0;
    let totalDailyFeathers = 0;

    chickensQuery.chickens.forEach((chicken) => {
      // Type distribution
      if (chicken.attributes?.Type?.[0]) {
        const type = chicken.attributes.Type[0];
        stats.typeDistribution[type] = (stats.typeDistribution[type] || 0) + 1;
      }

      // Economic stats - daily feathers
      if (chicken.attributes?.["Daily Feathers"]?.[0]) {
        const dailyFeathers =
          parseInt(chicken.attributes["Daily Feathers"][0]) || 0;
        totalDailyFeathers += dailyFeathers;
      }

      // Breeding stats - cooldown
      const tokenId = Number(chicken.tokenId);
      if (chickensQuery.isOnCooldown && chickensQuery.isOnCooldown(tokenId)) {
        chickensInCooldown++;
      }

      // Stats accumulation for averages
      const statFields = [
        { field: "innate attack", avgKey: "innateAttack" },
        { field: "innate defense", avgKey: "innateDefense" },
        { field: "innate health", avgKey: "innateHealth" },
        { field: "innate speed", avgKey: "innateSpeed" },
        { field: "grit attack", avgKey: "gritAttack" },
        { field: "grit defense", avgKey: "gritDefense" },
        { field: "grit health", avgKey: "gritHealth" },
        { field: "grit speed", avgKey: "gritSpeed" },
      ];

      statFields.forEach(({ field, avgKey }) => {
        // Use type assertion to avoid TypeScript errors with dynamic property access
        if ((chicken.attributes as any)?.[field]?.[0]) {
          const value = parseInt((chicken.attributes as any)[field][0]) || 0;
          // Use type assertion for the averageStats object as well
          (stats.averageStats as any)[avgKey] += value;
        }
      });
    });

    // Calculate averages
    const count = stats.totalChickens || 1; // Avoid division by zero

    // Calculate stat averages
    Object.keys(stats.averageStats).forEach((key) => {
      // Use type assertion to avoid TypeScript errors with dynamic property access
      const typedKey = key as keyof typeof stats.averageStats;
      stats.averageStats[typedKey] = Math.round(
        stats.averageStats[typedKey] / count
      );
    });

    // Add additional stats
    stats.chickensInCooldown = chickensInCooldown;
    stats.totalDailyFeathers = totalDailyFeathers;

    // Add egg phase statistics from the hooks
    stats.breedingPhaseEggs = breedingPhaseEggs.length;
    stats.hatchingPhaseEggs = hatchingPhaseEggs.length;

    return stats;
  }, [chickensQuery, breedingPhaseEggs.length, hatchingPhaseEggs.length]);

  // Prepare address-based debugger data
  const addressData = address
    ? {
        address,
        chickens: filteredChickens,
        eggs: filteredEggs,
        nullAttributes: filteredNullAttributes,
        searchTokenId,
        chickensTotal: chickensQuery.chickens?.length || 0,
        eggsTotal: chickensQuery.eggs?.length || 0,
        nullAttributesTotal: chickensQuery.chickensNullAttributes?.length || 0,
      }
    : null;

  // Prepare token ID-based debugger data
  const tokenIdData = {
    inputTokenId,
    setInputTokenId,
    handleTokenIdSubmit,
    submittedTokenId,
  };

  // Render the component
  return (
    <div>
      <div className="sticky top-0 z-20 bg-stone-900/80 backdrop-blur-sm border-b border-primary/10">
        <AppNavbar />
      </div>

      <div className="bg-gray-50 text-gray-900 min-h-screen p-4 md:p-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
            <h1 className="text-2xl font-bold mb-2 text-primary">
              Chicken Data Debugger
            </h1>
            <p className="text-gray-500 mb-4">
              Explore and analyze chicken data by wallet address or token ID
            </p>

            {/* Tabbed Interface for both debugger modes */}
            <DebuggerTabs
              addressData={addressData}
              tokenIdData={tokenIdData}
              onViewOwnerData={handleViewOwnerData}
              inputAddress={inputAddress}
              setInputAddress={setInputAddress}
              handleAddressSubmit={handleAddressSubmit}
              searchTokenId={searchTokenId}
              setSearchTokenId={setSearchTokenId}
              totalItems={totalItems}
              filteredItems={totalFilteredItems}
              chickenStats={chickenStats}
              isLoading={Boolean(!tokenId && address && chickensQuery.loading)}
              error={
                !tokenId && address && chickensQuery.error
                  ? chickensQuery.error.message
                  : null
              }
              selectedTab={selectedTab}
              setSelectedTab={handleTabChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
