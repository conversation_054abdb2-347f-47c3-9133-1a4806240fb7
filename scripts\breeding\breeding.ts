import { ethers } from "hardhat";
// import hre from "hardhat";

// import {
//   time,
//   loadFixture,
//   mine,
// } from "@nomicfoundation/hardhat-toolbox/network-helpers";

const signer = "******************************************";
const legacyChickenAddress = "******************************************";

async function main() {
  // await mine();

  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);

  const accountBalance = await deployer.provider.getBalance(deployer.address);

  console.log("Account balance:", accountBalance.toString());

  const ERC721Sale = await ethers.getContractFactory("ERC721Sale");
  const erc721Sale = await ERC721Sale.deploy(
    "******************************************",
    "Sabong Saga Genesis",
    "SSGENESIS",
    "https://chicken-api-ivory.vercel.app/api/"
  );
  await erc721Sale.waitForDeployment();
  const sabongSagaGenesisChickenAddress = await erc721Sale.getAddress();

  console.log(
    "sabongSagaGenesisChickenAddress:",
    sabongSagaGenesisChickenAddress
  );

  const sabongSagaChickensAddress = legacyChickenAddress;

  console.log("sabongSagaChickensAddress:", sabongSagaChickensAddress);

  const ERC1155Common = await ethers.getContractFactory("ERC1155Common");
  const erc1155Common = await ERC1155Common.deploy(
    "******************************************",
    "Sabong Saga Items",
    "SSITEMS",
    "https://item-api-beta.vercel.app/api/"
  );

  await erc1155Common.waitForDeployment();
  const erc1155CommonAddress = await erc1155Common.getAddress();

  console.log("erc1155CommonAddress:", erc1155CommonAddress);

  const ERC20Common = await ethers.getContractFactory("ERC20Common");
  const erc20Common = await ERC20Common.deploy(
    "******************************************",
    "******************************************",
    "******************************************",
    true,
    "COCK",
    "COCK",
    18
  );
  await erc20Common.waitForDeployment();

  const erc20CommonAddress = await erc20Common.getAddress();
  console.log("ERC20Common Address:", erc20CommonAddress);

  const SabongSagaBreeding = await ethers.getContractFactory(
    "SabongSagaBreeding"
  );
  const sabongSagaBreeding = await SabongSagaBreeding.deploy(
    erc20CommonAddress,
    sabongSagaGenesisChickenAddress,
    sabongSagaChickensAddress,
    erc1155CommonAddress,
    deployer.address,
    3,
    3600,
    signer
  );

  await sabongSagaBreeding.waitForDeployment();
  const sabongSagaBreedingAddress = await sabongSagaBreeding.getAddress();
  console.log("sabongSagaBreedingAddress:", sabongSagaBreedingAddress);
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
