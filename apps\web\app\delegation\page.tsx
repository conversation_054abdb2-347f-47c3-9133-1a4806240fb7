"use client";

import dynamic from "next/dynamic";
import AppInitializer from "@/providers/app/initializer";
import AppNavbar from "@/components/shared/navbar";

const DynamicDelegation = dynamic(() => import("@/features/delegation"), {
  ssr: false,
  loading: () => (
    <div className="container mx-auto px-4 py-8">
      <div className="animate-pulse space-y-6">
        <div className="h-8 bg-stone-800 rounded w-1/3"></div>
        <div className="h-4 bg-stone-800 rounded w-2/3"></div>
        <div className="grid grid-cols-3 gap-4">
          <div className="h-12 bg-stone-800 rounded"></div>
          <div className="h-12 bg-stone-800 rounded"></div>
          <div className="h-12 bg-stone-800 rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="h-80 bg-stone-800 rounded-lg"></div>
          ))}
        </div>
      </div>
    </div>
  ),
});

export default function DelegationPage() {
  return (
    <AppInitializer>
      <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 bg-opacity-85 overflow-hidden">
        <div className="relative z-10">
          <AppNavbar />
        </div>

        <DynamicDelegation />
      </div>
    </AppInitializer>
  );
}
