"use client";

import useChickenMetadata from "@/features/breeding/tab/breeding/hooks/useChickenMetadata";
import { fetchChickens } from "@/features/breeding/tab/breeding/hooks/useChickens";
import { isStaging } from "@/lib/constants";
import { IAttribute, IChickenMetadata } from "@/lib/types/chicken.types";
import { useStateContext } from "@/providers/app/state";
import { useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { DelegationAPI } from "../api/delegation.api";
import { mockChickenQueryData } from "@/features/rewards/ninuno/mock/chicken-query-data";

// Interface for chicken rental status
export interface IChickenRentalStatus {
  isAvailable: boolean;
  rentalStatus?:
    | "available"
    | "listed"
    | "rented"
    | "delegated"
    | "expired"
    | "cancelled";
  statusLabel?: string;
}

// Interface for chicken data suitable for delegation
export interface IChickenForDelegation {
  tokenId: number;
  image: string;
  metadata?: IChickenMetadata;
  dailyFeathers?: number;
  breedCount?: number;
  type?: string;
  isAvailable?: boolean;
  rentalStatus?: IChickenRentalStatus;
  cooldownEndsAt?: Date;
  level?: number;
  winRate?: number;
  battleStats?: IBattleStats;
}

// Interface for battle stats
interface IBattleStats {
  wins: number;
  losses: number;
  level?: number;
  state?: string; // "normal", "faint", "dead", "breeding"
  recoverDate?: string;
  stats?: {
    attack?: number;
    defense?: number;
    speed?: number;
    currentHp?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
    instinct?: string;
  };
}

// Helper function to determine rental status from individual rental data
const getChickenRentalStatusFromData = (
  rental: any | null
): IChickenRentalStatus => {
  if (!rental) {
    // No rental data means chicken is available
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  // Check the rental status
  if (rental.status === 0) {
    // Status 0 = AVAILABLE (listed in rental market)
    return {
      isAvailable: false,
      rentalStatus: "listed",
      statusLabel: "Listed in Rental Market",
    };
  } else if (rental.status === 1) {
    // Status 1 = RENTED (actually rented by someone)
    // Check if it's a paid rental or free delegation
    const roninPrice = rental.roninPrice || rental.ronin_price;
    const isDelegation = roninPrice === "0" || roninPrice === 0;

    if (isDelegation) {
      return {
        isAvailable: false,
        rentalStatus: "delegated",
        statusLabel: "Already Delegated",
      };
    } else {
      return {
        isAvailable: false,
        rentalStatus: "rented",
        statusLabel: "Already Rented",
      };
    }
  } else if (rental.status === 2) {
    // Status 2 = EXPIRED
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  } else if (rental.status === 3) {
    // Status 3 = CANCELLED
    return {
      isAvailable: true,
      rentalStatus: "available",
      statusLabel: "Available",
    };
  }

  // Default to available for unknown statuses
  return {
    isAvailable: true,
    rentalStatus: "available",
    statusLabel: "Available",
  };
};

const fetchChickenBattleStats = async (
  tokenId: number
): Promise<IBattleStats> => {
  try {
    const response = await fetch(`/api/proxy/game?tokenId=${tokenId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch battle stats: ${response.status}`);
    }
    const data = await response.json();
    return {
      wins: data.wins || 0,
      losses: data.losses || 0,
      level: data.level,
      state: data.state || "normal",
      recoverDate: data.recoverDate,
      stats: data.stats,
    };
  } catch (error) {
    // If API call fails, return default stats
    console.warn(`Failed to fetch battle stats for chicken ${tokenId}:`, error);
    return {
      wins: 0,
      losses: 0,
      state: "normal",
    };
  }
};

// Helper functions to check chicken states
const isChickenFaint = (battleStats: IBattleStats): boolean => {
  return battleStats.state === "faint";
};

const isChickenDead = (battleStats: IBattleStats): boolean => {
  return battleStats.state === "dead";
};

const isChickenBreeding = (battleStats: IBattleStats): boolean => {
  return battleStats.state === "breeding";
};

const isChickenOnCooldown = (battleStats: IBattleStats): boolean => {
  return (
    isChickenFaint(battleStats) ||
    isChickenDead(battleStats) ||
    isChickenBreeding(battleStats)
  );
};

export function useChickensForDelegation() {
  const { address, isConnected } = useStateContext();

  // Fetch user's chickens
  let chickenQuery = useQuery({
    queryKey: ["chickens", address],
    queryFn: () => fetchChickens(address!),
    enabled: !!address && isConnected,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
  });

  // Use mock data in staging
  if (
    isStaging &&
    address === "0xED3f049B1a396495D3b0Bea9A13622CE6E8C2FD7".toLowerCase()
  ) {
    chickenQuery = mockChickenQueryData;
  }

  const allChickenTokenIds = useMemo(() => {
    if (!chickenQuery.isSuccess || !chickenQuery.data) {
      return [];
    }
    // Sort the token IDs to ensure consistent array reference
    return chickenQuery.data
      .map((chicken) => Number(chicken.tokenId))
      .sort((a, b) => a - b);
  }, [chickenQuery.isSuccess, chickenQuery.data]);

  // Fetch rental status for all chickens in bulk
  const chickenRentalStatusQuery = useQuery({
    queryKey: ["chickenRentalStatuses", allChickenTokenIds],
    queryFn: async () => {
      if (allChickenTokenIds.length === 0) return {};

      try {
        const response =
          await DelegationAPI.getChickenRentalsBulk(allChickenTokenIds);
        return response.data;
      } catch (error) {
        console.error("Failed to fetch chicken rental statuses:", error);
        // Return empty object on error
        return {};
      }
    },
    enabled: allChickenTokenIds.length > 0,
    staleTime: 30000, // 30 seconds (rental status can change frequently)
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
  });

  // Fetch metadata for chickens
  const metadataQuery = useChickenMetadata(allChickenTokenIds);

  // Create a map for efficient metadata lookup
  const metadataMap = useMemo(() => {
    if (
      !metadataQuery.metadataQuery.isSuccess ||
      !metadataQuery.metadataQuery.data
    ) {
      return new Map();
    }

    // Create map using tokenId as key to match how we access it later
    return new Map(
      allChickenTokenIds.map((tokenId, index) => [
        tokenId,
        metadataQuery.metadataQuery.data![index],
      ])
    );
  }, [
    metadataQuery.metadataQuery.isSuccess,
    metadataQuery.metadataQuery.data,
    allChickenTokenIds,
  ]);

  // Create rental status map from individual rental data
  const rentalStatusMap = useMemo(() => {
    const statusMap: Record<number, IChickenRentalStatus> = {};

    if (chickenRentalStatusQuery.data) {
      allChickenTokenIds.forEach((tokenId) => {
        const rental = chickenRentalStatusQuery.data![tokenId];
        statusMap[tokenId] = getChickenRentalStatusFromData(rental);
      });
    } else {
      // Default to available if no data yet
      allChickenTokenIds.forEach((tokenId) => {
        statusMap[tokenId] = {
          isAvailable: true,
          rentalStatus: "available",
          statusLabel: "Available",
        };
      });
    }

    return statusMap;
  }, [allChickenTokenIds, chickenRentalStatusQuery.data]);

  // Fetch battle stats for all chickens
  const battleStatsQuery = useQuery({
    queryKey: ["chickenBattleStats", allChickenTokenIds],
    queryFn: async () => {
      if (allChickenTokenIds.length === 0) return {};

      const statsChecks = await Promise.allSettled(
        allChickenTokenIds.map(async (tokenId) => ({
          tokenId,
          stats: await fetchChickenBattleStats(tokenId),
        }))
      );

      const statsMap: Record<number, IBattleStats> = {};
      statsChecks.forEach((result, index) => {
        const tokenId = allChickenTokenIds[index];
        if (tokenId !== undefined) {
          if (result.status === "fulfilled") {
            statsMap[tokenId] = result.value.stats;
          } else {
            // If check failed, use default stats
            statsMap[tokenId] = { wins: 0, losses: 0 };
          }
        }
      });

      return statsMap;
    },
    enabled:
      metadataQuery.metadataQuery.isSuccess && allChickenTokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes (battle stats change less frequently)
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
  });

  // Format chickens data for delegation
  const chickensForDelegation: IChickenForDelegation[] = useMemo(() => {
    if (!chickenQuery.isSuccess || !metadataQuery.metadataQuery.isSuccess) {
      return [];
    }

    return allChickenTokenIds.map((tokenId) => {
      const chickenMetadata = metadataMap.get(tokenId);
      const rentalStatus = rentalStatusMap[tokenId] ?? {
        isAvailable: true,
        rentalStatus: "available",
        statusLabel: "Available",
      };
      const battleStats = battleStatsQuery.data?.[tokenId] ?? {
        wins: 0,
        losses: 0,
        state: "normal",
      };

      // Check if chicken is on cooldown (faint, dead, or breeding)
      const isOnCooldown = isChickenOnCooldown(battleStats);

      // Extract attributes from metadata
      const typeAttribute = chickenMetadata?.attributes.find(
        (attr: IAttribute) => attr.trait_type === "Type"
      );
      const dailyFeathersAttribute = chickenMetadata?.attributes.find(
        (attr: IAttribute) => attr.trait_type === "Daily Feathers"
      );
      const levelAttribute = chickenMetadata?.attributes.find(
        (attr: IAttribute) => attr.trait_type === "Level"
      );
      const breedCountAttribute = chickenMetadata?.attributes.find(
        (attr: IAttribute) => attr.trait_type === "Breed Count"
      );

      // Calculate win rate from battle stats
      const totalBattles = battleStats.wins + battleStats.losses;
      const winRate =
        totalBattles > 0
          ? Math.round((battleStats.wins / totalBattles) * 100)
          : 0;

      // Determine final availability - chicken must not be rented/listed AND not on cooldown
      const finalAvailability = rentalStatus.isAvailable && !isOnCooldown;

      // Update rental status if chicken is on cooldown
      let finalRentalStatus = rentalStatus;
      if (isOnCooldown && rentalStatus.isAvailable) {
        const stateLabel = isChickenFaint(battleStats)
          ? "Fainted"
          : isChickenDead(battleStats)
            ? "Dead"
            : isChickenBreeding(battleStats)
              ? "Breeding"
              : "On Cooldown";
        finalRentalStatus = {
          isAvailable: false,
          rentalStatus: "available", // Keep as available but mark as unavailable
          statusLabel: stateLabel,
        };
      }

      return {
        tokenId,
        image:
          chickenMetadata?.image ||
          `https://chicken-api-ivory.vercel.app/api/image/${tokenId}.png`,
        metadata: chickenMetadata,
        type: typeAttribute?.value as string,
        dailyFeathers: Number(dailyFeathersAttribute?.value) || 0,
        level: Number(levelAttribute?.value) || 1,
        breedCount: Number(breedCountAttribute?.value) || 0,
        isAvailable: finalAvailability,
        rentalStatus: finalRentalStatus,
        winRate,
        battleStats, // Include battle stats for state checking
      };
    });
  }, [
    chickenQuery.isSuccess,
    metadataQuery.metadataQuery.isSuccess,
    allChickenTokenIds,
    metadataMap,
    rentalStatusMap,
    battleStatsQuery.data,
  ]);

  // Filter chickens by type
  const chickensByType = useMemo(() => {
    const ordinary = chickensForDelegation.filter(
      (chicken) => chicken.type?.toLowerCase() === "ordinary"
    );
    const legacy = chickensForDelegation.filter(
      (chicken) => chicken.type?.toLowerCase() === "legacy"
    );
    const genesis = chickensForDelegation.filter(
      (chicken) => chicken.type?.toLowerCase() === "genesis"
    );

    return { ordinary, legacy, genesis };
  }, [chickensForDelegation]);

  return {
    chickens: chickensForDelegation,
    chickensByType,
    isLoading:
      chickenQuery.isFetching ||
      chickenQuery.isLoading ||
      metadataQuery.isLoading ||
      chickenRentalStatusQuery.isLoading ||
      battleStatsQuery.isLoading,
    error:
      chickenQuery.error ||
      metadataQuery.error ||
      chickenRentalStatusQuery.error ||
      battleStatsQuery.error,
    isConnected,
    address,
  };
}
