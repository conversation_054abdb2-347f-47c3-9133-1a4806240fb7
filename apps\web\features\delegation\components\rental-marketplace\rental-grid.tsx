"use client";

import { useState } from "react";
import { But<PERSON> } from "ui";
import { Grid, List, Search } from "lucide-react";
import {
  IRentalWithMetadata,
  IRentalFilters,
  RENTAL_FILTER_DEFAULTS,
} from "../../types/delegation.types";
import { RentalCard } from "./rental-card";
import { RentalFilters } from "./rental-filters";

interface IRentalGridProps {
  rentals: IRentalWithMetadata[];
  loading?: boolean;
  onRent?: (rental: IRentalWithMetadata) => void;
  currentUserAddress?: string;
  className?: string;
}

export function RentalGrid({
  rentals,
  loading = false,
  onRent,
  currentUserAddress,
  className,
}: IRentalGridProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [filters, setFilters] = useState<IRentalFilters>({
    priceRange: {
      min: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MIN,
      max: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MAX,
    },
    durationRange: {
      min: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MIN,
      max: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MAX,
    },
    chickenType: [],
    rewardDistribution: [],
    delegatedTask: [],
    sortBy: "created",
    sortOrder: "desc",
  });

  // Filter and search logic
  const filteredRentals = rentals.filter((rental) => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesId = rental.chickenTokenId.toString().includes(query);
      const matchesName = rental.chickenMetadata?.name
        ?.toLowerCase()
        .includes(query);
      const matchesOwner = rental.ownerAddress?.toLowerCase().includes(query);
      if (!matchesId && !matchesName && !matchesOwner) return false;
    }

    // Price filter - convert to daily rate
    const totalPriceInRon = parseFloat(rental.roninPrice) / 1e18;
    const durationInDays = rental.rentalPeriod / 86400;
    const dailyRate =
      durationInDays > 0 ? totalPriceInRon / durationInDays : totalPriceInRon;
    if (
      dailyRate < filters.priceRange.min ||
      (filters.priceRange.max !== Number.MAX_SAFE_INTEGER &&
        dailyRate > filters.priceRange.max)
    ) {
      return false;
    }

    // Duration filter
    if (
      durationInDays < filters.durationRange.min ||
      (filters.durationRange.max !== Number.MAX_SAFE_INTEGER &&
        durationInDays > filters.durationRange.max)
    ) {
      return false;
    }

    // Chicken type filter
    if (filters.chickenType.length > 0) {
      const chickenType = rental.chickenMetadata?.attributes?.find(
        (attr) => attr.trait_type === "Type"
      )?.value;
      if (!chickenType || !filters.chickenType.includes(String(chickenType)))
        return false;
    }

    // Reward distribution filter
    if (filters.rewardDistribution.length > 0) {
      if (!filters.rewardDistribution.includes(rental.rewardDistribution))
        return false;
    }

    // Delegated task filter
    if (filters.delegatedTask.length > 0) {
      if (!filters.delegatedTask.includes(rental.delegatedTask)) return false;
    }

    return true;
  });

  // Sort logic
  const sortedRentals = [...filteredRentals].sort((a, b) => {
    let comparison = 0;

    switch (filters.sortBy) {
      case "price":
        comparison = parseFloat(a.roninPrice) - parseFloat(b.roninPrice);
        break;
      case "duration":
        comparison = a.rentalPeriod - b.rentalPeriod;
        break;
      case "created":
        comparison =
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      case "feathers":
        comparison = (a.dailyFeathers || 0) - (b.dailyFeathers || 0);
        break;
      case "tokenId":
        comparison = a.chickenTokenId - b.chickenTokenId;
        break;
      default:
        comparison = 0;
    }

    return filters.sortOrder === "asc" ? comparison : -comparison;
  });

  const resetFilters = () => {
    setFilters({
      priceRange: {
        min: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MIN,
        max: RENTAL_FILTER_DEFAULTS.PRICE_RANGE.MAX,
      },
      durationRange: {
        min: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MIN,
        max: RENTAL_FILTER_DEFAULTS.DURATION_RANGE.MAX,
      },
      chickenType: [],
      rewardDistribution: [],
      delegatedTask: [],
      sortBy: "created",
      sortOrder: "desc",
    });
    setSearchQuery("");
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-20 bg-stone-800 rounded-lg mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="h-80 bg-stone-800 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search and View Controls */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        {/* Search */}
        <div className="relative w-full md:w-96">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
            <Search className="h-5 w-5" />
          </div>
          <input
            type="text"
            className="bg-stone-700 text-white pl-10 pr-4 py-2 rounded-lg w-full focus:outline-none focus:ring-2 focus:ring-yellow-500 border border-stone-600"
            placeholder="Search by ID, name, or owner address..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-2">
          <Button
            size="small"
            appearance={viewMode === "grid" ? "solid" : "outline"}
            onPress={() => setViewMode("grid")}
          >
            <Grid className="w-4 h-4" />
          </Button>
          <Button
            size="small"
            appearance={viewMode === "list" ? "solid" : "outline"}
            onPress={() => setViewMode("list")}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <RentalFilters
        filters={filters}
        onFiltersChange={setFilters}
        onReset={resetFilters}
      />

      {/* Results Count */}
      <div className="flex items-center justify-between">
        <p className="text-gray-400">
          Showing {sortedRentals.length} of {rentals.length} rentals
        </p>
      </div>

      {/* Rental Grid/List */}
      {sortedRentals.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {searchQuery ||
            filters.priceRange.min > 0 ||
            filters.priceRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.durationRange.min > 1 ||
            filters.durationRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.chickenType.length > 0 ||
            filters.rewardDistribution.length > 0 ||
            filters.delegatedTask.length > 0
              ? "No rentals match your search criteria"
              : "No rentals available"}
          </div>
          {(searchQuery ||
            filters.priceRange.min > 0 ||
            filters.priceRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.durationRange.min > 1 ||
            filters.durationRange.max !== Number.MAX_SAFE_INTEGER ||
            filters.chickenType.length > 0 ||
            filters.rewardDistribution.length > 0 ||
            filters.delegatedTask.length > 0) && (
            <Button onPress={resetFilters} appearance="outline">
              Clear Filters
            </Button>
          )}
        </div>
      ) : (
        <div
          className={
            viewMode === "grid"
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              : "space-y-4"
          }
        >
          {sortedRentals.map((rental) => (
            <RentalCard
              key={rental.id}
              rental={rental}
              onRent={onRent}
              isOwner={
                currentUserAddress && rental.ownerAddress
                  ? rental.ownerAddress.toLowerCase() ===
                    currentUserAddress.toLowerCase()
                  : false
              }
              className={viewMode === "list" ? "max-w-none" : ""}
            />
          ))}
        </div>
      )}
    </div>
  );
}
