// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

interface IERC721PresetMinterPauserAutoIdCustomized {
    /**
     * @dev Creates a new token for `to`. Its token ID will be automatically
     * assigned (and available on the emitted {IERC721Upgradeable-Transfer} event), and the token
     * URI autogenerated based on the base URI passed at construction.
     *
     * See {ERC721Upgradeable-_mint}.
     *
     * Requirements:
     *
     * - the caller must have the `MINTER_ROLE`.
     */
    function mint(address to) external returns (uint256 tokenId);

    /**
     * @dev Pauses all token transfers.
     *
     * See {ERC721PausableUpgradeable} and {PausableUpgradeable-_pause}.
     *
     * Requirements:
     *
     * - the caller must have the `PAUSER_ROLE`.
     */
    function pause() external;

    /**
     * @dev Unpauses all token transfers.
     *
     * See {ERC721PausableUpgradeable} and {PausableUpgradeable-_unpause}.
     *
     * Requirements:
     *
     * - the caller must have the `PAUSER_ROLE`.
     */
    function unpause() external;
}
