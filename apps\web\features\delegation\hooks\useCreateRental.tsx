"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address } from "viem";
import { useStateContext } from "@/providers/app/state";
import useBlockchain from "@/lib/hooks/useBlockchain";
import { DelegationAPI } from "../api/delegation.api";
import { useListChickenForRent } from "./useListChickenForRent";
import {
  ICreateRentalFormData,
  ICreateRentalResponse,
} from "../types/delegation.types";

/**
 * Hook for handling rental creation process
 * Integrates with blockchain listing functionality for marketplace rentals
 */
export const useCreateRental = () => {
  const { address } = useStateContext();
  const { blockchainQuery } = useBlockchain();
  const { executeListChickenForRent, isListing } = useListChickenForRent();
  const queryClient = useQueryClient();

  const [isCreating, setIsCreating] = useState(false);

  // Get rental contract address (for future use)
  const rentalAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.rental_address
    : ("" as Address);

  /**
   * Execute the rental creation process
   * - Direct delegations: Create database entry with RENTED status (API only)
   * - Rental listings: Create database entry + blockchain listing transaction
   */
  const executeCreateRental = async (formData: ICreateRentalFormData) => {
    try {
      if (!address) {
        toast.error("Cannot create rental", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return { success: false, error: "Wallet not connected" };
      }

      setIsCreating(true);

      // Handle direct delegation (API only)
      if (formData.isDirectDelegation) {
        toast.info("Creating delegation...", {
          description: "Setting up direct delegation",
          position: "top-center",
        });

        // Call the API to create the delegation
        const response: ICreateRentalResponse =
          await DelegationAPI.createRental(formData);

        if (response.status === 1) {
          toast.success("Delegation created successfully!", {
            description: "Your chicken has been delegated",
            position: "top-center",
          });

          // Invalidate queries for direct delegation
          setTimeout(() => {
            queryClient.invalidateQueries({ queryKey: ["my-rentals"] });
            queryClient.invalidateQueries({ queryKey: ["chickens"] });
            queryClient.invalidateQueries({
              queryKey: ["my-rentals", address],
            });
            queryClient.invalidateQueries({ queryKey: ["chickens", address] });
            queryClient.invalidateQueries({
              queryKey: ["chickenRentalStatuses"],
            });
          }, 500);

          return {
            success: true,
            data: response.data,
            message: response.message,
          };
        } else {
          throw new Error(response.message || "Failed to create delegation");
        }
      } else {
        // Handle marketplace listing (API + Blockchain)
        toast.info("Creating rental listing...", {
          description: "This will require blockchain confirmation",
          position: "top-center",
        });

        // Use the listing hook for blockchain integration
        const result = await executeListChickenForRent(formData);

        if (result?.success) {
          // Success message is already shown by the listing hook
          return {
            success: true,
            data: result.data,
            hash: result.hash,
            receipt: result.receipt,
          };
        } else {
          throw new Error("Failed to list chicken for rent");
        }
      }
    } catch (error) {
      console.error("Create rental failed:", error);

      let errorMessage = "Failed to create rental";
      let errorDescription = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;

        // Handle specific error cases
        if (error.message.includes("Chicken not found")) {
          errorDescription = "The selected chicken was not found";
        } else if (error.message.includes("already rented")) {
          errorDescription = "This chicken is already rented or delegated";
        } else if (error.message.includes("not owned")) {
          errorDescription = "You don't own this chicken";
        } else if (error.message.includes("Validation")) {
          errorDescription = "Please check your input values";
        } else {
          errorDescription = error.message;
        }
      }

      toast.error(errorMessage, {
        description: errorDescription,
        position: "top-center",
      });

      return { success: false, error: errorMessage };
    } finally {
      setIsCreating(false);
    }
  };

  /**
   * Validate form data before submission
   */
  const validateFormData = (formData: ICreateRentalFormData): string[] => {
    const errors: string[] = [];

    if (!formData.chickenTokenId) {
      errors.push("Please select a chicken");
    }

    if (formData.isDirectDelegation) {
      if (!formData.renterAddress) {
        errors.push("Renter address is required for direct delegation");
      } else if (
        !formData.renterAddress.startsWith("0x") ||
        formData.renterAddress.length !== 42
      ) {
        errors.push("Invalid renter address format");
      }
    } else {
      if (!formData.roninPrice || parseFloat(formData.roninPrice) <= 0) {
        errors.push("Valid daily rental rate is required");
      }
    }

    if (!formData.rentalPeriod || formData.rentalPeriod < 3600) {
      errors.push("Rental period must be at least 1 hour");
    }

    return errors;
  };

  // Mutation for React Query integration
  const createRentalMutation = useMutation({
    mutationFn: executeCreateRental,
    onSuccess: (result) => {
      if (result?.success) {
        // Additional success handling if needed
      }
    },
    onError: (error) => {
      console.error("Create rental mutation error:", error);
    },
  });

  return {
    executeCreateRental,
    createRentalMutation,
    validateFormData,
    isCreating: isCreating || isListing, // Include listing state
    rentalAddress,
  };
};
