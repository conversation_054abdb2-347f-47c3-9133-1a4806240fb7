# Marketplace Fees Implementation

## Overview

The marketplace fee system has been implemented to deduct a percentage-based fee from rental earnings. This ensures the platform can sustain operations while providing value to users.

## Configuration

### Fee Percentage

- **Current Rate**: Fetched from rental smart contract
- **Hook**: `useMarketplaceFee` in `apps/web/features/delegation/hooks/useMarketplaceFee.tsx`
- **Fallback Rate**: 2.5% (if contract read fails)
- **Contract Function**: `feePercentage()` in the rental contract

```typescript
// Use the hook to get the current fee percentage
const { feePercentage, isLoading, error, isFromContract } = useMarketplaceFee();

// Fallback configuration
export const MARKETPLACE_CONFIG = {
  FALLBACK_FEE_PERCENTAGE: 2.5,
} as const;
```

## Utility Functions

### `calculateMarketplaceFee(totalAmount: number, feePercentage: number): number`

Calculates the marketplace fee for a given total amount and fee percentage.

**Example:**

```typescript
const { feePercentage } = useMarketplaceFee();
const fee = calculateMarketplaceFee(100, feePercentage); // Returns fee amount based on contract percentage
```

### `calculateEarningsAfterFees(totalAmount: number, feePercentage: number): number`

Calculates the owner's earnings after marketplace fees are deducted.

**Example:**

```typescript
const { feePercentage } = useMarketplaceFee();
const earnings = calculateEarningsAfterFees(100, feePercentage); // Returns earnings after fee deduction
```

### `formatMarketplaceFeePercentage(feePercentage: number): string`

Returns the formatted fee percentage for display.

**Example:**

```typescript
const { feePercentage } = useMarketplaceFee();
const percentage = formatMarketplaceFeePercentage(feePercentage); // Returns "2.5%" or current contract rate
```

### `useMarketplaceFee(): { feePercentage: number, isLoading: boolean, error: any, isFromContract: boolean }`

Hook to fetch the current marketplace fee percentage from the rental contract.

**Example:**

```typescript
const { feePercentage, isLoading, error, isFromContract } = useMarketplaceFee();

if (isLoading) return <div>Loading fee percentage...</div>;
if (error) console.warn('Failed to fetch fee from contract, using fallback');

// Use feePercentage in calculations
const fee = calculateMarketplaceFee(totalAmount, feePercentage);
```

## Implementation Details

### Create Rental Form

**File**: `apps/web/features/delegation/components/create-rental/create-rental.tsx`

**Features:**

- Shows marketplace fee breakdown in the daily rate section
- Displays "Your Earnings" after fee deduction
- Updates estimated earnings calculation to include marketplace fees

**UI Elements:**

- Fee breakdown box showing marketplace fee percentage and amount
- Net earnings display in yellow highlighting the actual amount the owner will receive

### Rental Cards

**File**: `apps/web/features/delegation/components/rental-marketplace/rental-card.tsx`

**Features:**

- Shows marketplace fee information only to the rental owner
- Displays fee amount and net earnings in the price section
- Uses conditional rendering based on current user address

### Estimated Earnings

The `calculateEstimatedEarnings` function now includes:

- `marketplaceFee`: The calculated marketplace fee amount
- `netRentalIncome`: The rental income after fees are deducted

## User Experience

### For Rental Owners (Listers)

1. **During Listing Creation:**

   - See real-time calculation of marketplace fees
   - Understand exactly how much they'll earn after fees
   - Clear breakdown of total income vs. net earnings

2. **In Rental Cards:**
   - Fee information is only visible to the owner
   - Shows both the fee amount and final earnings
   - Color-coded for easy understanding (red for fees, green for earnings)

### For Renters

- Renters pay the full listed amount
- Marketplace fees are transparent but don't affect renter costs
- No additional fees or charges for renters

## Example Calculations

### Scenario 1: Short-term Rental

- Daily Rate: 0.05 RON
- Duration: 7 days
- Total Income: 0.35 RON
- Marketplace Fee (5%): 0.0175 RON
- Owner Earnings: 0.3325 RON

### Scenario 2: Long-term Rental

- Daily Rate: 1 RON
- Duration: 30 days
- Total Income: 30 RON
- Marketplace Fee (5%): 1.5 RON
- Owner Earnings: 28.5 RON

## Future Considerations

### Configurable Fees

The current implementation uses a fixed 5% fee. Future enhancements could include:

- Dynamic fee rates based on rental duration
- Tiered fees based on rental amounts
- Special rates for premium users

### Fee Distribution

Currently, fees are simply deducted. Future implementations might include:

- Fee tracking and reporting
- Revenue sharing mechanisms
- Loyalty programs based on fee contributions

## Maintenance

### Updating Fee Percentage

To change the marketplace fee percentage:

1. Update the fee percentage in the rental smart contract using the `setFeePercentage` function
2. The change will be automatically reflected in the UI through the `useMarketplaceFee` hook
3. Test all affected components
4. Update documentation if needed
5. Communicate changes to users

**Note**: Fee percentage changes now require smart contract interaction and appropriate admin permissions.

### Monitoring

Consider implementing:

- Fee collection tracking
- Revenue analytics
- User impact analysis
