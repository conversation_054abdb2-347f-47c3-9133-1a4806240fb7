import { HardhatRuntimeEnvironment } from "hardhat/types";

const deploy = async ({
  getNamedAccounts,
  deployments,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  
  await deploy("SabongSagaBreedingLogic", {
    contract: "SabongSagaBreedingUpgradeable",
    from: deployer,
    log: true,
  });
};

deploy.tags = ["SabongSagaBreedingLogic"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;