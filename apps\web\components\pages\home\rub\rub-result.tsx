import { Button } from "@/components/ui";
import { memo } from "react";

// Individual delegation distribution for a specific party
export interface IPartyDistribution {
  address: string;
  feathers: number;
  legendaryFeathers: number;
  role: "owner" | "renter";
}

// Overall delegation distribution result
export interface IDelegationDistribution {
  userFeathers: number; // What the current user gets
  userLegendaryFeathers: number;
  userRole: "owner" | "renter"; // Current user's role
  otherParties: IPartyDistribution[]; // All other parties involved
  totalFeathers: number; // Total feathers distributed
  totalLegendaryFeathers: number;
}

type RubResultProps = {
  show: boolean;
  legendaryFeathers: number;
  feathers: number;
  close: () => void;
  delegationDistribution?: IDelegationDistribution | null;
};

// Reusable component for displaying feather items
const FeatherItem = memo(({ type, count }: { type: string; count: number }) => {
  const isLegendary = type === "legendary";
  const imageSrc = isLegendary
    ? "/images/legendary-feathers.png"
    : "/images/feathers.png";
  const altText = isLegendary ? "Legendary Feathers" : "Feathers";

  return (
    <div className="flex flex-col items-center bg-stone-800/70 p-4 rounded-lg border border-primary/20 hover:border-primary/40 transition-all">
      <div className="relative mb-3 p-2">
        <div className="absolute inset-0 bg-primary/10 rounded-full animate-pulse opacity-30"></div>
        <img
          src={imageSrc}
          alt={altText}
          className="w-20 h-20 object-contain relative z-10"
        />
      </div>
      <span className="text-primary font-medium text-center">{altText}</span>
      <span className="text-white/70 text-sm mt-1 bg-stone-700/50 px-2 py-0.5 rounded-full">
        x{count}
      </span>
    </div>
  );
});

// Component for displaying delegation distribution with multiple parties
const MultiPartyDelegationDistribution = memo(
  ({
    type,
    delegationDistribution,
  }: {
    type: string;
    delegationDistribution: IDelegationDistribution;
  }) => {
    const isLegendary = type === "legendary";
    const imageSrc = isLegendary
      ? "/images/legendary-feathers.png"
      : "/images/feathers.png";
    const altText = isLegendary ? "Legendary Feathers" : "Feathers";

    const formatAddress = (address: string) => {
      return `${address.slice(0, 6)}...${address.slice(-4)}`;
    };

    const userFeathers = isLegendary
      ? delegationDistribution.userLegendaryFeathers
      : delegationDistribution.userFeathers;

    return (
      <div className="bg-stone-800/70 p-4 rounded-lg border border-primary/20">
        <div className="flex flex-col items-center mb-4">
          <div className="relative mb-3 p-2">
            <div className="absolute inset-0 bg-primary/10 rounded-full animate-pulse opacity-30"></div>
            <img
              src={imageSrc}
              alt={altText}
              className="w-16 h-16 object-contain relative z-10"
            />
          </div>
          <span className="text-primary font-medium text-center text-sm">
            {altText}
          </span>
        </div>

        {/* User's share (always shown first) */}
        <div className="mb-3 text-center">
          <div className="text-xs text-white/60 mb-1">
            You (
            {delegationDistribution.userRole === "owner" ? "Owner" : "Renter"})
          </div>
          <div className="text-white/70 text-sm bg-stone-700/50 px-2 py-0.5 rounded-full">
            x{userFeathers}
          </div>
        </div>

        {/* Other parties involved (informational only) */}
        {delegationDistribution.otherParties.map((party) => (
          <div key={party.address} className="mb-2 text-center">
            <div className="text-xs text-white/60 mb-1">
              {party.role === "owner" ? "Owner" : "Renter"} (
              {formatAddress(party.address)})
            </div>
            <div className="text-purple-400 text-sm bg-purple-900/30 px-2 py-0.5 rounded-full">
              {delegationDistribution.userRole === "renter"
                ? "Received their share"
                : "Shared according to terms"}
            </div>
          </div>
        ))}
      </div>
    );
  }
);

FeatherItem.displayName = "FeatherItem";
MultiPartyDelegationDistribution.displayName =
  "MultiPartyDelegationDistribution";

const RubResult = memo(
  ({
    show,
    legendaryFeathers,
    feathers,
    close,
    delegationDistribution,
  }: RubResultProps) => {
    if (!show) return null;

    const hasDelegation =
      delegationDistribution && delegationDistribution.otherParties.length > 0;

    // User role is now directly available from the delegation distribution
    const userRole = delegationDistribution?.userRole;

    return (
      <div className="fixed inset-0 flex items-center justify-center bg-black/80 backdrop-blur-md z-50 p-4">
        <div className="bg-gradient-to-b from-stone-800 to-stone-900 rounded-xl shadow-2xl max-w-4xl w-full border border-primary/30 overflow-hidden">
          <div className="bg-primary/10 p-4 flex items-center justify-between">
            <h2 className="text-2xl font-Arcadia text-primary">Rub Results</h2>
            <Button
              intent="danger"
              onPress={() => close()}
              className="w-8 h-8 rounded-full"
            >
              <span>×</span>
            </Button>
          </div>

          <div className="p-6">
            {(feathers > 0 || legendaryFeathers > 0) && (
              <>
                {!hasDelegation ? (
                  // Regular display when no delegation
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
                    {feathers > 0 && (
                      <FeatherItem type="normal" count={feathers} />
                    )}
                    {legendaryFeathers > 0 && (
                      <FeatherItem type="legendary" count={legendaryFeathers} />
                    )}
                  </div>
                ) : (
                  // Delegation distribution display
                  <div className="space-y-6">
                    <div className="text-center">
                      <h3 className="text-lg font-medium text-white mb-2">
                        Feather Distribution
                      </h3>
                      <p className="text-sm text-white/60">
                        {userRole === "renter"
                          ? "You rubbed rented chickens - feathers distributed according to delegation terms"
                          : "Some chickens are delegated - feathers distributed according to delegation terms"}
                      </p>
                      <p className="text-xs text-white/40 mt-1">
                        {delegationDistribution.otherParties.length} other{" "}
                        {delegationDistribution.otherParties.length === 1
                          ? "party"
                          : "parties"}{" "}
                        involved
                      </p>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      {feathers > 0 && (
                        <MultiPartyDelegationDistribution
                          type="normal"
                          delegationDistribution={delegationDistribution}
                        />
                      )}
                      {legendaryFeathers > 0 && (
                        <MultiPartyDelegationDistribution
                          type="legendary"
                          delegationDistribution={delegationDistribution}
                        />
                      )}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex justify-center mb-4">
            <Button
              intent="primary"
              onPress={() => close()}
              className="px-8 py-2 font-medium"
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    );
  }
);

RubResult.displayName = "RubResult";
export { RubResult };
