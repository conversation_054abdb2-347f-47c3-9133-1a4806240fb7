import { Context } from "hono";
import ME from "../models/me";
import { ResponseHelper, sendResponse } from "../utils/response-helper";
import { getYesterdayUTCTimestamp, UTCTimestamp } from "../utils/timestamp-utc";
import { checkNewlyTransfer } from "../utils/new-transfer";
import { MetadataAttributes } from "../types/nfts";
import { Address, getAddress } from "viem";

import Transfer from "../models/event";

import Rub from "../models/rub";
import { getRedisStore } from "../services/redis-store";
import marketplace from "../models/marketplace";
import { getNotListedNfts } from "../services/getNotListedNfts";
import { getRevealMetadata } from "../services/get-reveal-metadata";
import {
  DailyFeathersData,
  DelegatedTaskType,
  RentedNFT,
  RewardDistributionType,
} from "../types";
import LegendaryFeathers from "../models/legendary-feathers";
import { LegendaryFeathersDropRandomizer } from "../services/legendaryFeathersRandomizer";
import { uniqueByTokenId } from "../utils/unique-token-id";
import { getRentedNfts } from "../services/getRentedNfts";

const redisStore = getRedisStore();

const getDailyFeathersValue = (attributes: MetadataAttributes): number => {
  const feathers = attributes.find(
    (attribute) => attribute.trait_type === "Daily Feathers"
  )!;

  if (feathers) {
    return feathers.value as number;
  } else {
    return 0;
  }
};

async function fetchNFTMetadata(nft: {
  id: string;
  address: string;
}): Promise<DailyFeathersData | null> {
  // Changed return type to allow null
  try {
    if (parseInt(nft.id) > 11110) {
      return null;
    }

    let legendaryCount = 0;
    const metadata = await getRevealMetadata({ tokenId: nft.id });

    if (!metadata) {
      throw new Error(`No metadata found for token ${nft.id}`);
    }

    const breeding = metadata.attributes.find(
      (attribute) => attribute.trait_type === "Breeding"
    );

    const state = metadata.attributes.find(
      (attribute) => attribute.trait_type === "State"
    );

    const leg = metadata.attributes.find(
      (attribute) => attribute.trait_type === "Legendary Count"
    );

    // If breeding is undefined or breeding.value is true, return null
    if (breeding && breeding.value === true) {
      return null;
    }

    if (state && state.value === "Faint") {
      return null;
    }
    if (leg) {
      legendaryCount = leg.value as number;
    }

    return {
      tokenId: nft.id,
      image: metadata.image,
      dailyFeathers: getDailyFeathersValue(metadata.attributes),
      legendaryCount,
    };
  } catch (error) {
    return {
      tokenId: nft.id,
    };
  }
}

async function batchGetMetadata(
  nfts: { id: string; address: string }[]
): Promise<DailyFeathersData[]> {
  const metadataPromises = nfts.map(fetchNFTMetadata);
  const results = await Promise.all(metadataPromises);
  // Filter out null values from the results
  return results.filter(
    (result): result is DailyFeathersData => result !== null
  );
}

async function getTransactionEvents(address: Address, epoch: number) {
  const checksumAddress = getAddress(address);
  const yesterdayEpoch = getYesterdayUTCTimestamp();

  const [transfers, transferYes] = await Promise.all([
    Transfer.find({
      epoch,
      "data.eventType": "Transfer",
      "data.to": checksumAddress,
    }),

    Transfer.find({
      epoch: yesterdayEpoch,
      "data.eventType": "Transfer",
      "data.to": checksumAddress,
    }),
  ]);

  return [...transfers, ...transferYes];
}

async function getValidToRub(address: Address): Promise<DailyFeathersData[]> {
  try {
    const yesterdayEpoch = getYesterdayUTCTimestamp();
    const nfts = await getNotListedNfts(address);

    const nftWithMetadata = await batchGetMetadata(nfts);
    const fetchRentedNfts = await getRentedNfts(address);

    // Filter rented NFTs to only include those delegated for daily rub
    const rentedNfts = fetchRentedNfts
      .filter(
        (nft: RentedNFT) =>
          nft.delegatedTask === DelegatedTaskType.DAILY_RUB ||
          nft.delegatedTask === DelegatedTaskType.BOTH
      )
      .map((nft: RentedNFT) => ({
        tokenId: nft.tokenId,
        image: nft.image,
        dailyFeathers: nft.dailyFeathers,
        legendaryCount: nft.legendaryCount,
        renterAddress: nft.renterAddress,
        ownerAddress: nft.ownerAddress,
        delegatedTask: nft.delegatedTask,
        rewardDistribution: nft.rewardDistribution,
        sharedRewardAmount: nft.sharedRewardAmount,
        rubStreakBenefactor: nft.rubStreakBenefactor,
      }));

    nftWithMetadata.push(...rentedNfts);

    const epoch = UTCTimestamp();

    const validTransactions = await getTransactionEvents(address, epoch);
    const newlyTransferred = checkNewlyTransfer(validTransactions, nfts);

    const nftsNotNewlyTransferred = nftWithMetadata.filter(
      (nft) => !newlyTransferred?.includes(nft.tokenId)
    );

    const listedNftsLocal = await marketplace.find({ address, epoch });
    const listedNftsLocalYest = await marketplace.find({
      address,
      epoch: yesterdayEpoch,
    });

    // Today list marketplace
    let finalChickens = nftsNotNewlyTransferred.filter(
      (chick) => !listedNftsLocal.some((c) => c.tokenId === chick.tokenId)
    );

    // Yesterday list marketplace
    finalChickens = finalChickens.filter(
      (chick) => !listedNftsLocalYest.some((c) => c.tokenId === chick.tokenId)
    );

    finalChickens = uniqueByTokenId(finalChickens);

    const rubbedChickens = await Rub.find({ epoch });

    if (rubbedChickens?.length) {
      // Return only the NFTs that haven't been rubbed yet
      return uniqueByTokenId(
        finalChickens.filter(
          (nft) =>
            !rubbedChickens.some((chicken) => chicken.tokenId === nft.tokenId)
        )
      );
    }

    return finalChickens;
  } catch (error) {
    throw error;
  }
}

const handleRubLogic = async (
  address: Address
): Promise<DailyFeathersData[]> => {
  let rubData = await redisStore.get(`ableToRub:${address}`);

  if (!rubData) {
    const validToRub = await getValidToRub(address);
    await redisStore.store(`ableToRub:${address}`, validToRub, 10);
    rubData = validToRub;
  }

  return rubData;
};

export const rub = async (c: Context) => {
  const user = c.get("user");
  const address = user.address;

  try {
    const addrLowercase = address.toLowerCase();
    const lockAcquired = await redisStore.acquireLock(addrLowercase);
    if (!lockAcquired) {
      return sendResponse(
        c,
        ResponseHelper.badRequest("Multiple request detected in short time.")
      );
    }
    const userFound = await ME.findOne({ address: addrLowercase });

    if (!userFound) {
      return sendResponse(c, ResponseHelper.notFound("User not found"));
    }
    let insertedRubchicken: any[] = [];

    // Get the NFTs that are valid for rubbing
    const ableToRubChickens = await handleRubLogic(addrLowercase);

    // Get the current epoch and find the chickens already rubbed
    const epoch = UTCTimestamp();

    const legendaryFeathersRandomizer = new LegendaryFeathersDropRandomizer();

    const ableToRubChickensWithLegFeathers =
      legendaryFeathersRandomizer.calculateDropsForArray(ableToRubChickens);

    if (
      ableToRubChickensWithLegFeathers &&
      ableToRubChickensWithLegFeathers.length != 0
    ) {
      insertedRubchicken = ableToRubChickensWithLegFeathers.map((item) => ({
        address: addrLowercase,
        epoch,
        tokenId: item.tokenId,
        feathers: item.dailyFeathers,
        legendaryFeathers: item.itemsDropped,
      }));

      const ownedChickens = ableToRubChickens.filter(
        (chick) => !chick.renterAddress
      );

      if (ownedChickens.length > 0) {
        let totalFeathers = 0;
        let totalLegendaryFeathers = 0;

        for (const ownedChicken of ownedChickens) {
          const chickenData = insertedRubchicken.find(
            (chick) => chick.tokenId === ownedChicken.tokenId
          );
          if (!chickenData) continue;
          totalFeathers += chickenData.feathers;
          totalLegendaryFeathers += chickenData.legendaryFeathers;
        }
        await ME.findOneAndUpdate(
          { address: addrLowercase }, // Find the user by address
          { $inc: { feathers: totalFeathers } }, // Increment the feathers field by totalFeathers
          { new: true } // Return the updated document
        );

        await LegendaryFeathers.findOneAndUpdate(
          { address: addrLowercase }, // Find the user by address
          { $inc: { legendaryFeathers: totalLegendaryFeathers } }, // Increment the feathers field by totalLegendaryFeathers
          { new: true, upsert: true } // Return the updated document
        );
      }

      const rentedChickens = ableToRubChickens.filter(
        (chick) => chick.renterAddress
      );

      if (rentedChickens.length > 0) {
        let totalFeathersForRenter = 0;
        let totalLegendaryFeathersForRenter = 0;
        let totalFeathersForOwner = 0;
        let totalLegendaryFeathersForOwner = 0;

        for (const rentedChicken of rentedChickens) {
          const rewardDistribution = rentedChicken.rewardDistribution;
          const delegatedTask = rentedChicken.delegatedTask;

          if (
            delegatedTask !== DelegatedTaskType.DAILY_RUB &&
            delegatedTask !== DelegatedTaskType.BOTH
          )
            continue;
          switch (rewardDistribution) {
            case RewardDistributionType.DELEGATOR_ONLY:
              {
                const chickenData = insertedRubchicken.find(
                  (chick) => chick.tokenId === rentedChicken.tokenId
                );
                if (!chickenData) continue;
                totalFeathersForOwner += chickenData.feathers;
                totalLegendaryFeathersForOwner += chickenData.legendaryFeathers;
              }
              break;
            case RewardDistributionType.DELEGATEE_ONLY:
              {
                const chickenData = insertedRubchicken.find(
                  (chick) => chick.tokenId === rentedChicken.tokenId
                );
                if (!chickenData) continue;
                totalFeathersForRenter += chickenData.feathers;
                totalLegendaryFeathersForRenter +=
                  chickenData.legendaryFeathers;
              }
              break;
            case RewardDistributionType.SHARED:
              {
                const chickenData = insertedRubchicken.find(
                  (chick) => chick.tokenId === rentedChicken.tokenId
                );
                if (!chickenData) continue;

                totalFeathersForRenter = chickenData.sharedRewardAmount;
                totalLegendaryFeathersForRenter = 0; // Legendary feathers always go to owner
                totalFeathersForOwner =
                  chickenData.feathers - chickenData.sharedRewardAmount;
                totalLegendaryFeathersForOwner += chickenData.legendaryFeathers;
              }
              break;
          }
        }

        const renterAddress = rentedChickens[0]!.renterAddress;
        const ownerAddress = rentedChickens[0]!.ownerAddress;

        await ME.findOneAndUpdate(
          { address: renterAddress }, // Find the user by address
          { $inc: { feathers: totalFeathersForRenter } }, // Increment the feathers field by totalFeathers
          { new: true } // Return the updated document
        );

        await LegendaryFeathers.findOneAndUpdate(
          { address: renterAddress }, // Find the user by address
          { $inc: { legendaryFeathers: totalLegendaryFeathersForRenter } }, // Increment the feathers field by totalLegendaryFeathers
          { new: true, upsert: true } // Return the updated document
        );

        await ME.findOneAndUpdate(
          { address: ownerAddress }, // Find the user by address
          { $inc: { feathers: totalFeathersForOwner } }, // Increment the feathers field by totalFeathersForOwner
          { new: true } // Return the updated document
        );

        await LegendaryFeathers.findOneAndUpdate(
          { address: ownerAddress }, // Find the user by address
          { $inc: { legendaryFeathers: totalLegendaryFeathersForOwner } }, // Increment the feathers field by totalLegendaryFeathersForOwner
          { new: true, upsert: true } // Return the updated document
        );
      }

      await Rub.insertMany(insertedRubchicken);
    }
    await redisStore.remove(`ableToRub:${addrLowercase}`);
    await redisStore.releaseLock(addrLowercase);

    if (insertedRubchicken && insertedRubchicken.length != 0) {
      const totalFeathers = insertedRubchicken.reduce(
        (sum, chick) => sum + chick.feathers,
        0
      );

      const totalLegFeathers = insertedRubchicken.reduce(
        (sum, chick) => sum + chick.legendaryFeathers,
        0
      );

      const returnedData = {
        totalChickens: insertedRubchicken.length,
        totalFeathers,
        totalLegFeathers,
      };
      return sendResponse(c, ResponseHelper.success(returnedData));
    } else {
      return sendResponse(
        c,
        ResponseHelper.badRequest("No chickens able to rub")
      );
    }
  } catch (error) {
    await redisStore.remove(`ableToRub:${address.toLowerCase()}`);
    const knownError = error as Error;
    return sendResponse(c, ResponseHelper.serverError(knownError.message));
  }
};

export const ableToRub = async (c: Context) => {
  const user = c.get("user");
  const address = user.address;
  const normAddr = address.toLowerCase();
  const timeStampNow = UTCTimestamp();

  const userFound = await ME.findOne({ address: normAddr });
  const alreadyRub = await Rub.find({ address: normAddr, epoch: timeStampNow });

  if (!userFound) {
    return sendResponse(c, ResponseHelper.notFound("User not found"));
  }

  /// If already rub for today
  if (alreadyRub && alreadyRub.length != 0) {
    return sendResponse(c, ResponseHelper.success([]));
  }
  const rubData = await handleRubLogic(address.toLowerCase());

  if (rubData) {
    return sendResponse(c, ResponseHelper.success(rubData));
  } else {
    return sendResponse(
      c,
      ResponseHelper.badRequest("No chickens able to rub")
    );
  }
};
