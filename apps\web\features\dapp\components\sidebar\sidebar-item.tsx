"use client";

import React from "react";
import Link from "next/link";
import { FC } from "react";
import { cn } from "@/utils/classes";

interface ISidebarItemProps {
  children: React.ReactNode;
  href: string;
  isCurrent: boolean;
}

export const SidebarItem: FC<ISidebarItemProps> = ({
  children,
  href = "/dapp",
  isCurrent = false,
}) => {
  return (
    <Link
      href={href}
      className={cn(
        "px-4 flex items-center gap-2 hover:text-white",
        isCurrent ? "text-primary font-semibold" : "text-white/60"
      )}
    >
      {children}
    </Link>
  );
};
