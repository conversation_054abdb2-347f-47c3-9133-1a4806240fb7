import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { initializeContracts } from 'App/Helper/contracts'
import Chicken from 'App/Models/Chicken'
import ClaimNinunoReward, { ClaimNinunoRewardProcessedStatus } from 'App/Models/ClaimNinunoReward'
import NinunoRewardsTransferHistory from 'App/Models/NinunoRewardsTransferHistory'
import ClaimNinunoRewardValidator from 'App/Validators/ClaimNinunoRewardValidator'
import TransferNinunoRewardValidator from 'App/Validators/TransferNinunoRewardValidator'
import SABONG_CONFIG from 'Config/sabong'
import { encodePacked, keccak256 } from 'viem'
import { signMessage } from 'viem/accounts'

export default class NinunoRewardsController {
  public async maintenanceNinunoRewards({ response }: HttpContextContract) {
    response.status(400)
    return response.json({
      status: 0,
      message: 'Ninuno rewards claiming is currently unavailable.',
    })
  }

  public async transferNinunoRewardsToBalance({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { chickenTokenIds } = await request.validate(TransferNinunoRewardValidator)

    const { chickenLegacyContract, chickenGenesisContract } = initializeContracts()

    // VALIDATE OWNERS
    for (const chickenTokenId of chickenTokenIds) {
      const checkChicken = await Chicken.query().where('tokenId', chickenTokenId).first()

      if (!checkChicken) {
        continue
      }

      if (checkChicken.balance === 0n) {
        continue
      }

      const chickenTokenIdOwner =
        Number(chickenTokenId) > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
          ? await chickenLegacyContract.read.ownerOf([BigInt(chickenTokenId)])
          : await chickenGenesisContract.read.ownerOf([BigInt(chickenTokenId)])

      if (auth.user.blockchainAddress.toLowerCase() !== chickenTokenIdOwner.toLowerCase()) {
        response.status(400)
        return response.json({
          status: 0,
          message: `chickenTokenId ${chickenTokenId} is not owned by the owner wallet address`,
        })
      }
    }

    for (const chickenTokenId of chickenTokenIds) {
      const checkChicken = await Chicken.query().where('tokenId', chickenTokenId).first()

      if (!checkChicken) {
        continue
      }

      if (checkChicken.balance === 0n) {
        continue
      }

      auth.user.claimableBalance += checkChicken.balance
      await auth.user.save()

      await NinunoRewardsTransferHistory.create({
        userId: auth.user.id,
        address: auth.user.blockchainAddress,
        chickenTokenId: Number(chickenTokenId),
        amount: checkChicken.balance,
      })

      checkChicken.balance = 0n
      await checkChicken.save()
    }

    return response.json({
      status: 1,
      data: `Successfully transferred chicken's ninuno rewards to user's claimable balance`,
    })
  }

  public async viewTransferNinunoRewardsAll({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { page = 1, pageSize = 10 } = request.qs()

    const ninunoRewardsTransferHistory = await NinunoRewardsTransferHistory.query()
      .where('address', auth.user.blockchainAddress)
      .orderBy('id', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: ninunoRewardsTransferHistory,
    })
  }

  public async viewChickenInfo({ request, response }: HttpContextContract) {
    const { chickenTokenIds } = await request.validate(TransferNinunoRewardValidator)

    const fetchChicken = await Chicken.query().whereIn('tokenId', chickenTokenIds)

    return response.json({
      status: 1,
      data: fetchChicken,
    })
  }

  public async initiateClaimRequest({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { amount } = await request.validate(ClaimNinunoRewardValidator)

    if (BigInt(amount) <= 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid amount',
      })
    }

    if (auth.user.claimableBalance < BigInt(amount)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Insufficient claimable balance',
      })
    }

    const createClaimRequest = await ClaimNinunoReward.create({
      address: auth.user.blockchainAddress,
      amount: BigInt(amount),
      processed: ClaimNinunoRewardProcessedStatus.PENDING,
    })

    const charHash =
      keccak256(
        encodePacked(
          [
            'address', //sender
            'uint256', //withdrawalRequestId
            'uint256', //claimAmount
          ],
          [
            auth.user.blockchainAddress as `0x${string}`,
            BigInt(createClaimRequest.id),
            createClaimRequest.amount,
          ]
        )
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    auth.user.claimableBalance -= createClaimRequest.amount
    await auth.user.save()

    return response.json({
      status: 1,
      data: {
        address: auth.user.blockchainAddress,
        withdrawalRequestId: createClaimRequest.id,
        claimAmount: createClaimRequest.amount,
        signature,
      },
    })
  }

  public async viewClaimRequest({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { page = 1, pageSize = 10, status } = request.qs()

    const claimRequest = ClaimNinunoReward.query()
      .where('address', auth.user.blockchainAddress)
      .where('amount', '>', 0)

    if (status) {
      claimRequest.whereIn('processed', status)
    }

    const finalQuery = await claimRequest.orderBy('id', 'desc').paginate(page, pageSize)

    return response.json({
      status: 1,
      data: finalQuery,
    })
  }

  public async reinitiateClaimRequest({ auth, request, response }: HttpContextContract) {
    if (!auth.user) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized',
      })
    }

    const { withdrawalRequestId } = request.body()

    const findClaimRequest = await ClaimNinunoReward.find(withdrawalRequestId)

    if (!findClaimRequest) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'withdrawalRequestId not found',
      })
    }

    if (findClaimRequest.processed === ClaimNinunoRewardProcessedStatus.PROCESSED) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'withdrawalRequestId already processed',
      })
    }

    if (findClaimRequest.address.toLowerCase() !== auth.user.blockchainAddress.toLowerCase()) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'withdrawalRequestId not owned by user',
      })
    }

    const charHash =
      keccak256(
        encodePacked(
          [
            'address', //sender
            'uint256', //withdrawalRequestId
            'uint256', //claimAmount
          ],
          [
            auth.user.blockchainAddress as `0x${string}`,
            BigInt(findClaimRequest.id),
            findClaimRequest.amount,
          ]
        )
      ) || ''

    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    return response.json({
      status: 1,
      data: {
        address: auth.user.blockchainAddress,
        withdrawalRequestId: findClaimRequest.id,
        claimAmount: findClaimRequest.amount,
        signature,
      },
    })
  }
}
