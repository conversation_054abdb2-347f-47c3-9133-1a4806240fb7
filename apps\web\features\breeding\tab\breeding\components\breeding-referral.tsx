"use client";

import { Input } from "@/components/ui";
import { useBreeding } from "../hooks/useBreeding";

export default function BreedingReferral() {
  const { state } = useBreeding();

  return (
    <div className="flex flex-col gap-2 mb-6 md:mb-4 px-4 md:px-8 max-w-full mx-auto">
      <p className="text-muted-fg text-sm font-medium">
        Do you have a referral code? Enter it here!
      </p>
      <div className="w-full flex flex-col sm:flex-row sm:items-center gap-3">
        <Input
          value={state.savedReferralCode.value}
          onChange={(e) => state.savedReferralCode.set(e.target.value)}
          className="border text-center border-border w-full rounded-md px-3 py-2 bg-[#191C21]/80 focus:ring-1 focus:ring-primary/50 focus:border-primary/50 transition-all duration-200"
          placeholder="Enter referral code (optional)"
        />
      </div>
    </div>
  );
}
