"use client";

import { ThemeProvider } from "@/components/theme-provider";
import { StateProvider } from "@/providers/app/state";
import { waypoint } from "@sky-mavis/tanto-wagmi";
import { QueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { RouterProvider } from "react-aria-components";
import { Toast } from "ui";
import { Config, createConfig, http, WagmiProvider } from "wagmi";
import { ronin, saigon } from "wagmi/chains";
import { injected, metaMask, walletConnect } from "wagmi/connectors";
import ReactQueryProvider from "../lib/react-query";

export const chainId = Number(process.env.NEXT_PUBLIC_CHAINDID);
export const chain = chainId === 2020 ? ronin : saigon;

export const config: Config = createConfig({
  chains: [chainId === 2020 ? ronin : saigon],
  transports: {
    [ronin.id]: http(),
    [saigon.id]: http(),
  },
  connectors: [
    injected(),
    waypoint({
      clientId: String(process.env.NEXT_PUBLIC_WAYPOINT_CLIENTID),
      chainId,
      scopes: ["wallet", "profile", "openid"],
    }),
    walletConnect({
      projectId: String(process.env.NEXT_PUBLIC_REOWN_PROJECT_ID),
      qrModalOptions: {
        enableExplorer: false,
        themeMode: "dark",
      },
    }),
    metaMask(),
  ],
  ssr: false,
});

export const queryClient = new QueryClient();

interface IWeb3ProviderProps {
  children: React.ReactNode;
}

declare module "react-aria-components" {
  interface RouterConfig {
    routerOptions: NonNullable<
      Parameters<ReturnType<typeof useRouter>["push"]>[1]
    >;
  }
}

export default function Web3Provider({ children }: IWeb3ProviderProps) {
  const router = useRouter();

  return (
    <>
      <Toast />
      <WagmiProvider config={config}>
        <ReactQueryProvider>
          <StateProvider>
            <RouterProvider navigate={router.push}>
              <ThemeProvider
                enableSystem
                attribute="class"
                defaultTheme="dark"
                forcedTheme="dark"
              >
                <NuqsAdapter>{children}</NuqsAdapter>
              </ThemeProvider>
            </RouterProvider>
          </StateProvider>
        </ReactQueryProvider>
      </WagmiProvider>
    </>
  );
}
