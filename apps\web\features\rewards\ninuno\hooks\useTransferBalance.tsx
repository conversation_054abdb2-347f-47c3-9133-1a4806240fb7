"use client";

import axios from "@/lib/api";
import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query";
import { ITransferHistoryResponse } from "../types/transfer-history.types";
import { useHookstate } from "@hookstate/core";

// fetch
export const transferBalance = async (chickenIds: string[]) => {
  const { data } = await axios.post("/ninuno-rewards/transfer-balance", {
    chickenTokenIds: chickenIds,
  });
  return data;
};

export const fetchTransferHistory = async (page: number) => {
  const { data } = await axios.get(`/ninuno-rewards/transfer-balance-logs`, {
    params: { page, limit: 10 },
  });
  return data.data as ITransferHistoryResponse;
};

// hooks
export function useTransferBalance() {
  const page = useHookstate(1);

  const transferBalanceMutation = useMutation({
    mutationFn: transferBalance,
  });

  const transferHistoryQuery = useQuery({
    queryKey: ["transferHistory", page.value],
    queryFn: () => fetchTransferHistory(page.value),
    placeholderData: keepPreviousData,
  });

  return { transferBalanceMutation, transferHistoryQuery, page };
}
