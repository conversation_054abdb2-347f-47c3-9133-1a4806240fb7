export interface ITransferHistory {
  id: number;
  user_id: number;
  address: string;
  chicken_token_id: number;
  amount: string;
  created_at: string;
  updated_at: string;
}

export interface IMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  first_page: number;
  first_page_url: string;
  last_page_url: string;
  next_page_url: string | null;
  previous_page_url: string | null;
}

export interface ITransferHistoryResponse {
  meta: IMeta;
  data: ITransferHistory[];
}
