import { HardhatRuntimeEnvironment } from "hardhat/types";
import TransparentUpgradeableProxy from "hardhat-deploy/extendedArtifacts/TransparentUpgradeableProxy.json";
import { SabongSagaBreedingUpgradeable__factory } from "../../typechain-types";
import abi from "./abi.json";

const breedingInterface =
  SabongSagaBreedingUpgradeable__factory.createInterface();

const TREASURY_ADDRESS = "0x250eE5EBebFf033C424604843F28f41A9dD55726";
const SIGNER_ADDRESS = "0x2a56D546DC3d4De82D9a4d0c4bedF65D5E541C08";

const deploy = async ({
  getNamedAccounts,
  deployments,
  network,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();

  // Deploy ProxyAdmin
  // await deploy("SabongSagaBreedingProxyAdmin", {
  //   contract: "ProxyAdmin",
  //   from: deployer,
  //   log: true,
  //   args: [deployer],
  // });

  // Deploy Logic Contract
  await deploy("SabongSagaBreedingLogic", {
    contract: "SabongSagaBreedingUpgradeable",
    from: deployer,
    log: true,
  });

  const proxyAdmin = await deployments.get("SabongSagaBreedingProxyAdmin");
  const logicContract = await deployments.get("SabongSagaBreedingLogic");

  // Get initialization parameters from abi.json
  const initializeData = breedingInterface.encodeFunctionData("initialize", [
    abi.sabong_saga_cock_address, // _cockAddress
    abi.sabong_saga_genesis_address, // _genesisAddress
    abi.sabong_saga_legacy_address, // _legacyAddress
    abi.sabong_saga_items_address, // _feathersAddress
    abi.sabong_saga_resources_address, // _resourcesAddress
    TREASURY_ADDRESS, // _treasury (using deployer as treasury for now)
    SIGNER_ADDRESS, // _signer (using deployer as signer for now)
  ]);

  // Deploy Proxy
  await deploy("SabongSagaBreedingProxy", {
    contract: TransparentUpgradeableProxy,
    from: deployer,
    log: true,
    args: [logicContract.address, proxyAdmin.address, initializeData],
  });
};

deploy.tags = ["SabongSagaBreedingProxy"];
deploy.dependencies = [
  "VerifyContracts",
  "SabongSagaBreedingProxyAdmin",
  "SabongSagaBreedingLogic",
];

export default deploy;
