"use client";

import { useQuery } from "@tanstack/react-query";
import { IEggInfo } from "../types/egg-info.types";
import { isEggInBreedingPhase, isEggInHatchingPhase } from "../utils/egg.utils";

export const useEggPhases = (
  tokenIds: number[],
  eggInfoMap: Record<number, IEggInfo>
) => {
  // Use React Query to filter and cache breeding phase eggs
  const breedingPhaseQuery = useQuery({
    queryKey: [
      "breedingPhaseEggs",
      tokenIds,
      Object.keys(eggInfoMap || {}).length,
    ],
    queryFn: () => {
      if (!tokenIds || !eggInfoMap) {
        return [];
      }
      return tokenIds.filter((egg) => {
        const eggInfo = eggInfoMap[egg];
        return isEggInBreedingPhase(eggInfo);
      });
    },
    enabled: !!tokenIds && !!eggInfoMap,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Use React Query to filter and cache hatching phase eggs
  const hatchingPhaseQuery = useQuery({
    queryKey: [
      "hatchingPhaseEggs",
      tokenIds,
      Object.keys(eggInfoMap || {}).length,
    ],
    queryFn: () => {
      if (!tokenIds || !eggInfoMap) {
        return [];
      }
      return tokenIds.filter((egg) => {
        const eggInfo = eggInfoMap[egg];
        return isEggInHatchingPhase(eggInfo);
      });
    },
    enabled: !!tokenIds && !!eggInfoMap,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    breedingPhaseEggs: breedingPhaseQuery.data || [],
    hatchingPhaseEggs: hatchingPhaseQuery.data || [],
  };
};
