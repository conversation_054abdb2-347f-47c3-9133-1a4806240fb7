"use client";

import { cn } from "@/components/ui";
import Loading from "../loading";
import LoadingDots from "../loading/loading-dots";

interface ILoadingFullProps extends React.HTMLAttributes<HTMLDivElement> {
  classNameSpinner?: string;
  message?: string;
}

export default function LoadingFull({
  classNameSpinner = "",
  message = "Loading Data",
  ...props
}: ILoadingFullProps) {
  return (
    <div className="grid gap-2" {...props}>
      <Loading className={cn("mx-auto", classNameSpinner)} />
      <LoadingDots message={message} dots="." />
    </div>
  );
}
