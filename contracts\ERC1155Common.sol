// SPDX-License-Identifier: MIT
// Compatible with OpenZeppelin Contracts ^5.0.0
pragma solidity ^0.8.20;

import {AccessControlEnumerable} from "@openzeppelin/contracts/access/extensions/AccessControlEnumerable.sol";
import {ERC1155} from "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";

import {ERC1155Burnable} from "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Burnable.sol";
import {ERC1155Pausable} from "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Pausable.sol";
import {ERC1155Supply} from "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Supply.sol";
import {Strings} from "@openzeppelin/contracts/utils/Strings.sol";

/// @custom:security-contact <EMAIL>
contract ERC1155Common is
    ERC1155,
    AccessControlEnumerable,
    ERC1155Pausable,
    ERC1155Burnable,
    ERC1155Supply
{
    using Strings for uint256;

    bytes32 public constant URI_SETTER_ROLE = keccak256("URI_SETTER_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");

    string private _name;
    string private _symbol;

    constructor(
        address admin,
        string memory name,
        string memory symbol,
        string memory uri_
    ) ERC1155(uri_) {
        _grantRole(DEFAULT_ADMIN_ROLE, admin);
        _grantRole(PAUSER_ROLE, admin);
        _grantRole(MINTER_ROLE, admin);
        _grantRole(URI_SETTER_ROLE, admin);

        _name = name;
        _symbol = symbol;
    }

    /**
     * @dev Set the URI for all token types.
     */
    function setURI(string memory newURI) public onlyRole(URI_SETTER_ROLE) {
        _setURI(newURI);
    }

    /**
     * @dev Pauses all token transfers.
     */
    function pause() public onlyRole(PAUSER_ROLE) {
        _pause();
    }

    /**
     * @dev Unpauses all token transfers.
     */
    function unpause() public onlyRole(PAUSER_ROLE) {
        _unpause();
    }

    function mint(
        address account,
        uint256 id,
        uint256 amount,
        bytes memory data
    ) public onlyRole(MINTER_ROLE) {
        _mint(account, id, amount, data);
    }

    /**
     * @dev Mint batch of tokens.
     */
    function mintBatch(
        address to,
        uint256[] memory ids,
        uint256[] memory amounts,
        bytes memory data
    ) public onlyRole(MINTER_ROLE) {
        _mintBatch(to, ids, amounts, data);
    }

    /**
     * @dev Mint tokens to multiple addresses.
     */
    function bulkMint(
        uint256 id,
        address[] calldata tos,
        uint256[] calldata amounts,
        bytes[] calldata datas
    ) external onlyRole(MINTER_ROLE) {
        uint256 length = tos.length;
        require(
            length == amounts.length,
            ERC1155InvalidArrayLength(length, amounts.length)
        );

        for (uint256 i; i < length; ++i) {
            _mint(tos[i], id, amounts[i], datas[i]);
        }
    }

    /**
     * @dev See {ERC1155-uri}.
     */
    function uri(uint256 tokenId) public view override returns (string memory) {
        string memory uri_ = super.uri(tokenId);
        return string.concat(uri_, tokenId.toString());
    }

    /**
     * @dev Collection name.
     */
    function name() public view returns (string memory) {
        return _name;
    }

    /**
     * @dev Collection symbol.
     */
    function symbol() public view returns (string memory) {
        return _symbol;
    }

    /**
     * @dev See {ERC165-supportsInterface}.
     */
    function supportsInterface(
        bytes4 interfaceId
    ) public view override(ERC1155, AccessControlEnumerable) returns (bool) {
        return super.supportsInterface(interfaceId);
    }

    /**
     * @dev See {ERC1155-_update}.
     */
    function _update(
        address from,
        address to,
        uint256[] memory ids,
        uint256[] memory values
    ) internal override(ERC1155, ERC1155Pausable, ERC1155Supply) {
        super._update(from, to, ids, values);
    }
}
