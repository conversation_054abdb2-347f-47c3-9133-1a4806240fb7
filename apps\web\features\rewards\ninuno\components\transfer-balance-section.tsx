"use client";

import React, { useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON>, Card, Modal } from "ui";
import { useTransferBalance } from "../hooks/useTransferBalance";
import { IChickenInfo, IChickenSelection } from "../types/ninuno.types";

interface ITransferBalanceSectionProps {
  className?: string;
  chickens: IChickenInfo[];
  selectedChickens: IChickenSelection;
  onTransferSuccess: () => void;
}

/**
 * TransferBalanceSection Component
 *
 * Handles the transfer of rewards from selected chickens to the claimable balance.
 * MVP version with mock data and simplified functionality.
 */
export const TransferBalanceSection: React.FC<ITransferBalanceSectionProps> = ({
  className,
  chickens,
  selectedChickens,
  onTransferSuccess,
}) => {
  const { transferBalanceMutation } = useTransferBalance();

  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate total selected rewards
  const selectedChickenIds = Object.entries(selectedChickens)
    .filter(([_, isSelected]) => isSelected)
    .map(([tokenId]) => tokenId);

  const totalSelectedRewards = chickens
    .filter((chicken) => selectedChickens[chicken.tokenId])
    .reduce((sum, chicken) => sum + chicken.accumulatedRewards, 0);

  // Check if any chickens are selected
  const hasSelectedChickens = selectedChickenIds.length > 0;

  // Handle transfer button click
  const handleTransferClick = () => {
    if (hasSelectedChickens) {
      setIsConfirmDialogOpen(true);
    }
  };

  // Handle confirm transfer
  const handleConfirmTransfer = async () => {
    setIsLoading(true);

    try {
      await transferBalanceMutation.mutateAsync(selectedChickenIds);

      setIsLoading(false);
      setIsConfirmDialogOpen(false);

      onTransferSuccess();
      toast.success(
        "Successfully transferred rewards to your claimable balance"
      );
    } catch (error: any) {
      console.error("handleConfirmTransfer", error);

      setIsLoading(false);
      toast.error(
        error.response?.data?.message ||
          "Failed to transfer rewards. Please try again later."
      );
    }
  };

  return (
    <>
      <Card className={className}>
        <Card.Header>
          <Card.Title>Transfer to Balance</Card.Title>
          <Card.Description>
            Transfer accumulated rewards from selected chickens to your
            claimable balance
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div className="flex flex-col space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-fg">Selected Chickens:</span>
                <span className="font-medium">{selectedChickenIds.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-fg">Total Rewards:</span>
                <span className="font-medium text-primary">
                  {totalSelectedRewards.toFixed(2)} $COCK
                </span>
              </div>
            </div>

            <Button
              intent="primary"
              className="w-full"
              isDisabled={!hasSelectedChickens || isLoading}
              onPress={handleTransferClick}
            >
              {isLoading ? "Transferring..." : "Transfer to Balance"}
            </Button>

            <p className="text-xs text-muted-fg text-center">
              Transferring rewards will move them from your chickens to your
              claimable balance. Once transferred, you can claim these rewards
              as $COCK tokens.
            </p>
          </div>
        </Card.Content>
      </Card>

      {/* Confirmation Dialog */}
      <Modal isOpen={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <Modal.Content>
          <Modal.Header>
            <Modal.Title>Confirm Transfer</Modal.Title>
            <Modal.Description>
              You are about to transfer rewards from {selectedChickenIds.length}{" "}
              chicken(s) to your claimable balance.
            </Modal.Description>
          </Modal.Header>

          <Modal.Body>
            <div className="py-4">
              <div className="rounded-lg bg-secondary p-4">
                <div className="flex justify-between mb-2">
                  <span className="text-muted-fg">Selected Chickens:</span>
                  <span className="font-medium">
                    {selectedChickenIds.length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-fg">Total Rewards:</span>
                  <span className="font-medium text-primary">
                    {totalSelectedRewards.toFixed(2)} $COCK
                  </span>
                </div>
              </div>

              <p className="mt-4 text-sm text-muted-fg">
                This action cannot be undone. Once transferred, the rewards will
                be available in your claimable balance.
              </p>
            </div>
          </Modal.Body>

          <Modal.Footer>
            <Button
              intent="secondary"
              onPress={() => setIsConfirmDialogOpen(false)}
              isDisabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              intent="primary"
              onPress={handleConfirmTransfer}
              isDisabled={isLoading}
            >
              {isLoading ? "Transferring..." : "Confirm Transfer"}
            </Button>
          </Modal.Footer>
        </Modal.Content>
      </Modal>
    </>
  );
};
