"use client";

import { IChickenMetadata } from "@/lib/types/chicken.types";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { useMemo } from "react";

export const CHICKEN_API = process.env.NEXT_PUBLIC_CHICKEN_API_URL || "";

const fetchChickenMetadata = async (tokenId: number) => {
  const { data } = await axios.get(`${CHICKEN_API}/${tokenId}`);
  return data as IChickenMetadata;
};

const useChickenMetadata = (tokenIds: number[]) => {
  // Memoize the tokenIds to prevent unnecessary re-renders
  const memoizedTokenIds = useMemo(() => {
    if (!tokenIds || tokenIds.length === 0) return [];
    return [...tokenIds].sort((a, b) => a - b); // Sort to ensure consistent array
  }, [tokenIds]);

  const fetchMetadatas = () => {
    if (!CHICKEN_API) {
      throw new Error("Chicken API URL not configured");
    }

    if (!memoizedTokenIds || memoizedTokenIds.length === 0) {
      return [];
    }

    return Promise.all(memoizedTokenIds.map(fetchChickenMetadata));
  };

  const metadataQuery = useQuery({
    queryKey: ["chickenMetadata", memoizedTokenIds],
    queryFn: fetchMetadatas,
    enabled: !!memoizedTokenIds && memoizedTokenIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchInterval: false,
  });

  // create metadata mapping
  const metadataMap = useMemo(() => {
    if (!metadataQuery.data || !memoizedTokenIds) {
      return {};
    }
    return memoizedTokenIds.reduce(
      (map, tokenId, index) => {
        map[tokenId] = metadataQuery.data[index] as IChickenMetadata;
        return map;
      },
      {} as Record<number, IChickenMetadata>
    );
  }, [metadataQuery.data, memoizedTokenIds]);

  return {
    metadataQuery,
    metadataMap,
    isLoading: metadataQuery.isLoading || metadataQuery.isFetching,
    error: metadataQuery.error,
    isError: metadataQuery.isError,
  };
};

export { fetchChickenMetadata };
export default useChickenMetadata;
