export type Chickens = {
  tokenId: number;
  metadata: {
    image: string;
    description: string;
    name: string;
    externalUrl: string;
    totalFeathers: number;
  };
};

export type ChickensData = {
  tokenId: number;
  image: string;
  dailyFeathers: number;
  breedCount: number;
  type?: string; // Optional type field for distinguishing between egg and other types
  metadata?: {
    // Common metadata
    image?: string;
    name?: string;
    description?: string;
    attributes?: Array<{
      trait_type: string;
      value: string | number;
      display_type?: string;
    }>;

    // Egg-specific metadata
    chickenLeftTokenId?: number;
    chickenRightTokenId?: number;
    parentLeft?: number; // Alternative field name
    parentRight?: number; // Alternative field name
    hatchedAt?: string;
    isHatched?: number;
    generation?: number;
  };
};

export interface ChickenStats {
  hp: number;
  maxHp: number;
  level: number;
  attack: number;
  defense: number;
  speed: number;
  hpCooldown?: number; // in minutes
  regenRate?: number; // HP per minute
  state?: "normal" | "faint" | "dead" | "breeding"; // Add state property
  recoverDate?: string; // Add recovery date for faint chickens
  breedingTime?: number;
}
