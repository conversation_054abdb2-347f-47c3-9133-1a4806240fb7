import "dotenv/config";
import { z } from "zod";

const RedisSchema = z.object({
  host: z.string(),
  port: z.number(),
  password: z.string(),
  username: z.string(),
});

const SIWESchema = z.object({
  domain: z.string(),
  uri: z.string(),
  chainId: z.number(),
});

const envSchema = z.object({
  RONIN_RPC: z.string(),
  MONGODB_URI: z.string(),
  MARKETPLACE_CONTRACT: z.string(),
  MARKETPLACE_GRAPHQL_ENDPOINT: z.string(),
  JWT_SECRET: z.string(),
  REFRESH_JWT_SECRET: z.string(),
  SKYMAVIS_API: z.string(),
  CHICKEN_CONTRACT: z.string(),
  LEGACY_CONTRACT: z.string(),
  GAMEITEMS_CONTRACT: z.string(),
  MINTER_PK: z.string(),
  REVEAL_METADATA_API_ENDPOINT: z.string(),
  METADATA_CID: z.string(),
  RPC: z.string(),
  REDIS: z.preprocess(
    (val) => (typeof val === "string" ? JSON.parse(val) : val),
    RedisSchema
  ),
  ENV: z.enum(["production", "development"]).default("development"),
  SIWE: z.preprocess(
    (val) => (typeof val === "string" ? JSON.parse(val) : val),
    SIWESchema
  ),
  SKYNET_API: z.string(),
  IPFS_ENDPOINT: z.string(),
  ORIGIN: z.preprocess(
    (val) => (typeof val === "string" ? JSON.parse(val) : val),
    z.array(z.string())
  ),
  BREEDING_API_URL: z.string(),
});

type Env = z.infer<typeof envSchema>;

export const env: Env = envSchema.parse(process.env);

for (const key in env) {
  if (!(key in env)) {
    throw new Error(
      `Missing env variable: ${key}. Please check the .env file and try again.`
    );
  }
}
