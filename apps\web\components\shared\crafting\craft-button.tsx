"use client";

import { But<PERSON> } from "@/components/ui";
import { Loader } from "@/components/ui";
import { cn } from "@/components/ui";

interface CraftButtonProps {
  isPending: boolean;
  isDisabled: boolean;
  onPress: () => void;
  className?: string;
}

export default function CraftButton({
  isPending,
  isDisabled,
  onPress,
  className,
}: CraftButtonProps) {
  return (
    <Button
      intent="primary"
      isPending={isPending}
      isDisabled={isDisabled}
      className={cn("w-full", className)}
      onPress={onPress}
    >
      {({ isPending }) => (
        <div className="flex items-center justify-center gap-2">
          {isPending ? <Loader variant="spin" /> : <span>🔨</span>}
          {isPending ? "Crafting..." : "Craft Item"}
        </div>
      )}
    </Button>
  );
}
