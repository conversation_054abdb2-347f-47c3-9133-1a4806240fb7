# 🐓 SabongSagaRental Smart Contract

A secure and transparent rental system for Sabong Saga chickens, enabling players to rent chickens using ETH with signature-based authorization and insurance options.

---

## ⚙️ Features

- ✅ **Secure Chicken Rental** with signature-based authorization
- ✅ **ETH Payment System** with configurable fee distribution
- ✅ **Transparent Fee Structure** with percentage-based fees
- ✅ **Rental Tracking** with unique rental IDs
- ✅ **Admin Controls** for pausing, fee adjustments, and emergency functions
- ✅ **Upgradeable Design** using transparent proxy pattern
- ✅ **Event Emissions** for tracking and indexing off-chain
- ✅ **Chicken Listing & Unlisting** for owners to manage their chickens
- ✅ **Insurance System** for Legacy chickens with claim functionality
- ✅ **Revenue Sharing** with configurable percentage-based distribution
- ✅ **Bulk Rental Info Retrieval** for efficient data access

---

## 📜 Contract Details

| Feature                  | Description                                                                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------ |
| **Contract Type**        | Upgradeable (Transparent Proxy Pattern)                                                          |
| **Base Contracts**       | `Initializable`, `AccessControlUpgradeable`, `ReentrancyGuardUpgradeable`, `PausableUpgradeable` |
| **Payment**              | Native ETH                                                                                       |
| **Fee Structure**        | Percentage-based (in basis points, e.g., 450 = 4.5%)                                             |
| **Revenue Sharing**      | Percentage-based (in basis points, e.g., 50 = 0.5%)                                              |
| **Signature Validation** | Ensures rental requests are authorized                                                           |
| **Rental Tracking**      | Unique rental IDs with chicken ID, price, and owner information                                  |
| **NFT Integration**      | Supports Genesis Chickens (IDs 1-2222) and Legacy Chickens (IDs 2223+)                           |
| **Insurance System**     | Optional insurance for Legacy chickens with claim functionality                                  |
| **Bulk Operations**      | Support for retrieving multiple rental information in a single call                              |

---

## 🚀 How Rental Works

### Requirements:

- Unique rental ID (not previously used)
- ETH payment matching the specified price (plus insurance if applicable)
- Valid signature from authorized signer
- Contract must not be paused
- Chicken must be listed for rent

### Process:

1. Chicken owner lists their chicken for rent with a signature
2. Backend generates a signature authorizing the rental with specific parameters
3. User calls `rentChicken()` with the rental details and signature
4. Contract verifies the signature and payment amount
5. Contract distributes the payment between fee wallet, revenue share wallet, and chicken owner
6. Rental information is stored and events are emitted
7. After rental period expires, chicken can be unlisted and returned to owner

```solidity
// 1. List chicken for rent
rental.listChickenForRent(
    chickenId,         // ID of the chicken
    rentId,            // Unique rental ID
    ethPrice,          // Price in ETH
    insurancePrice,    // Insurance price (for Legacy chickens)
    rentDuration,      // Duration in seconds
    listingSignature   // Signature from authorized signer
);

// 2. Rent the chicken
rental.rentChicken(
    {
        rentId: rentId,              // Unique rental ID
        chickenId: chickenId,        // ID of the chicken
        ethPrice: ethPrice,          // Price in ETH
        insurancePrice: insurancePrice, // Insurance price
        renterAddress: renterAddress,   // Address to receive the chicken
        ownerAddress: ownerAddress,     // Address of the chicken owner
        signature: rentSignature        // Signature from authorized signer
    },
    { value: ethPrice + insurancePrice }
);

// 3. Unlist chicken after rental period
rental.unlistChickenForRent(
    rentId,              // Unique rental ID
    chickenId,           // ID of the chicken
    unlistingSignature   // Signature from authorized signer
);

// 4. Claim insurance if applicable
rental.claimInsurance(rentId);

// 5. Get information for multiple rentals
rental.getRentalInfoBulk([rentId1, rentId2, rentId3]);
```

---

## 📋 Contract Functions

### Core Functions

| Function                                                                                                                                                                         | Description                                                                             |
| -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------- |
| `initialize(address _genesisChicken, address _legacyChicken, address _feeWallet, uint256 _feePercentage, address _revShareAddress, uint256 _revSharePercentage, address _admin)` | Initializes the contract with required parameters                                       |
| `listChickenForRent(uint256 _chickenId, uint256 _rentId, uint256 _ethPrice, uint256 _insurancePrice, uint256 _rentDuration, bytes calldata _signature)`                          | Lists a chicken for rent                                                                |
| `rentChicken(RentChickenParams calldata params)`                                                                                                                                 | Processes a chicken rental with signature verification                                  |
| `unlistChickenForRent(uint256 _rentId, uint256 _chickenId, bytes calldata _signature)`                                                                                           | Unlists a chicken from rental                                                           |
| `claimInsurance(uint256 _rentId)`                                                                                                                                                | Claims insurance for a rental (by owner if chicken died, by renter if chicken is alive) |
| `getRentalInfoBulk(uint256[] calldata _rentIds)`                                                                                                                                 | Retrieves information for multiple rentals in a single call                             |

### Admin Functions

| Function                                       | Description                                                |
| ---------------------------------------------- | ---------------------------------------------------------- |
| `pause()`                                      | Pauses the contract (prevents rentals)                     |
| `unpause()`                                    | Unpauses the contract                                      |
| `setFeeWallet(address _newFeeWallet)`          | Updates the fee wallet address                             |
| `setSigner(address _newSigner)`                | Updates the authorized signer address                      |
| `setFeePercentage(uint256 _newFeePercentage)`  | Updates the fee percentage (in basis points)               |
| `setLegacyChicken(address _legacyChicken)`     | Updates the Legacy chicken contract address                |
| `setGenesisChicken(address _genesisChicken)`   | Updates the Genesis chicken contract address               |
| `setTreasuryAddress(address _treasury)`        | Updates the treasury address (alias for fee wallet)        |
| `setRevShareAddress(address _revShareAddress)` | Updates the revenue share address                          |
| `emergencyReleaseETH(address _to)`             | Releases all ETH from the contract in emergency situations |

### Utility Functions

| Function                                           | Description                                  |
| -------------------------------------------------- | -------------------------------------------- |
| `splitSignature(bytes memory sig)`                 | Splits signature into v, r, s components     |
| `recoverSigner(bytes32 message, bytes memory sig)` | Recovers the signer address from a signature |
| `prefixed(bytes32 hash)`                           | Prefixes a hash according to EIP-191         |

---

## 📊 Events

| Event                                                                                              | Description                                   |
| -------------------------------------------------------------------------------------------------- | --------------------------------------------- |
| `ChickenRented(uint256 indexed rentId, address indexed renter, uint256 expiresAt)`                 | Emitted when a chicken is successfully rented |
| `ChickenListedForRent(uint256 indexed rentId, uint256 indexed chickenId, address indexed owner)`   | Emitted when a chicken is listed for rent     |
| `ChickenUnlistedForRent(uint256 indexed rentId, uint256 indexed chickenId, address indexed owner)` | Emitted when a chicken is unlisted from rent  |
| `InsuranceClaimed(uint256 indexed rentId, address indexed claimant, uint256 amount)`               | Emitted when insurance is claimed             |
| `FeePercentageUpdated(uint256 oldPercentage, uint256 newPercentage)`                               | Emitted when the fee percentage is updated    |
| `EmergencyETHRelease(address indexed to, uint256 amount)`                                          | Emitted when ETH is released in an emergency  |

---

## ⚠️ Error Handling

| Error                          | Description                                                              |
| ------------------------------ | ------------------------------------------------------------------------ |
| `ErrInvalidSignature()`        | Thrown when the provided signature is invalid                            |
| `ErrRentIdAlreadyUsed()`       | Thrown when attempting to use a rental ID that has already been used     |
| `ErrInvalidPayment()`          | Thrown when the payment amount doesn't match the specified price         |
| `ErrTransferFailed()`          | Thrown when an ETH transfer fails                                        |
| `ErrInvalidFeePercentage()`    | Thrown when attempting to set a fee percentage greater than 10000 (100%) |
| `ErrChickenNotOwned()`         | Thrown when attempting to list a chicken not owned by the caller         |
| `ErrChickenNotDead()`          | Thrown when owner tries to claim insurance but chicken is still alive    |
| `ErrChickenDied()`             | Thrown when renter tries to claim insurance but chicken has died         |
| `ErrInsuranceAlreadyClaimed()` | Thrown when insurance has already been claimed for a rental              |
| `ErrRentalAlreadyUnlisted()`   | Thrown when attempting to unlist a rental that is already unlisted       |
| `ErrRentDurationNotExpired()`  | Thrown when attempting to unlist a rental before duration expires        |
| `ErrInvalidInsurancePrice()`   | Thrown when insurance price is invalid (e.g., for Genesis chickens)      |
| `ErrInvalidInsuranceClaim()`   | Thrown when an invalid insurance claim is attempted                      |

---

## 🧪 Testing

The contract includes a comprehensive test suite in `tests/sabong-saga-rental-upgradeable.test.js` that covers:

- Initialization and parameter validation
- Chicken listing and unlisting functionality
- Rental functionality with signature verification
- Fee and revenue share distribution
- Insurance claiming by both owner and renter
- Admin functions and access control
- Emergency operations
- Bulk rental information retrieval

### Running Tests

```bash
npx hardhat test tests/sabong-saga-rental-upgradeable.test.js
```

---

## 🚀 Deployment

The contract is deployed using a transparent proxy pattern for upgradeability via the deployment script in `scripts/deploy/sabongsaga_rental.ts`.

### Deployment Process

1. Deploy the implementation contract (`SabongSagaRentalLogic`)
2. Deploy the proxy admin contract (`SabongSagaRentalProxyAdmin`)
3. Encode initialization data with required parameters
4. Deploy the transparent proxy (`SabongSagaRentalProxy`) pointing to the implementation

### Deployment Command

```bash
npx hardhat deploy --network <network_name> --tags SabongSagaRental
```

### Upgrade Process

To upgrade the contract implementation:

```bash
npx hardhat deploy --network <network_name> --tags SabongSagaRentalProxyUpgrade
```

---

## 👥 Roles

| Role                 | Description                                                                                   |
| -------------------- | --------------------------------------------------------------------------------------------- |
| `DEFAULT_ADMIN_ROLE` | Can manage all aspects of the contract, including updating parameters and emergency functions |
| `PAUSER_ROLE`        | Can pause and unpause the contract                                                            |

---

## 🔒 Security Considerations

- The contract uses signature verification to ensure only authorized rentals are processed
- ReentrancyGuard prevents reentrancy attacks during ETH transfers
- Pausable functionality allows freezing the contract in case of emergencies
- Access control ensures only authorized addresses can call admin functions
- The contract is upgradeable, allowing fixes and improvements without losing state
- Insurance claims have validation to ensure only valid claims are processed
- Rental expiration time is tracked to prevent premature unlisting

---

## 📝 Implementation Notes

- Fee percentage is specified in basis points (1/100 of a percent), with 10000 representing 100%
- Revenue share percentage is also specified in basis points
- The contract can receive ETH through the `receive()` function
- Emergency ETH release function provides a safety mechanism to recover funds if needed
- Rental information is stored in a mapping from rental ID to RentalInfo struct
- Genesis chickens (IDs 1-2222) cannot have insurance, only Legacy chickens (IDs 2223+) can
- Insurance can be claimed by the owner if the chicken died, or by the renter if the chicken is alive
- Chicken ownership is transferred to the contract during rental period
- The `getRentalInfoBulk` function allows efficient retrieval of multiple rental details in a single call
