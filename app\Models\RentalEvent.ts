import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export enum RentalEventProcessedStatus {
  PENDING = 0,
  PROCESSED = 1,
  FAILED = 2,
}

export default class RentalEvent extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public address: string

  @column()
  public blockHash: string

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value ? BigInt(value) : value
    },
  })
  public blockNumber: bigint

  @column()
  public data: string

  @column()
  public logIndex: number

  @column()
  public transactionHash: string

  @column()
  public transactionIndex: number

  @column()
  public removed: boolean

  @column({
    prepare: (value) =>
      JSON.stringify(value, (_, value) => {
        if (typeof value === 'bigint') {
          return value.toString()
        } else {
          return value
        }
      }),
    consume: (value: any) => {
      return value && typeof value === 'string' ? JSON.parse(value) : value
    },
  })
  public args: Record<string, any>

  @column()
  public eventName: string

  @column()
  public processed: RentalEventProcessedStatus

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
