"use client";

import { useState } from "react";
import { Tabs } from "ui";
import { Store, Plus, History } from "lucide-react";
import { RentalMarketplace } from "./components/rental-marketplace";
import { CreateRental } from "./components/create-rental";
import { MyRentals } from "./components/my-rentals";

export default function DelegationPage() {
  const [activeTab, setActiveTab] = useState("marketplace");

  const tabs = [
    {
      id: "marketplace",
      label: "Rental Marketplace",
      icon: Store,
      description: "Browse and rent available chickens",
    },
    {
      id: "create",
      label: "Create Listing",
      icon: Plus,
      description: "List your chickens for rent or delegate directly",
    },
    {
      id: "my-rentals",
      label: "Delegation Dashboard",
      icon: History,
      description: "Manage your delegations and rentals",
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">
          Chicken Delegation & Rentals
        </h1>
        <p className="text-gray-400">
          Rent chickens for battles and daily activities, or list your own
          chickens for others to use
        </p>
      </div>

      {/* Tabs */}
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={(key) => setActiveTab(key as string)}
        className="w-full"
      >
        <Tabs.List className="grid w-full grid-cols-3 mb-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <Tabs.Tab
                key={tab.id}
                id={tab.id}
                className="flex flex-col items-center gap-2 p-4 text-center"
              >
                <Icon className="w-5 h-5" />
                <div>
                  <div className="font-medium">{tab.label}</div>
                  <div className="text-xs text-gray-400 hidden sm:block">
                    {tab.description}
                  </div>
                </div>
              </Tabs.Tab>
            );
          })}
        </Tabs.List>

        <Tabs.Panel id="marketplace">
          <RentalMarketplace />
        </Tabs.Panel>

        <Tabs.Panel id="create">
          <CreateRental />
        </Tabs.Panel>

        <Tabs.Panel id="my-rentals">
          <MyRentals />
        </Tabs.Panel>
      </Tabs>
    </div>
  );
}
