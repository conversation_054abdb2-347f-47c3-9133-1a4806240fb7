import { ethers } from "hardhat";
import abi from "./abi.json";
async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);
  const accountBalance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", accountBalance.toString());

  const genesisContract = new ethers.Contract(
    abi.sabong_saga_genesis_address,
    abi.sabong_saga_genesis_chickens,
    deployer
  );

  const legacyContract = new ethers.Contract(
    abi.sabong_saga_legacy_address,
    abi.sabong_saga_legacy_chickens,
    deployer
  );

  const itemsContract = new ethers.Contract(
    abi.sabong_saga_items_address,
    abi.erc1155_common,
    deployer
  );

  const cockContract = new ethers.Contract(
    abi.sabong_saga_cock_address,
    abi.erc20_common,
    deployer
  );

  let tx = await genesisContract.mintPresale(
    "******************************************",
    BigInt(10),
    ethers.toUtf8Bytes("")
  );
  await tx.wait();

  tx = await legacyContract.mintLaunchpad(
    "******************************************",
    BigInt(10),
    ethers.toUtf8Bytes("")
  );
  await tx.wait();

  tx = await itemsContract.bulkMint(
    1,
    ["******************************************"],
    [BigInt(10)],
    [ethers.toUtf8Bytes("")]
  );
  await tx.wait();

  tx = await cockContract.mint(
    "******************************************",
    ethers.parseEther("1000000")
  );

  await tx.wait();
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
