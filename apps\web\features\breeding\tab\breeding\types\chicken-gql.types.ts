export interface IChickenGQL {
  tokenId: string;
  address: string;
  image: string;
  attributes: IAttributes;
}

export interface IAttributes {
  Beak: string[];
  Birthdate: string[];
  Body: string[];
  "Breed Count": string[];
  Color: string[];
  Comb: string[];
  "Daily Feathers": string[];
  Eyes: string[];
  Feet: string[];
  Gender: string[];
  Generation: string[];
  Genes: string[];
  "Grit Attack": string[];
  "Grit Defense": string[];
  "Grit Health": string[];
  "Grit Speed": string[];
  "Innate Attack": string[];
  "Innate Defense": string[];
  "Innate Health": string[];
  "Innate Speed": string[];
  Instinct: string[];
  "Legendary Count": string[];
  Level: string[];
  "Parent 1": string[];
  "Parent 2": string[];
  Tail: string[];
  Type: string[];
  Wings: string[];
}
