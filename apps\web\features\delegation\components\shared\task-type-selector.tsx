"use client";

import { cn } from "ui";
import { Droplets, Swords, Target } from "lucide-react";
import {
  EDelegatedTaskType,
  DELEGATED_TASK_LABELS,
} from "../../types/delegation.types";

interface ITaskTypeSelectorProps {
  value: EDelegatedTaskType;
  onChange: (value: EDelegatedTaskType) => void;
  disabled?: boolean;
  className?: string;
}

export function TaskTypeSelector({
  value,
  onChange,
  disabled = false,
  className,
}: ITaskTypeSelectorProps) {
  const options = [
    {
      value: EDelegatedTaskType.DAILY_RUB,
      label: "Daily Rub",
      description: "Ren<PERSON> can only perform daily rub tasks",
      icon: Droplets,
      isPopular: false,
    },
    {
      value: EDelegatedTaskType.GAMEPLAY,
      label: "Gameplay",
      description: "Ren<PERSON> can only use chicken in battles and games",
      icon: Swords,
      isPopular: false,
    },
    {
      value: EDelegatedTaskType.BOTH,
      label: "Full Access",
      description:
        "Ren<PERSON> can perform all available tasks. Breeding not included",
      icon: Target,
      isPopular: true,
    },
  ];

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-white mb-4">
        Delegated Tasks
      </label>
      <div className="grid grid-cols-1 gap-3">
        {options.map((option) => {
          const Icon = option.icon;
          const isSelected = value === option.value;

          return (
            <div
              key={option.value}
              onClick={() => !disabled && onChange(option.value)}
              className={cn(
                "relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-200",
                disabled && "opacity-50 cursor-not-allowed",
                isSelected
                  ? "border-blue-500 bg-blue-500/10"
                  : "border-stone-600 bg-stone-800 hover:border-stone-500 hover:bg-stone-700",
                !disabled && "hover:scale-[1.02]"
              )}
            >
              <div className="flex items-start gap-3">
                <div
                  className={cn(
                    "p-2 rounded-lg",
                    isSelected
                      ? "bg-blue-500/20 text-blue-400"
                      : "bg-stone-700 text-gray-400"
                  )}
                >
                  <Icon className="w-5 h-5" />
                </div>

                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-medium text-white">{option.label}</h3>
                    {option.isPopular && (
                      <span className="px-2 py-0.5 text-xs font-medium bg-green-500/20 text-green-400 rounded-full">
                        Popular
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-400">{option.description}</p>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
