"use client";

import { Loading } from "@/components/shared/loading";
import NotReady from "@/components/shared/not-ready";
import { CreateRentalModal } from "@/features/delegation/components/create-rental/create-rental-modal";
import { LEGENDARY_NAMES } from "@/data/constants";
import { useChickendOwned } from "@/hooks/useChickendOwned";
import { useStateContext } from "@/providers/app/state";
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Filler,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  RadialLinearScale,
  Tooltip,
} from "chart.js";
import ChartDataLabels from "chartjs-plugin-datalabels";
import {
  Activity,
  Axe,
  ChevronLeft,
  Crosshair,
  Dna,
  Flame,
  Heart,
  LogOut,
  MountainSnow,
  Shield,
  Star,
  Swords,
  Trophy,
  UserCheck,
  Utensils,
  Wind,
  Zap,
  Medal,
  Clock,
  Plus,
} from "lucide-react";
import Image from "next/image";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Bar, Radar } from "react-chartjs-2";
import BattleModal from "./battle-modal";
import { FeedModal } from "./feed-modal";
import useFoodCraftingStore from "@/store/food-crafting";
import useDailyFeedStore from "@/store/daily-feeding";
import HealModal from "./heal-modal";
import { format } from "date-fns";
import {
  useDelegatedChickens,
  IDelegatedChicken,
} from "@/features/delegation/hooks/useDelegatedChickens";
import { EDelegatedTaskType } from "@/features/delegation/types/delegation.types";
import { useChickensForDelegation } from "@/features/delegation/hooks/useChickensForDelegation";
import { useMyRentals } from "@/features/delegation/hooks/useMyRentals";
import { getChickenDelegationStatus } from "./delegation-status-badge";

// Register chart components
ChartJS.register(
  RadialLinearScale,
  LinearScale,
  CategoryScale,
  BarElement,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  ChartDataLabels
);

// Type definitions
type ChickenAttribute = {
  trait_type: string;
  value: string | number;
};

type ChickenData = {
  image: string;
  attributes: ChickenAttribute[];
  edition: number;
  owner?: string;
};

type GameStats = {
  attack: number;
  defense: number;
  speed: number;
  currentHp: number;
  hp: number;
  cockrage: number;
  ferocity: number;
  evasion: number;
  instinct: string;
  level: number;
  wins: number;
  losses: number;
  mmr?: number;
  regenRate?: number;
  state?: "normal" | "faint" | "dead" | "breeding"; // Add state property
  recoverDate?: string; // Add recovery date for faint chickens
  breedingTime?: number;
  boosters?: {
    attack?: number;
    defense?: number;
    speed?: number;
    hp?: number;
    cockrage?: number;
    ferocity?: number;
    evasion?: number;
  };
};

type Points = {
  innatePoints: {
    attack: number | string;
    defense: number | string;
    speed: number | string;
    hp: number | string;
  };
  gritPoints: {
    attack: number | string;
    defense: number | string;
    speed: number | string;
    hp: number | string;
  };
};

interface GeneValue {
  p?: string;
  h1?: string;
  h2?: string;
  h3?: string;
}

interface GeneData {
  [key: string]: GeneValue | string | number;
}

// Updated interfaces for the new match model
interface Participant {
  tokenId: number;
  owner: string;
}

interface ThisToken {
  tokenId: number;
  owner: string;
}

interface BattleLogEntry {
  // Core match data
  result: "win" | "draw";
  participants: Participant[];
  date: string;
  mmrChange?: number;

  // Processed data for this specific user's view
  thisToken: ThisToken;
  opponents: Participant[];
  outcome: "won" | "lost" | "draw" | "participated";
}

// If you still need the old LogData interface for backward compatibility
interface LogData {
  tokenId: number;
  owner: string;
}

// Define the type for the chicken from the API
type ChickenListItem = {
  tokenId: string;
  name: string;
  image: string;
  owner?: string;
  // Add other properties as needed
};

// Custom hook for managing favorites
function useFavoriteChickens() {
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [isLoaded, setIsLoaded] = useState(false);

  // Load favorites from localStorage
  useEffect(() => {
    try {
      const storedFavorites = localStorage.getItem("favoriteChickens");
      if (storedFavorites) {
        const parsedFavorites = JSON.parse(storedFavorites);
        if (Array.isArray(parsedFavorites)) {
          setFavorites(new Set(parsedFavorites));
        }
      }
    } catch (e) {
      console.error("Error loading favorites from localStorage:", e);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  // Save favorites to localStorage whenever they change
  useEffect(() => {
    // Only save after initial load to prevent overwriting with empty set
    if (isLoaded) {
      try {
        localStorage.setItem(
          "favoriteChickens",
          JSON.stringify([...favorites])
        );
      } catch (e) {
        console.error("Error saving favorites to localStorage:", e);
      }
    }
  }, [favorites, isLoaded]);

  // Toggle a chicken's favorite status
  const toggleFavorite = useCallback((tokenId: string) => {
    setFavorites((prev) => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(tokenId)) {
        newFavorites.delete(tokenId);
      } else {
        newFavorites.add(tokenId);
      }
      return newFavorites;
    });
  }, []);

  // Check if a chicken is favorited
  const isFavorite = useCallback(
    (tokenId: string) => favorites.has(tokenId),
    [favorites]
  );

  return {
    favorites,
    toggleFavorite,
    isFavorite,
    isLoaded,
  };
}

export default function ChickenDetails() {
  const params = useParams();
  const router = useRouter();
  const [showFeedModal, setShowFeedModal] = useState(false);
  const chickenId = params.id as string;
  const { address: connectedWallet } = useStateContext();
  const {
    isLoading: loading,
    isFetching,
    data,
    isPending,
    refetch,
  } = useChickendOwned(connectedWallet as string);

  // Use our favorites hook
  const { isFavorite, toggleFavorite } = useFavoriteChickens();

  // Get delegated chickens to check if this chicken is delegated
  const { delegatedChickens } = useDelegatedChickens();

  // Fetch rental data to check if this chicken is rented to me
  const {
    rentedChickens,
    isLoading: rentalsLoading,
    error: rentalsError,
  } = useMyRentals(connectedWallet as string);

  // Fetch owned chickens with rental status to check if this chicken is delegated out
  const {
    chickens: ownedChickensWithStatus,
    isLoading: ownedChickensLoading,
    error: ownedChickensError,
  } = useChickensForDelegation();

  const [chickenData, setChickenData] = useState<ChickenData | null>(null);
  const [gameStats, setGameStats] = useState<GameStats>({
    attack: 0,
    defense: 0,
    speed: 0,
    hp: 0,
    currentHp: 0,
    level: 0,
    cockrage: 0,
    ferocity: 0,
    evasion: 0,
    instinct: "",
    wins: 0,
    losses: 0,
    regenRate: 0,
    state: "normal",
  });
  const { fetchFoodBalance } = useFoodCraftingStore();
  const { checkApproval } = useDailyFeedStore();
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [geneData, setGeneData] = useState<GeneData | null>(null);
  const [points, setPoints] = useState<Points | null>(null);
  const [ownedChickens, setOwnedChickens] = useState<ChickenListItem[]>([]);
  const [activeTab, setActiveTab] = useState<"stats" | "genes" | "battleLogs">(
    "stats"
  );
  const [battleRecord, setBattleRecord] = useState({
    wins: 0,
    losses: 0,
    draws: 0,
  });
  const [isBattleModalOpen, setIsBattleModalOpen] = useState(false);
  const [isNotReadyOpen, setNotReadyOpen] = useState(false);
  const [isDelegateModalOpen, setIsDelegateModalOpen] = useState(false);
  const [battleLogs, setBattleLogs] = useState<BattleLogEntry[]>([]);
  const [cooldownTimer, setCooldownTimer] = useState<number>(0);
  const cooldownIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [healModalOpen, setHealModalOpen] = useState(false);
  const [healInfo, setHealInfo] = useState<{
    healsRemaining: number;
    maxHeals: number;
    resetTime: string;
  } | null>(null);
  const [healInfoLoading, setHealInfoLoading] = useState(false);
  const [breedingTimer, setBreedingTimer] = useState<number>(0);
  const breedingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Recovery timer for faint chickens
  const [recoveryTimer, setRecoveryTimer] = useState<number>(0);
  const recoveryIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Helper functions for chicken state
  const isFaint = () => gameStats.state === "faint";
  const isDead = () => gameStats.state === "dead";
  const isEgg = () =>
    findAttributeByType(chickenData?.attributes || [], "Type") === "Egg";
  const isBreeding = () => gameStats.state === "breeding";

  // Helper function to format recovery time
  const formatRecoveryTime = (seconds: number) => {
    if (seconds <= 0) return "Ready to recover";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  // Helper function to format breeding time
  const formatBreedingTime = (seconds: number) => {
    if (seconds <= 0) return "Breeding complete";

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`;
    } else {
      return `${remainingSeconds}s`;
    }
  };

  const findAttributeByType = (
    attributes: ChickenAttribute[],
    type: string
  ): number | string => {
    const attribute = attributes.find((attr) => attr.trait_type === type);
    if (!attribute) return 0;

    const value = attribute.value;
    return value;
  };

  const isOwner = useMemo(() => {
    return (data?.tokens || []).some(
      (chicken: { tokenId: string }) => chicken.tokenId === chickenId
    );
  }, [data, chickenId]);

  // Check if this chicken is delegated to me and get delegation info
  const delegationInfo = useMemo(() => {
    const delegatedChicken = delegatedChickens.find(
      (chicken) => chicken.tokenId.toString() === chickenId
    );
    return delegatedChicken || null;
  }, [delegatedChickens, chickenId]);

  // Check if this chicken is rented to me and get rental info
  const rentalInfo = useMemo(() => {
    const rentedChicken = rentedChickens.find(
      (rental) =>
        rental.chickenTokenId.toString() === chickenId &&
        rental.roninPrice !== "0"
    );
    return rentedChicken || null;
  }, [rentedChickens, chickenId]);

  // Determine the chicken type based on ownership and delegation/rental status
  const chickenType = useMemo(() => {
    if (delegationInfo) {
      return "delegated-to-me";
    }
    if (rentalInfo) {
      return "rented-to-me";
    }
    if (isOwner) {
      // Check if it's delegated out or listed in market
      const ownedChicken = ownedChickensWithStatus.find(
        (chicken) => chicken.tokenId.toString() === chickenId
      );
      if (ownedChicken && ownedChicken.rentalStatus?.rentalStatus) {
        if (ownedChicken.rentalStatus.rentalStatus === "listed") {
          return "listed-in-market";
        } else if (
          ownedChicken.rentalStatus.rentalStatus === "rented" ||
          ownedChicken.rentalStatus.rentalStatus === "delegated"
        ) {
          return "delegated-out";
        }
      }
      return "owned";
    }
    return "unknown";
  }, [delegationInfo, rentalInfo, isOwner, ownedChickensWithStatus, chickenId]);

  // Check if this chicken is delegated out (owned by user but delegated to someone else)
  const chickenDelegationStatus = useMemo(() => {
    const ownedChicken = ownedChickensWithStatus.find(
      (chicken) => chicken.tokenId.toString() === chickenId
    );

    if (ownedChicken) {
      const status = getChickenDelegationStatus(ownedChicken, connectedWallet);
      console.log(
        `Chicken ${chickenId} delegation status:`,
        status,
        "ownedChicken:",
        ownedChicken
      );
      return status;
    }

    // If not found in owned chickens, check if it's delegated to us
    if (delegationInfo) {
      console.log(`Chicken ${chickenId} is delegated to us:`, delegationInfo);
      return { isDelegated: true, isDelegatedOut: false };
    }

    console.log(`Chicken ${chickenId} has no delegation status`);
    return { isDelegated: false, isDelegatedOut: false };
  }, [ownedChickensWithStatus, chickenId, connectedWallet, delegationInfo]);

  // Determine action button visibility based on chicken type and delegation/rental terms
  const actionPermissions = useMemo(() => {
    if (chickenType === "owned") {
      // Owned chickens - full access, but delegate disabled for faint/dead/breeding chickens
      return {
        canBattle: true,
        canBreed: true,
        canFeed: true,
        canHeal: true,
        canDelegate: !isFaint() && !isDead() && !isBreeding(),
        canRelease: true,
      };
    }

    if (chickenType === "delegated-to-me") {
      // Delegated to me - check delegation terms
      const canBattle =
        delegationInfo?.delegatedTask === EDelegatedTaskType.GAMEPLAY || // GAMEPLAY only
        delegationInfo?.delegatedTask === EDelegatedTaskType.BOTH; // BOTH

      const canHeal =
        delegationInfo?.delegatedTask === EDelegatedTaskType.DAILY_RUB || // DAILY_RUB only
        delegationInfo?.delegatedTask === EDelegatedTaskType.BOTH; // BOTH

      return {
        canBattle,
        canBreed: false, // Breeding not allowed for delegated chickens
        canFeed: true, // Feed always allowed
        canHeal,
        canDelegate: false, // Cannot delegate someone else's chicken
        canRelease: false, // Cannot release someone else's chicken
      };
    }

    if (chickenType === "rented-to-me") {
      // Rented to me - check rental terms
      const canBattle =
        rentalInfo?.delegatedTask === EDelegatedTaskType.GAMEPLAY || // GAMEPLAY only
        rentalInfo?.delegatedTask === EDelegatedTaskType.BOTH; // BOTH

      const canHeal =
        rentalInfo?.delegatedTask === EDelegatedTaskType.DAILY_RUB || // DAILY_RUB only
        rentalInfo?.delegatedTask === EDelegatedTaskType.BOTH; // BOTH

      return {
        canBattle,
        canBreed: false, // Breeding not allowed for rented chickens
        canFeed: true, // Feed always allowed
        canHeal,
        canDelegate: false, // Cannot delegate someone else's chicken
        canRelease: false, // Cannot release someone else's chicken
      };
    }

    if (chickenType === "delegated-out") {
      // Delegated out - all actions disabled
      return {
        canBattle: false,
        canBreed: false,
        canFeed: false,
        canHeal: false,
        canDelegate: false,
        canRelease: false,
      };
    }

    // Unknown type - no actions allowed
    return {
      canBattle: false,
      canBreed: false,
      canFeed: false,
      canHeal: false,
      canDelegate: false,
      canRelease: false,
    };
  }, [chickenType, delegationInfo, rentalInfo]);

  const hasRun = useRef(false);

  const fetchHealInfo = useCallback(async () => {
    if (!connectedWallet) return;

    setHealInfoLoading(true);
    try {
      const response = await fetch(
        `/api/proxy/heal?address=${connectedWallet}`
      );

      if (!response.ok) {
        throw new Error(`API responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        // Format the reset time
        const resetDate = new Date(Date.now() + data.resetIn.milliseconds);
        const formattedResetTime = format(resetDate, "HH:mm:ss 'UTC'");

        setHealInfo({
          healsRemaining: data.healsRemaining,
          maxHeals: data.maxHeals,
          resetTime: formattedResetTime,
        });
      } else {
        console.log(data);

        console.error("Error fetching heal info:", data.error);
        setHealInfo(null);
      }
    } catch (error) {
      console.error("Error fetching heal info:", error);
      setHealInfo(null);
    } finally {
      setHealInfoLoading(false);
    }
  }, [connectedWallet]);

  const handleHealChicken = useCallback(async () => {
    if (!connectedWallet || !chickenId) return false;

    try {
      const res = await fetch("/csrf-token");
      if (!res.ok) {
        throw new Error(`API call failed: ${res.statusText}`);
      }
      const { csrfToken } = await res.json();

      const response = await fetch("/api/proxy/heal", {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: parseInt(chickenId, 10),
          address: connectedWallet,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `API responded with status: ${response.status}`
        );
      }

      const data = await response.json();

      if (data.success) {
        // Update chicken stats with the healed data
        setGameStats((prev) => ({
          ...prev,
          currentHp: data.chicken.stats.currentHp,
          hp: data.chicken.stats.hp,
        }));

        // Update heal info
        setHealInfo((prev) =>
          prev
            ? {
                ...prev,
                healsRemaining: data.healsRemaining,
              }
            : null
        );

        // Reset cooldown timer if it was active
        setCooldownTimer(0);

        return true;
      } else {
        console.error("Healing failed:", data.error);
        return false;
      }
    } catch (error) {
      console.error("Error healing chicken:", error);
      throw error;
    }
  }, [connectedWallet, chickenId]);

  const openHealModal = useCallback(() => {
    fetchHealInfo();
    setHealModalOpen(true);
  }, [fetchHealInfo]);

  // Add this effect to fetch heal info when the component mounts
  useEffect(() => {
    if (connectedWallet && isOwner) {
      fetchHealInfo();
    }
  }, [connectedWallet, isOwner, fetchHealInfo]);

  useEffect(() => {
    if (hasRun.current) return;
    hasRun.current = true;

    checkApproval();
    fetchFoodBalance();
  }, []);
  // Fetch chicken data and stats
  async function fetchChickenData(refetch: boolean = false) {
    if (!chickenId) return;

    try {
      if (!refetch) {
        setIsLoading(true);
      }

      setError("");

      // Fetch chicken metadata
      const response = await fetch(`/api/proxy?tokenId=${chickenId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch chicken data: ${response.status}`);
      }
      const data = await response.json();

      const _innatePoints = {
        attack: findAttributeByType(data.attributes, "Innate Attack"),
        defense: findAttributeByType(data.attributes, "Innate Defense"),
        speed: findAttributeByType(data.attributes, "Innate Speed"),
        hp: findAttributeByType(data.attributes, "Innate Health"),
      };

      const _gritPoints = {
        attack: findAttributeByType(data.attributes, "Grit Attack"),
        defense: findAttributeByType(data.attributes, "Grit Defense"),
        speed: findAttributeByType(data.attributes, "Grit Speed"),
        hp: findAttributeByType(data.attributes, "Grit Health"),
      };

      setChickenData(data);
      setPoints({ innatePoints: _innatePoints, gritPoints: _gritPoints });

      // Fetch game stats
      const statsResponse = await fetch(`/api/proxy/game?tokenId=${chickenId}`);
      if (!statsResponse.ok) {
        throw new Error(`Failed to fetch game stats: ${statsResponse.status}`);
      }
      const statsData = await statsResponse.json();

      // Set the cooldown timer if it exists
      if (statsData.hpCooldown && statsData.hpCooldown > 0) {
        setCooldownTimer(Math.round(statsData.hpCooldown * 60)); // Convert minutes to seconds
      }

      // Set recovery timer if chicken is faint and has recovery date
      if (statsData.state === "faint" && statsData.recoverDate) {
        const now = new Date();
        const recoverDate = new Date(statsData.recoverDate);
        const timeRemaining = recoverDate.getTime() - now.getTime();

        if (timeRemaining > 0) {
          setRecoveryTimer(Math.ceil(timeRemaining / 1000));
        }
      }

      setGameStats({
        attack: statsData.stats?.attack || 0,
        defense: statsData.stats?.defense || 0,
        speed: statsData.stats?.speed || 0,
        currentHp: statsData.stats?.currentHp || 100,
        hp: statsData.stats?.hp || 100,
        cockrage: statsData.stats?.cockrage || 0,
        ferocity: statsData.stats?.ferocity || 0,
        evasion: statsData.stats?.evasion || 0,
        instinct: statsData.stats?.instinct || "",
        level: statsData.level || 1,
        wins: statsData.wins || 0,
        losses: statsData.losses || 0,
        regenRate: statsData.regenRate || 1, // Add regeneration rate
        boosters: statsData.boosters || {},
        mmr: statsData.mmr || 0,
        state: statsData.state || "normal", // Add state from API
        recoverDate: statsData.recoverDate, // Add recovery date from API
      });

      // Fetch gene data
      fetchGeneData(chickenId);
      // Fetch battle logs
      fetchBattleLogs(chickenId);

      setBattleRecord({
        wins: statsData.wins,
        losses: statsData.losses,
        draws: statsData.draws,
      });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      setChickenData(null);
      setGameStats({
        attack: 0,
        defense: 0,
        speed: 0,
        hp: 0,
        currentHp: 0,
        level: 0,
        cockrage: 0,
        ferocity: 0,
        evasion: 0,
        instinct: "",
        wins: 0,
        losses: 0,
        regenRate: 1,
        boosters: {},
        mmr: 0,
        state: "normal",
      });
    } finally {
      if (!refetch) {
        setIsLoading(false);
      }
    }
  }

  useEffect(() => {
    fetchChickenData();
  }, [chickenId]);

  // Recovery timer effect for faint chickens
  useEffect(() => {
    if (recoveryTimer <= 0) return;

    recoveryIntervalRef.current = setInterval(() => {
      setRecoveryTimer((prev) => {
        if (prev <= 0) {
          // Recovery completed, update chicken state to normal
          setGameStats((prevStats) => ({
            ...prevStats,
            state: "normal",
            recoverDate: undefined,
          }));
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (recoveryIntervalRef.current) {
        clearInterval(recoveryIntervalRef.current);
      }
    };
  }, [recoveryTimer]);

  // Add this useEffect after the recovery timer effect
  // Breeding timer effect for breeding chickens
  useEffect(() => {
    if (breedingTimer <= 0) return;

    breedingIntervalRef.current = setInterval(() => {
      setBreedingTimer((prev) => {
        if (prev <= 0) {
          // Breeding completed, update chicken state to normal
          setGameStats((prevStats) => ({
            ...prevStats,
            state: "normal",
            breedingTime: undefined,
          }));
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => {
      if (breedingIntervalRef.current) {
        clearInterval(breedingIntervalRef.current);
      }
    };
  }, [breedingTimer]);

  useEffect(() => {
    if (cooldownTimer <= 0) return;

    cooldownIntervalRef.current = setInterval(() => {
      setCooldownTimer((prev) => {
        if (prev <= 0) return 0;

        const newValue = prev - 1;

        // Regenerate HP every minute (60 seconds)
        if (newValue > 0 && newValue % 60 === 0) {
          setGameStats((prevStats) => {
            if (prevStats.currentHp < prevStats.hp) {
              const newHp = Math.min(
                prevStats.currentHp + (prevStats.regenRate || 1),
                prevStats.hp
              );

              return {
                ...prevStats,
                currentHp: newHp,
              };
            }
            return prevStats;
          });
        }

        return newValue;
      });
    }, 1000);

    return () => {
      if (cooldownIntervalRef.current) {
        clearInterval(cooldownIntervalRef.current);
      }
    };
  }, [cooldownTimer]);

  // Format cooldown time
  const formatCooldownTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    if (hours > 0) {
      return `${hours}h:${minutes.toString().padStart(2, "0")}m:${remainingSeconds.toString().padStart(2, "0")}s`;
    } else {
      return `${minutes}m:${remainingSeconds.toString().padStart(2, "0")}s`;
    }
  };

  const fetchGeneData = async (tokenId: string) => {
    try {
      const response = await fetch(`/api/proxy/genes?tokenId=${tokenId}`);
      const data = await response.json();
      if (data.success) {
        setGeneData(data.decodedGene as GeneData);
      } else {
        setGeneData(null);
      }
    } catch (err) {
      console.error("Error fetching gene data:", err);
      setGeneData(null);
    }
  };

  const fetchBattleLogs = async (tokenId: string) => {
    try {
      const response = await fetch(
        `/api/proxy/game/matches?tokenId=${tokenId}`
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch battle logs: ${response.status}`);
      }
      const logsData = await response.json();
      setBattleLogs(logsData.matches);
    } catch (err) {
      console.error("Error fetching battle logs:", err);
      setBattleLogs([]);
    }
  };

  const isLegendary = (value: string): boolean => {
    return LEGENDARY_NAMES.includes(value);
  };

  const chartData = {
    labels: ["Attack", "Defense", "Speed", "Cockrage", "Ferocity", "Evasion"],
    datasets: [
      {
        label: "Value",
        data: [
          gameStats.attack,
          gameStats.defense,
          gameStats.speed,
          gameStats.cockrage,
          gameStats.ferocity,
          gameStats.evasion,
        ],
        backgroundColor: [
          "rgba(255, 99, 132, 0.7)",
          "rgba(54, 162, 235, 0.7)",
          "rgba(255, 206, 86, 0.7)",
          "rgba(75, 192, 192, 0.7)",
          "rgba(153, 102, 255, 0.7)",
          "rgba(255, 159, 64, 0.7)",
        ],
        borderColor: [
          "rgba(255, 99, 132, 1)",
          "rgba(54, 162, 235, 1)",
          "rgba(255, 206, 86, 1)",
          "rgba(75, 192, 192, 1)",
          "rgba(153, 102, 255, 1)",
          "rgba(255, 159, 64, 1)",
        ],
        borderWidth: 2,
        borderRadius: 5,
      },
    ],
  };

  const radarChartData = points
    ? {
        labels: ["Attack", "Defense", "Speed", "HP"],
        datasets: [
          {
            label: "Innate Points",
            data: [
              points.innatePoints.attack,
              points.innatePoints.defense,
              points.innatePoints.speed,
              points.innatePoints.hp,
            ],
            backgroundColor: "rgba(54, 162, 235, 0.2)",
            borderColor: "rgb(54, 162, 235)",
            pointBackgroundColor: "rgb(54, 162, 235)",
            pointBorderColor: "#fff",
            pointHoverBackgroundColor: "#fff",
            pointHoverBorderColor: "rgb(54, 162, 235)",
          },
          {
            label: "Grit Points",
            data: [
              points.gritPoints.attack,
              points.gritPoints.defense,
              points.gritPoints.speed,
              points.gritPoints.hp,
            ],
            backgroundColor: "rgba(255, 99, 132, 0.2)",
            borderColor: "rgb(255, 99, 132)",
            pointBackgroundColor: "rgb(255, 99, 132)",
            pointBorderColor: "#fff",
            pointHoverBackgroundColor: "#fff",
            pointHoverBorderColor: "rgb(255, 99, 132)",
          },
        ],
      }
    : null;

  const getBodyPartIcon = (part: string) => {
    switch (part) {
      case "Beak":
        return <span className="text-xl">🐧</span>;
      case "Comb":
        return <span className="text-xl">🐔</span>;
      case "Eyes":
        return <span className="text-xl">👁️</span>;
      case "Feet":
        return <span className="text-xl">🦶</span>;
      case "Tail":
        return <span className="text-xl">🦚</span>;
      case "Body":
        return <span className="text-xl">🐓</span>;
      case "Wings":
        return <span className="text-xl">🪶</span>;
      case "Color":
        return <span className="text-xl">🎨</span>;
      default:
        return <span className="text-xl">❓</span>;
    }
  };

  // Helper function to safely render gene data values
  const renderGeneDataValue = (key: string): string => {
    if (!geneData) return "N/A";

    const value = geneData[key];

    if (typeof value === "string") return value;
    if (typeof value === "number") return value.toString();

    return "N/A";
  };

  // Calculate HP percentage for the HP bar
  const hpPercentage =
    gameStats.hp > 0 ? (gameStats.currentHp / gameStats.hp) * 100 : 100;

  // Get HP bar color based on percentage
  const getHpColor = (percentage: number) => {
    if (percentage > 70) return "bg-green-500";
    if (percentage > 30) return "bg-yellow-500";
    return "bg-red-500";
  };

  const isUserChicken = (tokenId: number) => {
    return tokenId === parseInt(chickenId);
  };

  const truncateAddress = (address: string) => {
    if (!address) return "";
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  // Calculate win rate percentage
  const totalBattles =
    battleRecord.wins + battleRecord.losses + battleRecord.draws;
  const winRate =
    totalBattles > 0 ? Math.round((battleRecord.wins / totalBattles) * 100) : 0;

  // Action handlers
  const handleBattle = () => {
    setIsBattleModalOpen(true);
  };

  const handleBreeding = () => {
    // console.log("Breeding action for chicken", chickenId);
    router.push(`/breeding?parent1=${chickenId}`);
  };

  const handleFeed = () => {
    // console.log("Feed action for chicken", chickenId);
    setShowFeedModal(true);
  };

  const handleDelegate = () => {
    // console.log("Delegate action for chicken", chickenId);
    setIsDelegateModalOpen(true);
  };

  const handleRelease = () => {
    // console.log("Release action for chicken", chickenId);
    setNotReadyOpen(true);
  };

  const handleBackToInventory = () => {
    router.push("/inventory/chickens");
  };

  // Handle favorite toggle
  const handleToggleFavorite = () => {
    toggleFavorite(chickenId);
  };

  if (isLoading) return <Loading />;
  if (error) return <p className="text-red-500 text-center">{error}</p>;
  console.log(chickenData);
  return (
    <>
      <div className="mb-4">
        <button
          onClick={handleBackToInventory}
          className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
        >
          <ChevronLeft size={20} />
          <span>Back to Inventory</span>
        </button>
      </div>

      <div className="relative mb-8">
        {/* Background banner - could be customized based on chicken type and legendary */}
        <div className="h-52 bg-gradient-to-r from-yellow-900 via-amber-800 to-yellow-900 rounded-t-lg relative overflow-hidden">
          <div className="absolute inset-0 bg-[url('/images/feather-pattern.png')] opacity-10"></div>
          {isOwner && (
            <div className="absolute top-4 right-4 bg-green-600 text-white px-3 py-1 rounded-full text-sm font-bold flex items-center">
              <span className="mr-1">●</span> Owned
            </div>
          )}
          {isOwner && isFavorite(chickenId) && (
            <div className="absolute top-4 left-4 bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold flex items-center">
              <Heart className="w-4 h-4 mr-1 fill-black" /> Favorite
            </div>
          )}
        </div>

        <div className="flex flex-col md:flex-row items-center md:items-end px-6 -mt-16 relative z-10">
          <div className="w-32 h-32 rounded-xl border-4 border-gray-800 overflow-hidden bg-gray-700 shadow-lg">
            {chickenData && (
              <Image
                src={chickenData.image}
                alt={`Chicken #${chickenId}`}
                width={128}
                height={128}
                className={`object-cover transition-all duration-300 ${
                  isFaint() || isDead() ? "grayscale" : ""
                }`}
              />
            )}

            {/* State overlays */}
            {isFaint() && (
              <div className="absolute inset-0 bg-black bg-opacity-60 rounded-xl flex flex-col items-center justify-center">
                <div className="text-red-400 font-bold text-sm mb-1">FAINT</div>
                <div className="text-white text-xs text-center">
                  {recoveryTimer > 0
                    ? `Recovers in ${formatRecoveryTime(recoveryTimer)}`
                    : "Ready to recover!"}
                </div>
              </div>
            )}

            {isDead() && (
              <div className="absolute inset-0 bg-black bg-opacity-80 rounded-xl flex flex-col items-center justify-center">
                <div className="text-red-600 font-bold text-sm mb-1">DEAD</div>
                <div className="text-gray-400 text-xs text-center">
                  Permanently defeated
                </div>
              </div>
            )}
          </div>

          <div className="md:ml-6 mt-4 md:mt-0 text-center md:text-left flex-1">
            <h1 className="text-3xl font-bold text-yellow-400 font-Arcadia">
              Chicken #{chickenId}
              {isFaint() && (
                <span className="ml-2 text-red-400 text-lg">[FAINT]</span>
              )}
              {isDead() && (
                <span className="ml-2 text-red-600 text-lg">[DEAD]</span>
              )}
              {isBreeding() && (
                <span className="ml-2 text-pink-400 text-lg">[BREEDING]</span>
              )}
            </h1>
            {chickenData?.attributes &&
              findAttributeByType(chickenData?.attributes, "Type") !==
                "Egg" && (
                <div className="flex flex-wrap items-center justify-center md:justify-start gap-4 mt-2">
                  <div className="bg-gray-800 px-3 py-1 rounded-full flex items-center">
                    <Star className="text-yellow-400 w-4 h-4 mr-1" />
                    <span className="text-white font-medium">
                      Level {gameStats.level}
                    </span>
                  </div>
                  <div className="bg-gray-800 px-3 py-1 rounded-full flex items-center">
                    <Trophy className="text-yellow-400 w-4 h-4 mr-1" />
                    <span className="text-white font-medium">
                      {winRate}% Win Rate
                    </span>
                  </div>
                  <div className="bg-gray-800 px-3 py-1 rounded-full flex items-center">
                    <Medal className="text-yellow-400 w-4 h-4 mr-1" />
                    <span className="text-white font-medium">
                      MMR {gameStats.mmr}
                    </span>
                  </div>
                  {/* {!isFaint() && !isDead() && (
                  <div className="bg-gray-800 px-3 py-1 rounded-full flex items-center">
                    <Activity className="text-green-400 w-4 h-4 mr-1" />
                    <span className="text-white font-medium">
                      {gameStats.currentHp.toFixed(0)}/{gameStats.hp.toFixed(0)}{" "}
                      HP
                    </span>
                  </div>
                )}
                {isFaint() && recoveryTimer > 0 && (
                  <div className="bg-orange-600 px-3 py-1 rounded-full flex items-center">
                    <Clock className="text-white w-4 h-4 mr-1" />
                    <span className="text-white font-medium">
                      Recovers in {formatRecoveryTime(recoveryTimer)}
                    </span>
                  </div>
                )}
                {isBreeding() && breedingTimer > 0 && (
                  <div className="bg-pink-600 px-3 py-1 rounded-full flex items-center">
                    <Clock className="text-white w-4 h-4 mr-1" />
                    <span className="text-white font-medium">
                      Breeding: {formatBreedingTime(breedingTimer)}
                    </span>
                  </div>
                )} */}

                  {/* Chicken Type Badge */}
                  {chickenType !== "owned" && (
                    <div
                      className={`px-3 py-1 rounded-full flex items-center ${
                        chickenType === "delegated-to-me"
                          ? "bg-purple-600/80 border border-purple-400/60"
                          : chickenType === "rented-to-me"
                            ? "bg-orange-600/80 border border-orange-400/60"
                            : chickenType === "listed-in-market"
                              ? "bg-green-600/80 border border-green-400/60"
                              : chickenType === "delegated-out"
                                ? "bg-blue-600/80 border border-blue-400/60"
                                : "bg-gray-600/80 border border-gray-400/60"
                      }`}
                    >
                      <UserCheck className="w-4 h-4 mr-1" />
                      <span className="text-white font-medium text-sm">
                        {chickenType === "delegated-to-me"
                          ? "Delegated to me"
                          : chickenType === "rented-to-me"
                            ? "Rented to me"
                            : chickenType === "listed-in-market"
                              ? "Listed in Market"
                              : chickenType === "delegated-out"
                                ? "Delegated out"
                                : "Unknown"}
                      </span>
                    </div>
                  )}
                </div>
              )}
          </div>
          {chickenData?.attributes &&
            findAttributeByType(chickenData?.attributes, "Type") !== "Egg" && (
              <>
                <div className="mt-4 md:mt-0 flex flex-wrap justify-center gap-2">
                  {/* Show action buttons based on chicken type and permissions */}
                  {(chickenType === "owned" ||
                    chickenType === "delegated-to-me" ||
                    chickenType === "rented-to-me") && (
                    <>
                      {/* Battle Button */}
                      {actionPermissions.canBattle && (
                        <button
                          onClick={handleBattle}
                          disabled={
                            gameStats.currentHp < 50 ||
                            isFaint() ||
                            isDead() ||
                            isBreeding()
                          }
                          className={`flex items-center gap-1 px-3 py-2 rounded-lg transition-colors ${
                            gameStats.currentHp >= 50 &&
                            !isFaint() &&
                            !isDead() &&
                            !isBreeding()
                              ? "bg-red-600 hover:bg-red-700 text-white"
                              : "bg-stone-600 text-stone-400 cursor-not-allowed"
                          }`}
                          title={
                            isFaint()
                              ? "Cannot battle - chicken is faint"
                              : isDead()
                                ? "Cannot battle - chicken is dead"
                                : isBreeding()
                                  ? "Cannot battle - chicken is breeding"
                                  : gameStats.currentHp < 50
                                    ? "Cannot battle - HP too low (minimum 50)"
                                    : "Battle"
                          }
                        >
                          <Swords size={16} />
                          Battle
                        </button>
                      )}

                      {/* Breed Button - Only show if not delegated */}
                      {actionPermissions.canBreed && (
                        <button
                          onClick={handleBreeding}
                          disabled={isFaint() || isDead() || isBreeding()}
                          className={`flex items-center gap-1 px-3 py-2 rounded-lg transition-colors ${
                            !isFaint() && !isDead() && !isBreeding()
                              ? "bg-pink-600 hover:bg-pink-700 text-white"
                              : "bg-stone-600 text-stone-400 cursor-not-allowed"
                          }`}
                          title={
                            isFaint()
                              ? "Cannot breed - chicken is faint"
                              : isDead()
                                ? "Cannot breed - chicken is dead"
                                : isBreeding()
                                  ? "Cannot breed - chicken is already breeding"
                                  : "Breed"
                          }
                        >
                          <MountainSnow size={16} />
                          Breed
                        </button>
                      )}

                      {/* Favorite Button - Only for owned chickens */}
                      {chickenType === "owned" && (
                        <button
                          onClick={handleToggleFavorite}
                          className={`flex items-center gap-1 ${
                            isFavorite(chickenId)
                              ? "bg-yellow-500 text-black"
                              : "bg-yellow-600 hover:bg-yellow-700 text-white"
                          } px-3 py-2 rounded-lg transition-colors`}
                        >
                          <Heart
                            size={16}
                            className={
                              isFavorite(chickenId) ? "fill-black" : ""
                            }
                          />
                          {isFavorite(chickenId) ? "Favorited" : "Favorite"}
                        </button>
                      )}

                      {/* Feed Button */}
                      {actionPermissions.canFeed && (
                        <button
                          onClick={handleFeed}
                          disabled={isFaint() || isDead() || isBreeding()}
                          className={`flex items-center gap-1 px-3 py-2 rounded-lg transition-colors ${
                            !isFaint() && !isDead() && !isBreeding()
                              ? "bg-blue-600 hover:bg-blue-700 text-white"
                              : "bg-stone-600 text-stone-400 cursor-not-allowed"
                          }`}
                          title={
                            isFaint()
                              ? "Cannot feed - chicken is faint"
                              : isDead()
                                ? "Cannot feed - chicken is dead"
                                : isBreeding()
                                  ? "Cannot feed - chicken is breeding"
                                  : "Feed"
                          }
                        >
                          <Utensils size={16} />
                          Feed
                        </button>
                      )}

                      {/* Heal Button */}
                      {actionPermissions.canHeal && (
                        <button
                          onClick={openHealModal}
                          disabled={
                            gameStats.currentHp >= gameStats.hp ||
                            isFaint() ||
                            isDead() ||
                            isBreeding()
                          }
                          className={`flex items-center gap-1 px-3 py-2 rounded-lg transition-colors ${
                            gameStats.currentHp < gameStats.hp &&
                            !isFaint() &&
                            !isDead() &&
                            !isBreeding()
                              ? "bg-green-600 hover:bg-green-700 text-white"
                              : "bg-stone-600 text-stone-400 cursor-not-allowed"
                          }`}
                          title={
                            isFaint()
                              ? "Cannot heal - chicken is faint"
                              : isDead()
                                ? "Cannot heal - chicken is dead"
                                : isBreeding()
                                  ? "Cannot heal - chicken is breeding"
                                  : gameStats.currentHp >= gameStats.hp
                                    ? "Chicken is already at full health"
                                    : "Heal"
                          }
                        >
                          <Plus size={16} />
                          Heal
                        </button>
                      )}
                    </>
                  )}

                  {/* Show status message for delegated out chickens */}
                  {chickenType === "delegated-out" && (
                    <div className="text-center text-gray-400 py-2">
                      <p className="text-sm">
                        This chicken is delegated to someone else.
                      </p>
                      <p className="text-xs">
                        Actions are disabled while delegated.
                      </p>
                    </div>
                  )}
                </div>
              </>
            )}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex border-b border-gray-700 mb-6">
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "stats"
              ? "text-yellow-400 border-b-2 border-yellow-400"
              : "text-gray-400 hover:text-white"
          }`}
          onClick={() => setActiveTab("stats")}
        >
          <div className="flex items-center gap-2">
            <Activity size={16} />
            Battle Stats
          </div>
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "genes"
              ? "text-yellow-400 border-b-2 border-yellow-400"
              : "text-gray-400 hover:text-white"
          }`}
          onClick={() => setActiveTab("genes")}
        >
          <div className="flex items-center gap-2">
            <Dna size={16} />
            Genetic Info
          </div>
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === "battleLogs"
              ? "text-yellow-400 border-b-2 border-yellow-400"
              : "text-gray-400 hover:text-white"
          }`}
          onClick={() => setActiveTab("battleLogs")}
        >
          <div className="flex items-center gap-2">
            <Trophy size={16} />
            Battle Logs
          </div>
        </button>
      </div>

      {/* Main Content */}
      {chickenData && (
        <div className="flex flex-col gap-6">
          {activeTab === "stats" ? (
            <>
              {/* Battle Stats Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Chicken Info Card */}
                <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  <div className="bg-gradient-to-r from-gray-700 to-gray-800 px-4 py-3 border-b border-gray-700">
                    <h3 className="font-bold text-white flex items-center">
                      <Shield className="mr-2 text-blue-400" size={18} />
                      Chicken Profile
                    </h3>
                  </div>
                  <div className="px-4 pb-4">
                    <div className="flex justify-center relative">
                      {/* Cooldown Timer - positioned in the upper right of the image */}
                      {cooldownTimer > 0 && !isFaint() && !isDead() && (
                        <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30">
                          {formatCooldownTime(cooldownTimer)}
                        </div>
                      )}

                      {/* Recovery Timer for faint chickens */}
                      {isFaint() && recoveryTimer > 0 && (
                        <div className="absolute top-2 left-2 bg-orange-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatRecoveryTime(recoveryTimer)}
                        </div>
                      )}

                      {isBreeding() && breedingTimer > 0 && (
                        <div className="absolute top-2 left-2 bg-pink-600 text-white text-xs font-bold px-2 py-1 rounded-full z-30 flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatBreedingTime(breedingTimer)}
                        </div>
                      )}

                      <div className="relative">
                        <iframe
                          src={`https://chicken-animation.vercel.app/chicken/${chickenId}`}
                          width="300"
                          height="300"
                          frameBorder="0"
                          style={{ background: "transparent" }}
                          className={`transition-all duration-300 ${
                            isFaint() || isDead() ? "grayscale" : ""
                          }`}
                        ></iframe>

                        {/* State overlay for iframe */}
                        {isFaint() && (
                          <div className="absolute inset-0 bg-black bg-opacity-60 rounded-lg flex flex-col items-center justify-center">
                            <div className="text-red-400 font-bold text-lg mb-2">
                              FAINT
                            </div>
                            <div className="text-white text-sm text-center px-2">
                              {recoveryTimer > 0
                                ? `Recovers in ${formatRecoveryTime(recoveryTimer)}`
                                : "Ready to recover!"}
                            </div>
                          </div>
                        )}

                        {/* {isBreeding() && (
                          <div className="absolute inset-0 bg-pink-900 bg-opacity-70 rounded-lg flex flex-col items-center justify-center">
                            <div className="text-pink-300 font-bold text-lg mb-2 flex items-center gap-2">
                              <MountainSnow className="h-6 w-6" />
                              BREEDING
                            </div>
                            <div className="text-white text-sm text-center px-2">
                              {breedingTimer > 0
                                ? `Completes in ${formatBreedingTime(breedingTimer)}`
                                : "Breeding complete!"}
                            </div>
                          </div>
                        )} */}

                        {isDead() && (
                          <div className="absolute inset-0 bg-black bg-opacity-80 rounded-lg flex flex-col items-center justify-center">
                            <div className="text-red-600 font-bold text-lg mb-2">
                              DEAD
                            </div>
                            <div className="text-gray-400 text-sm text-center px-2">
                              This chicken has died. Your chicken&apos;s journey
                              has ended, but the bond remains forever.
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* HP Bar - only show for non-faint/dead chickens */}
                    {!isFaint() && !isDead() && !isBreeding() && (
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span className="font-medium">HP</span>
                          <span>
                            {gameStats.currentHp.toFixed(0)}/
                            {gameStats.hp.toFixed(0)}
                          </span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-4 relative overflow-hidden">
                          <div
                            className={`${getHpColor(hpPercentage)} h-4 rounded-full transition-all duration-500`}
                            style={{ width: `${hpPercentage}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {(isFaint() || isDead() || isBreeding()) && (
                      <div className="mb-4">
                        <div
                          className={`text-center p-3 rounded-lg ${
                            isFaint()
                              ? "bg-orange-900/50 border border-orange-500 text-orange-300"
                              : isDead()
                                ? "bg-red-900/50 border border-red-500 text-red-300"
                                : isBreeding()
                                  ? "bg-pink-900/50 border border-pink-500 text-pink-300"
                                  : ""
                          }`}
                        >
                          <div className="font-bold">
                            {isFaint()
                              ? "🔄 FAINT STATE"
                              : isDead()
                                ? "💀 DEAD"
                                : isBreeding()
                                  ? "💕 BREEDING"
                                  : ""}
                          </div>
                          <div className="text-sm mt-1">
                            {isFaint()
                              ? recoveryTimer > 0
                                ? `Recovers in ${formatRecoveryTime(recoveryTimer)}`
                                : "Ready to recover!"
                              : isDead()
                                ? "This chicken has died."
                                : isBreeding()
                                  ? breedingTimer > 0
                                    ? `Completes in ${formatBreedingTime(breedingTimer)}`
                                    : "Breeding complete!"
                                  : ""}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Battle Record */}
                    <div className="bg-gray-700 rounded-lg p-3 mb-4">
                      <h4 className="text-sm font-semibold text-gray-300 mb-2 flex items-center">
                        <Trophy className="mr-1 text-yellow-400" size={14} />
                        Battle Record
                      </h4>
                      <div className="grid grid-cols-3 gap-2 text-center">
                        <div className="bg-green-900 bg-opacity-30 rounded p-2">
                          <div className="text-green-400 font-bold">
                            {battleRecord.wins}
                          </div>
                          <div className="text-xs text-gray-400">Wins</div>
                        </div>
                        <div className="bg-red-900 bg-opacity-30 rounded p-2">
                          <div className="text-red-400 font-bold">
                            {battleRecord.losses}
                          </div>
                          <div className="text-xs text-gray-400">Losses</div>
                        </div>
                        <div className="bg-blue-900 bg-opacity-30 rounded p-2">
                          <div className="text-blue-400 font-bold">
                            {battleRecord.draws}
                          </div>
                          <div className="text-xs text-gray-400">Draws</div>
                        </div>
                      </div>
                    </div>

                    {/* Chicken Attributes */}
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      {chickenData.attributes.map((attr, index) => {
                        if (
                          [
                            "Beak",
                            "Comb",
                            "Eyes",
                            "Feet",
                            "Wings",
                            "Tail",
                            "Body",
                          ].includes(attr.trait_type)
                        ) {
                          return (
                            <div key={index} className="flex items-center">
                              <span className="mr-1">
                                {getBodyPartIcon(attr.trait_type)}
                              </span>
                              <div>
                                <span className="text-gray-400">
                                  {attr.trait_type}:
                                </span>{" "}
                                <span
                                  className={
                                    typeof attr.value === "string" &&
                                    isLegendary(attr.value)
                                      ? "text-yellow-400 font-medium"
                                      : "text-white"
                                  }
                                >
                                  {attr.value}
                                  {typeof attr.value === "string" &&
                                    isLegendary(attr.value) && (
                                      <span className="ml-1 text-xs">⭐</span>
                                    )}
                                </span>
                              </div>
                            </div>
                          );
                        }
                        return null;
                      })}
                      <div className="flex items-center">
                        <span className="mr-1">🧠</span>
                        <div>
                          <span className="text-gray-400">Instinct:</span>{" "}
                          <span className="text-white">
                            {gameStats.instinct ||
                              findAttributeByType(
                                chickenData.attributes,
                                "Instinct"
                              )}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Battle Stats Card */}
                <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  <div className="bg-gradient-to-r from-gray-700 to-gray-800 px-4 py-3 border-b border-gray-700">
                    <h3 className="font-bold text-white flex items-center">
                      <Swords className="mr-2 text-red-400" size={18} />
                      Battle Stats
                    </h3>
                  </div>
                  <div className="p-4">
                    <div className="mt-2" style={{ height: "350px" }}>
                      <Bar
                        data={chartData}
                        options={{
                          scales: {
                            y: {
                              beginAtZero: true,
                              max: 120,
                              grid: {
                                color: "rgba(255, 255, 255, 0.1)",
                              },
                              ticks: {
                                color: "rgba(255, 255, 255, 0.7)",
                              },
                            },
                            x: {
                              grid: {
                                color: "rgba(255, 255, 255, 0.1)",
                              },
                              ticks: {
                                color: "rgba(255, 255, 255, 0.7)",
                              },
                            },
                          },
                          maintainAspectRatio: false,
                          responsive: true,
                          plugins: {
                            legend: {
                              display: false,
                            },
                            datalabels: {
                              anchor: "end",
                              align: "top",
                              formatter: Math.round,
                              font: {
                                weight: "bold",
                              },
                              color: "white",
                            },
                          },
                        }}
                      />
                    </div>

                    {/* Stat Details */}
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-red-500 bg-opacity-20 flex items-center justify-center mr-3">
                          <Crosshair size={16} className="text-red-400" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400">Attack</div>
                          <div className="font-bold text-white flex items-center">
                            {gameStats.attack.toFixed(1)}
                            {gameStats.boosters?.attack &&
                              gameStats.boosters.attack > 0 && (
                                <span className="ml-2 text-green-400 text-xs font-bold">
                                  (+{gameStats.boosters.attack})
                                </span>
                              )}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-blue-500 bg-opacity-20 flex items-center justify-center mr-3">
                          <Shield size={16} className="text-blue-400" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400">Defense</div>
                          <div className="font-bold text-white flex items-center">
                            {gameStats.defense.toFixed(1)}
                            {gameStats.boosters?.defense &&
                              gameStats.boosters.defense > 0 && (
                                <span className="ml-2 text-green-400 text-xs font-bold">
                                  (+{gameStats.boosters.defense})
                                </span>
                              )}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-yellow-500 bg-opacity-20 flex items-center justify-center mr-3">
                          <Zap size={16} className="text-yellow-400" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400">Speed</div>
                          <div className="font-bold text-white flex items-center">
                            {gameStats.speed.toFixed(1)}
                            {gameStats.boosters?.speed &&
                              gameStats.boosters.speed > 0 && (
                                <span className="ml-2 text-green-400 text-xs font-bold">
                                  (+{gameStats.boosters.speed})
                                </span>
                              )}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-green-500 bg-opacity-20 flex items-center justify-center mr-3">
                          <Flame size={16} className="text-green-400" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400">Cockrage</div>
                          <div className="font-bold text-white flex items-center">
                            {gameStats.cockrage.toFixed(1)}
                            {gameStats.boosters?.cockrage &&
                              gameStats.boosters.cockrage > 0 && (
                                <span className="ml-2 text-green-400 text-xs font-bold">
                                  (+{gameStats.boosters.cockrage})
                                </span>
                              )}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-purple-500 bg-opacity-20 flex items-center justify-center mr-3">
                          <Axe size={16} className="text-purple-400" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400">Ferocity</div>
                          <div className="font-bold text-white flex items-center">
                            {gameStats.ferocity.toFixed(1)}
                            {gameStats.boosters?.ferocity &&
                              gameStats.boosters.ferocity > 0 && (
                                <span className="ml-2 text-green-400 text-xs font-bold">
                                  (+{gameStats.boosters.ferocity})
                                </span>
                              )}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-700 rounded-lg p-3 flex items-center">
                        <div className="w-8 h-8 rounded-full bg-orange-500 bg-opacity-20 flex items-center justify-center mr-3">
                          <Wind size={16} className="text-orange-400" />
                        </div>
                        <div className="flex-1">
                          <div className="text-xs text-gray-400">Evasion</div>
                          <div className="font-bold text-white flex items-center">
                            {gameStats.evasion.toFixed(1)}
                            {gameStats.boosters?.evasion &&
                              gameStats.boosters.evasion > 0 && (
                                <span className="ml-2 text-green-400 text-xs font-bold">
                                  (+{gameStats.boosters.evasion})
                                </span>
                              )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Innate & Grit Points Card */}
                <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  <div className="bg-gradient-to-r from-gray-700 to-gray-800 px-4 py-3 border-b border-gray-700">
                    <h3 className="font-bold text-white flex items-center">
                      <Star className="mr-2 text-yellow-400" size={18} />
                      Innate & Grit Points
                    </h3>
                  </div>
                  <div className="p-4">
                    {radarChartData && (
                      <div>
                        <div className="mb-4 mt-4">
                          <Radar
                            data={radarChartData}
                            options={{
                              scales: {
                                r: {
                                  beginAtZero: true,
                                  ticks: {
                                    display: false,
                                  },
                                  grid: {
                                    color: "rgba(255, 255, 255, 0.1)",
                                  },
                                  pointLabels: {
                                    color: "rgba(255, 255, 255, 0.7)",
                                  },
                                },
                              },
                              responsive: true,
                              plugins: {
                                datalabels: {
                                  display: false,
                                },
                                legend: {
                                  position: "bottom",
                                  labels: {
                                    color: "white",
                                    boxWidth: 12,
                                    padding: 20,
                                  },
                                },
                              },
                            }}
                          />
                        </div>

                        {points && (
                          <div className="grid grid-cols-2 gap-3 mt-8">
                            <div className="bg-blue-900 bg-opacity-20 rounded-lg p-3">
                              <h4 className="text-blue-400 text-sm font-semibold mb-2">
                                Innate Points
                              </h4>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Attack:</span>
                                  <span className="text-white font-medium">
                                    {points.innatePoints.attack}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">
                                    Defense:
                                  </span>
                                  <span className="text-white font-medium">
                                    {points.innatePoints.defense}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Speed:</span>
                                  <span className="text-white font-medium">
                                    {points.innatePoints.speed}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">HP:</span>
                                  <span className="text-white font-medium">
                                    {points.innatePoints.hp}
                                  </span>
                                </div>
                              </div>
                            </div>

                            <div className="bg-red-900 bg-opacity-20 rounded-lg p-3">
                              <h4 className="text-red-400 text-sm font-semibold mb-2">
                                Grit Points
                              </h4>
                              <div className="space-y-2 text-sm">
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Attack:</span>
                                  <span className="text-white font-medium">
                                    {points.gritPoints.attack}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">
                                    Defense:
                                  </span>
                                  <span className="text-white font-medium">
                                    {points.gritPoints.defense}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">Speed:</span>
                                  <span className="text-white font-medium">
                                    {points.gritPoints.speed}
                                  </span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-gray-400">HP:</span>
                                  <span className="text-white font-medium">
                                    {points.gritPoints.hp}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              {findAttributeByType(chickenData?.attributes, "Type") !==
                "Egg" && (
                <>
                  {/* Additional Actions - Based on chicken type and permissions */}
                  {(chickenType === "owned" ||
                    chickenType === "delegated-out") && (
                    <div className="bg-gray-800 rounded-lg p-4 mt-4">
                      <h3 className="text-lg font-semibold text-white mb-3">
                        Additional Actions
                      </h3>
                      <div className="flex flex-wrap gap-3">
                        {/* Delegate button - Only for owned chickens that are not delegated out */}
                        {chickenType === "owned" && (
                          <button
                            onClick={handleDelegate}
                            disabled={!actionPermissions.canDelegate}
                            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                              actionPermissions.canDelegate
                                ? "bg-blue-600 hover:bg-blue-700 text-white"
                                : "bg-gray-600 cursor-not-allowed opacity-50 text-gray-300"
                            }`}
                            title={
                              !actionPermissions.canDelegate
                                ? isFaint()
                                  ? "Cannot delegate - chicken is fainted"
                                  : isDead()
                                    ? "Cannot delegate - chicken is dead"
                                    : isBreeding()
                                      ? "Cannot delegate - chicken is breeding"
                                      : "Cannot delegate"
                                : "Delegate this chicken"
                            }
                          >
                            <UserCheck size={18} />
                            Delegate
                          </button>
                        )}

                        {/* Release button - Only for owned chickens */}
                        {actionPermissions.canRelease && (
                          <button
                            onClick={handleRelease}
                            className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                          >
                            <LogOut size={18} />
                            Release
                          </button>
                        )}

                        {/* Show status for delegated out chickens */}
                        {chickenType === "delegated-out" && (
                          <div className="text-gray-400 text-sm">
                            <p>
                              This chicken is currently delegated to someone
                              else.
                            </p>
                            <p>
                              Additional actions are disabled while delegated.
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Show delegation info for delegated/rented to me chickens */}
                  {(chickenType === "delegated-to-me" ||
                    chickenType === "rented-to-me") && (
                    <div className="bg-gray-800 rounded-lg p-4 mt-4">
                      <h3 className="text-lg font-semibold text-white mb-3">
                        {chickenType === "delegated-to-me"
                          ? "Delegation Info"
                          : "Rental Info"}
                      </h3>
                      <div className="text-gray-300 text-sm space-y-2">
                        <p>
                          <span className="text-gray-400">Owner:</span>{" "}
                          {delegationInfo?.ownerAddress ||
                            rentalInfo?.ownerAddress ||
                            "Unknown"}
                        </p>
                        <p>
                          <span className="text-gray-400">Access:</span>{" "}
                          {(() => {
                            const task =
                              delegationInfo?.delegatedTask ||
                              rentalInfo?.delegatedTask;
                            switch (task) {
                              case 1:
                                return "Daily Care Only";
                              case 2:
                                return "Gameplay Only";
                              case 3:
                                return "Full Access";
                              default:
                                return "Unknown";
                            }
                          })()}
                        </p>
                        {chickenType === "rented-to-me" && rentalInfo && (
                          <p>
                            <span className="text-gray-400">Daily Rate:</span>{" "}
                            {rentalInfo.roninPrice} RON
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}
            </>
          ) : activeTab === "genes" ? (
            <>
              {/* Genetic Information Section */}
              {geneData && (
                <div className="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                  <div className="bg-gradient-to-r from-gray-700 to-gray-800 px-4 py-3 border-b border-gray-700">
                    <h3 className="font-bold text-white flex items-center">
                      <Dna className="mr-2 text-green-400" size={18} />
                      Genetic Information
                    </h3>
                  </div>
                  <div className="p-6">
                    {/* Decoded Genes Section */}
                    <div>
                      <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                        <span className="text-green-400 mr-2">🧬</span>
                        Decoded Genes
                      </h4>

                      {/* Table-like structure with header row */}
                      <div className="bg-gray-700 rounded-lg overflow-hidden mb-8 border border-gray-600">
                        {/* Header Row */}
                        <div className="grid grid-cols-5 gap-2 bg-gray-600 p-3">
                          <div className="text-yellow-300 font-semibold text-left">
                            Body Part
                          </div>
                          <div className="text-yellow-300 font-semibold text-center">
                            Primary
                          </div>
                          <div className="text-yellow-300 font-semibold text-center">
                            Hidden 1
                          </div>
                          <div className="text-yellow-300 font-semibold text-center">
                            Hidden 2
                          </div>
                          <div className="text-yellow-300 font-semibold text-center">
                            Hidden 3
                          </div>
                        </div>

                        {/* Gene Rows */}
                        {Object.entries(geneData).map(([key, value]) => {
                          if (typeof value === "object" && value !== null) {
                            const geneValue = value as GeneValue;

                            return (
                              <div
                                key={key}
                                className="grid grid-cols-5 gap-2 p-3 border-b border-gray-600 hover:bg-[#2d3748] transition-colors"
                              >
                                {/* Body Part with Icon */}
                                <div className="flex items-center justify-start gap-2">
                                  {getBodyPartIcon(key)}
                                  <span className="text-sm text-white">
                                    {key}
                                  </span>
                                </div>

                                {/* Primary Gene */}
                                <div className="flex items-center justify-center">
                                  {geneValue.p ? (
                                    <span
                                      className={`text-sm ${isLegendary(geneValue.p) ? "text-yellow-400 font-medium" : "text-white"}`}
                                    >
                                      {geneValue.p}
                                      {isLegendary(geneValue.p) && (
                                        <span className="ml-1 text-xs">⭐</span>
                                      )}
                                    </span>
                                  ) : (
                                    <span className="text-sm text-gray-400">
                                      -
                                    </span>
                                  )}
                                </div>

                                {/* Hidden Genes */}
                                {["h1", "h2", "h3"].map((hidden) => (
                                  <div
                                    key={hidden}
                                    className="flex items-center justify-center"
                                  >
                                    {geneValue[hidden as keyof GeneValue] ? (
                                      <span className="text-sm text-white">
                                        {geneValue[hidden as keyof GeneValue]}
                                      </span>
                                    ) : (
                                      <span className="text-sm text-gray-400">
                                        -
                                      </span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            );
                          }
                          return null;
                        })}
                      </div>

                      {/* Instincts and Innate Points Section */}
                      <div className="mt-8">
                        <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                          <span className="text-green-400 mr-2">🔍</span>
                          Other Traits
                        </h4>
                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                          {/* Trait boxes */}
                          {[
                            { name: "Instinct", icon: "🧠", key: "Instinct" },
                            {
                              name: "Attack",
                              icon: "⚔️",
                              key: "Innate Attack",
                            },
                            {
                              name: "Defense",
                              icon: "🛡️",
                              key: "Innate Defense",
                            },
                            { name: "Speed", icon: "⚡", key: "Innate Speed" },
                            {
                              name: "Health",
                              icon: "❤️",
                              key: "Innate Health",
                            },
                          ].map((trait) => (
                            <div
                              key={trait.key}
                              className="bg-gray-700 p-4 rounded-lg hover:bg-[#2d3748] transition-colors border border-gray-600"
                            >
                              <div className="flex items-center gap-2 mb-2">
                                <span className="text-xl">{trait.icon}</span>
                                <h4 className="text-yellow-300 font-semibold">
                                  {trait.name}
                                </h4>
                              </div>
                              <p className="text-sm text-white font-medium">
                                {renderGeneDataValue(trait.key)}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : activeTab === "battleLogs" ? (
            <div className="bg-gray-900 rounded-lg p-4 shadow-xl border border-gray-700">
              <h3 className="text-xl font-bold text-white mb-4 flex items-center">
                Recent 20 Matches
              </h3>

              {battleLogs.length === 0 ? (
                <div className="text-center p-6 text-gray-400">
                  No battles recorded yet
                </div>
              ) : (
                <div className="space-y-4">
                  {battleLogs.map((log, index) => {
                    // Determine border color based on outcome for this user's chicken
                    let borderColor = "border-gray-600";
                    if (log.outcome === "won") borderColor = "border-green-500";
                    else if (log.outcome === "lost")
                      borderColor = "border-red-500";
                    else if (log.outcome === "draw")
                      borderColor = "border-yellow-500";

                    return (
                      <div
                        key={index}
                        className={`bg-gradient-to-r from-gray-800 to-gray-700 p-4 rounded-lg shadow-lg transition-all hover:shadow-xl hover:scale-[1.01] border-l-4 ${borderColor}`}
                      >
                        <div className="flex flex-col md:flex-row justify-between mb-3 gap-2">
                          <div className="text-gray-400 text-sm">
                            {new Date(log.date).toLocaleString()}
                          </div>
                          {log.mmrChange !== undefined && (
                            <div
                              className={`text-sm font-semibold ${
                                log.outcome === "won"
                                  ? "text-green-400"
                                  : log.outcome === "lost"
                                    ? "text-red-400"
                                    : "text-gray-400"
                              }`}
                            >
                              MMR: {log.mmrChange > 0 ? "+" : ""}
                              {log.mmrChange}
                            </div>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* User's Chicken Card */}
                          <div
                            className={`rounded-lg p-3 border transform transition-all ${
                              log.outcome === "won"
                                ? "bg-gradient-to-br from-green-900/30 to-green-700/20 border-green-500/50"
                                : log.outcome === "lost"
                                  ? "bg-gradient-to-br from-red-900/30 to-red-700/20 border-red-500/50"
                                  : log.outcome === "draw"
                                    ? "bg-gradient-to-br from-yellow-900/30 to-yellow-700/20 border-yellow-500/50"
                                    : "bg-gray-700/50 border-gray-600/30"
                            }`}
                          >
                            <div className="flex items-center mb-2">
                              <div
                                className={`w-8 h-8 ${
                                  log.outcome === "won"
                                    ? "bg-green-500"
                                    : log.outcome === "lost"
                                      ? "bg-red-500"
                                      : log.outcome === "draw"
                                        ? "bg-yellow-500"
                                        : "bg-gray-600"
                                } rounded-full flex items-center justify-center mr-2`}
                              >
                                <span className="text-gray-900 font-bold text-sm">
                                  {log.outcome === "won"
                                    ? "W"
                                    : log.outcome === "lost"
                                      ? "L"
                                      : "D"}
                                </span>
                              </div>
                              <span
                                className={`font-bold ${
                                  log.outcome === "won"
                                    ? "text-green-400"
                                    : log.outcome === "lost"
                                      ? "text-red-400"
                                      : log.outcome === "draw"
                                        ? "text-yellow-400"
                                        : "text-gray-400"
                                }`}
                              >
                                {log.outcome === "won"
                                  ? "WINNER"
                                  : log.outcome === "lost"
                                    ? "DEFEATED"
                                    : "DRAW"}
                              </span>
                              <span className="ml-2 text-xs bg-blue-600/50 text-blue-100 px-2 py-0.5 rounded-full">
                                YOUR CHICKEN
                              </span>
                            </div>

                            <div
                              className={`pl-2 border-l-2 ${
                                log.outcome === "won"
                                  ? "border-green-500"
                                  : log.outcome === "lost"
                                    ? "border-red-500"
                                    : log.outcome === "draw"
                                      ? "border-yellow-500"
                                      : "border-gray-500"
                              }`}
                            >
                              <div className="mb-1 flex items-center">
                                <span className="text-white font-mono">
                                  Chicken #{log.thisToken.tokenId}
                                </span>
                              </div>
                              <div className="text-gray-300 text-sm truncate">
                                Owner:{" "}
                                <span className="text-gray-300">
                                  {truncateAddress(log.thisToken.owner)}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Opponent Card */}
                          {log.opponents.length > 0 && (
                            <div className="rounded-lg p-3 border bg-gray-700/50 border-gray-600/30">
                              <div className="flex items-center mb-2">
                                <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center mr-2">
                                  <span className="text-gray-900 font-bold text-sm">
                                    {log.outcome === "won"
                                      ? "L"
                                      : log.outcome === "lost"
                                        ? "W"
                                        : "D"}
                                  </span>
                                </div>
                                <span className="font-bold text-gray-400">
                                  {log.outcome === "won"
                                    ? "DEFEATED"
                                    : log.outcome === "lost"
                                      ? "WINNER"
                                      : "OPPONENT"}
                                </span>
                              </div>

                              <div className="pl-2 border-l-2 border-gray-600">
                                <div className="mb-1 flex items-center">
                                  <span className="text-white font-mono">
                                    Chicken #
                                    {log.opponents[0]?.tokenId || "N/A"}
                                  </span>
                                </div>
                                <div className="text-gray-400 text-sm truncate">
                                  Owner:{" "}
                                  <span className="text-gray-300">
                                    {log.opponents[0]?.owner
                                      ? truncateAddress(log.opponents[0].owner)
                                      : "N/A"}
                                  </span>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          ) : null}
          <BattleModal
            isOpen={isBattleModalOpen}
            onClose={() => setIsBattleModalOpen(false)}
            chickenId={chickenId}
            chickenImage={chickenData?.image}
            chickenStats={{
              level: gameStats.level,
              attack: gameStats.attack,
              defense: gameStats.defense,
              speed: gameStats.speed,
              hp: gameStats.currentHp,
              maxHp: gameStats.hp,
            }}
          />
          <NotReady
            isOpen={isNotReadyOpen}
            onClose={() => setNotReadyOpen(false)}
          />
          <CreateRentalModal
            isOpen={isDelegateModalOpen}
            onOpenChange={setIsDelegateModalOpen}
            preSelectedChickenId={chickenId ? Number(chickenId) : null}
          />
          <FeedModal
            setShow={setShowFeedModal}
            show={showFeedModal}
            tokenId={Number(chickenId)}
          />
          <HealModal
            isOpen={healModalOpen}
            onClose={() => setHealModalOpen(false)}
            chickenId={chickenId}
            chickenImage={chickenData?.image}
            currentHp={gameStats.currentHp}
            maxHp={gameStats.hp}
            onHeal={handleHealChicken}
            healInfo={healInfo}
            refetch={() => fetchChickenData(true)}
            isLoading={healInfoLoading}
            onBattle={() => {
              // Close heal modal and open battle modal
              setIsBattleModalOpen(true);
            }}
          />
        </div>
      )}
    </>
  );
}
