"use client";

import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { useSignMessage } from "wagmi";
import axios from "@/lib/api";

// Types for Battle API
interface IBattleRequestResponse {
  status: number;
  message: string;
  data: string; // The message to sign
}

interface IBattleVerifyResponse {
  status: number;
  message: string;
  data: {
    chickenTokenId: number;
    nonce: number;
    signature: string;
  };
}

interface IBattleRequestPayload {
  chickenTokenId: number;
}

interface IBattleVerifyPayload {
  chickenTokenId: number;
  signature: string;
}

// API functions
export const battleRequest = async (payload: IBattleRequestPayload): Promise<IBattleRequestResponse> => {
  const { data } = await axios.post("/battle/request", payload);
  return data;
};

export const battleVerify = async (payload: IBattleVerifyPayload): Promise<IBattleVerifyResponse> => {
  const { data } = await axios.post("/battle/verify", payload);
  return data;
};

// Custom hook for battle flow
export const useBattleApi = () => {
  const { signMessage, isPending: isSigningMessage } = useSignMessage();

  // Battle request mutation
  const battleRequestMutation = useMutation({
    mutationFn: battleRequest,
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || "Failed to request battle";
      toast.error(errorMessage);
    },
  });

  // Battle verify mutation
  const battleVerifyMutation = useMutation({
    mutationFn: battleVerify,
    onSuccess: (data) => {
      toast.success("Battle ownership verified successfully!");
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || "Failed to verify battle ownership";
      toast.error(errorMessage);
    },
  });

  // Complete battle flow function
  const initiateBattle = async (chickenTokenId: number): Promise<IBattleVerifyResponse | null> => {
    try {
      // Step 1: Request battle (get message to sign)
      const requestResult = await battleRequestMutation.mutateAsync({ chickenTokenId });
      
      if (requestResult.status !== 1) {
        throw new Error(requestResult.message);
      }

      const messageToSign = requestResult.data;

      // Step 2: Sign the message
      return new Promise((resolve, reject) => {
        signMessage(
          { message: messageToSign },
          {
            onSuccess: async (signature) => {
              try {
                // Step 3: Verify the signature
                const verifyResult = await battleVerifyMutation.mutateAsync({
                  chickenTokenId,
                  signature,
                });

                if (verifyResult.status === 1) {
                  resolve(verifyResult);
                } else {
                  reject(new Error(verifyResult.message));
                }
              } catch (error) {
                reject(error);
              }
            },
            onError: (error) => {
              reject(error);
            },
          }
        );
      });
    } catch (error) {
      console.error("Battle initiation failed:", error);
      return null;
    }
  };

  return {
    // Individual mutations
    battleRequestMutation,
    battleVerifyMutation,
    
    // Complete flow function
    initiateBattle,
    
    // Loading states
    isRequestingBattle: battleRequestMutation.isPending,
    isVerifyingBattle: battleVerifyMutation.isPending,
    isSigningMessage,
    
    // Combined loading state
    isBattleInProgress: battleRequestMutation.isPending || isSigningMessage || battleVerifyMutation.isPending,
  };
};

// Hook for just the API calls without the complete flow
export const useBattleApiCalls = () => {
  const battleRequestMutation = useMutation({
    mutationFn: battleRequest,
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || "Failed to request battle";
      toast.error(errorMessage);
    },
  });

  const battleVerifyMutation = useMutation({
    mutationFn: battleVerify,
    onSuccess: (data) => {
      toast.success("Battle ownership verified successfully!");
    },
    onError: (error: any) => {
      const errorMessage = error?.response?.data?.message || "Failed to verify battle ownership";
      toast.error(errorMessage);
    },
  });

  return {
    battleRequest: battleRequestMutation.mutateAsync,
    battleVerify: battleVerifyMutation.mutateAsync,
    isRequestingBattle: battleRequestMutation.isPending,
    isVerifyingBattle: battleVerifyMutation.isPending,
  };
};
