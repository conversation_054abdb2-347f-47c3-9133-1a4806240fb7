import { HardhatRuntimeEnvironment } from "hardhat/types";

const deploy = async ({
  getNamedAccounts,
  deployments,
  ethers,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();
  await deploy("SabongSagaChickensLogic", {
    contract: "SabongSagaChickens",
    from: deployer,
    log: true,
  });
};

deploy.tags = ["SabongSagaChickensLogic"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;
