"use client";

import { useState, useEffect, useMemo, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loading } from "@/components/shared/loading";
import { usePreAlphaLeaderboard } from "@/hooks/usePreAlphaLeaderboard";

// Updated type to match the new API response
interface ChickenLeaderboardItem {
  rank: number;
  tokenId: number;
  name: string;
  mmr: number;
  wins: number;
  losses: number;
  draws: number;
  totalGames: number;
  type: string;
  lastUpdated: string;
  owner: string;
}

interface LeaderboardPagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface LeaderboardResponse {
  leaderboard: ChickenLeaderboardItem[];
  pagination: LeaderboardPagination;
  message?: string;
}

// Define column type for type safety
type Column = {
  key: keyof ChickenLeaderboardItem;
  label: string;
  className?: string;
  format?: (val: any, item?: ChickenLeaderboardItem) => React.ReactNode;
};

export default function ChickenLeaderboard() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [page, setPage] = useState(parseInt(searchParams.get("page") || "1"));
  const [limit, setLimit] = useState(20);
  const [searchId, setSearchId] = useState(searchParams.get("tokenId") || "");
  const [isSearching, setIsSearching] = useState(!!searchParams.get("tokenId"));

  // Custom hook to fetch data from the API
  const { data, isLoading, error } =
    usePreAlphaLeaderboard<LeaderboardResponse>("chickens", page, limit, {
      order: "desc", // Always sort by highest MMR first
      tokenId: searchId || undefined,
      minGames: "20", // Hardcoded to always be 20
    });

  const leaderboard = data?.leaderboard || [];
  const totalPages = data?.pagination?.pages || 1;
  const totalItems = data?.pagination?.total || 0;
  const apiMessage = data?.message;

  useEffect(() => {
    const params = new URLSearchParams();
    params.set("page", page.toString());
    if (searchId) params.set("tokenId", searchId);
    // minGames is not included in URL params as it's always 20
    router.replace(`/leaderboard?${params.toString()}`, { scroll: false });
  }, [page, searchId, router]);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleLimitChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      setLimit(parseInt(e.target.value));
      setPage(1);
    },
    []
  );

  const handleSearch = useCallback(() => {
    if (searchId) {
      setIsSearching(true);
      setPage(1);
    }
  }, [searchId]);

  const clearSearch = useCallback(() => {
    setSearchId("");
    setIsSearching(false);
    setPage(1);
  }, []);

  const downloadCsv = useCallback(() => {
    const params = new URLSearchParams();
    params.set("format", "csv");
    params.set("order", "desc");
    if (searchId) params.set("tokenId", searchId);
    params.set("minGames", "20");

    window.open(
      `https://chicken-api-ivory.vercel.app/api/stats/prealpha/leaderboard/chickens?${params.toString()}`,
      "_blank"
    );
  }, [searchId]);

  const paginationItems = useMemo(() => {
    const items = [];
    const maxVisiblePages = 5;

    items.push(
      <button
        key="first"
        onClick={() => handlePageChange(1)}
        className={`px-3 py-1 rounded-md ${
          page === 1
            ? "bg-primary text-black font-bold"
            : "bg-stone-800 text-white hover:bg-stone-700"
        }`}
      >
        1
      </button>
    );

    let startPage = Math.max(2, page - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 2);

    if (startPage > 2) {
      items.push(
        <span key="ellipsis1" className="px-2 text-white">
          ...
        </span>
      );
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-1 rounded-md ${
            page === i
              ? "bg-primary text-black font-bold"
              : "bg-stone-800 text-white hover:bg-stone-700"
          }`}
        >
          {i}
        </button>
      );
    }

    if (endPage < totalPages - 1) {
      items.push(
        <span key="ellipsis2" className="px-2 text-white">
          ...
        </span>
      );
    }

    if (totalPages > 1) {
      items.push(
        <button
          key="last"
          onClick={() => handlePageChange(totalPages)}
          className={`px-3 py-1 rounded-md ${
            page === totalPages
              ? "bg-primary text-black font-bold"
              : "bg-stone-800 text-white hover:bg-stone-700"
          }`}
        >
          {totalPages}
        </button>
      );
    }

    return items;
  }, [page, totalPages, handlePageChange]);

  // Get marketplace URL based on token ID
  const getMarketplaceUrl = (tokenId: number) => {
    if (tokenId <= 2222) {
      return `https://marketplace.roninchain.com/collections/sabong-saga-genesis/${tokenId}`;
    } else {
      return `https://marketplace.roninchain.com/collections/sabong-saga-chickens/${tokenId}`;
    }
  };

  // Get inventory URL
  const getInventoryUrl = (tokenId: number) => {
    return `https://app.sabongsaga.com/inventory/chickens/${tokenId}`;
  };

  // Format Ronin address for display
  const formatAddress = (address: string) => {
    if (!address || address === "Unknown") return "Unknown";

    // Check if it's an RNS-style name (contains a dot)
    if (address.includes(".")) return address;

    // Otherwise format as Ethereum address
    return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
  };

  // Type-safe columns definition for the new API
  const tableColumns = useMemo<Column[]>(() => {
    return [
      { key: "rank", label: "Rank" },
      {
        key: "tokenId",
        label: "Token ID",
        format: (value, item) => (
          <div className="group relative">
            <a
              href={getInventoryUrl(value)}
              target="_blank"
              rel="noopener noreferrer"
              className="text-white hover:text-primary transition-colors duration-200"
            >
              <span className="group-hover:text-primary">{value}</span>
              <span className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 absolute left-0 -bottom-5 text-xs bg-stone-700 px-2 py-1 rounded-md text-blue-300 whitespace-nowrap z-10">
                View in Inventory
              </span>
            </a>
          </div>
        ),
      },
      {
        key: "name",
        label: "Name",
        format: (value, item) => (
          <div className="group relative">
            <a
              href={getMarketplaceUrl(item?.tokenId || 0)}
              target="_blank"
              rel="noopener noreferrer"
              className="text-white hover:text-primary transition-colors duration-200"
            >
              <span className="group-hover:text-primary">{value}</span>
              <span className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 absolute left-0 -bottom-5 text-xs bg-stone-700 px-2 py-1 rounded-md text-yellow-300 whitespace-nowrap z-10">
                View in Marketplace
              </span>
            </a>
          </div>
        ),
      },
      { key: "mmr", label: "MMR", className: "text-yellow-400 font-bold" },
      { key: "wins", label: "Wins", className: "text-green-400" },
      { key: "losses", label: "Losses", className: "text-red-400" },
      { key: "draws", label: "Draws", className: "text-blue-400" },
      { key: "totalGames", label: "Total Games", className: "text-purple-400" },
      {
        key: "type",
        label: "Type",
        format: (value) => (
          <span className="px-2 py-1 text-xs rounded-full bg-stone-700">
            {value}
          </span>
        ),
      },
      {
        key: "owner",
        label: "Owner",
        format: (value) => (
          <div className="truncate max-w-[120px] text-cyan-300" title={value}>
            {formatAddress(value)}
          </div>
        ),
      },
    ];
  }, []);

  return (
    <div className="leaderboard-container">
      {/* Filters */}
      <div className="flex flex-wrap justify-between items-center mb-6 gap-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Chicken ID Search */}
          <div className="flex items-center space-x-2">
            <label htmlFor="searchId" className="text-white">
              Chicken ID:
            </label>
            <input
              id="searchId"
              type="number"
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
              className="bg-stone-800 text-white border border-stone-700 rounded-md px-3 py-1 w-24"
              placeholder="ID"
              onKeyDown={(e) => {
                if (e.key === "Enter") handleSearch();
              }}
            />
            <button
              onClick={handleSearch}
              className="bg-stone-700 text-white px-3 py-1 rounded-md hover:bg-stone-600 transition-colors duration-200"
            >
              Search
            </button>
            {isSearching && (
              <button
                onClick={clearSearch}
                className="bg-red-700 text-white px-3 py-1 rounded-md hover:bg-red-600 transition-colors duration-200"
              >
                Clear
              </button>
            )}
          </div>
        </div>

        {/* Results per page and CSV download */}
        <div className="flex items-center space-x-4">
          {!isSearching && (
            <select
              value={limit}
              onChange={handleLimitChange}
              className="bg-stone-800 text-white border border-stone-700 rounded-md px-3 py-1"
            >
              {[10, 20, 50, 100].map((val) => (
                <option key={val} value={val}>
                  {val} per page
                </option>
              ))}
            </select>
          )}

          {/* <button
            onClick={downloadCsv}
            className="bg-primary text-black px-4 py-2 rounded-md font-medium hover:bg-yellow-500 transition-colors duration-200 flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-2"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
              />
            </svg>
            Download CSV
          </button> */}
        </div>
      </div>

      {/* Filter indicators */}
      <div className="flex flex-wrap gap-2 mb-4">
        {isSearching && (
          <div className="bg-stone-700 text-white p-3 rounded-md flex justify-between items-center flex-1">
            <span>
              Showing results for Chicken ID: <strong>{searchId}</strong>
            </span>
            <button
              onClick={clearSearch}
              className="bg-red-700 text-white px-3 py-1 rounded-md hover:bg-red-600 transition-colors duration-200 text-sm"
            >
              Clear
            </button>
          </div>
        )}

        <div className="bg-stone-700 text-white p-3 rounded-md flex justify-between items-center flex-1">
          <span className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-primary"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
              />
            </svg>
            Showing chickens with at least{" "}
            <strong className="mx-1">20 games</strong>
          </span>
        </div>

        {isLoading && (
          <div className="bg-cyan-900 text-white p-3 rounded-md flex items-center flex-1">
            <svg
              className="animate-spin h-5 w-5 mr-2 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            <span>Loading data including blockchain owner information...</span>
          </div>
        )}
      </div>

      {/* API Message */}
      {apiMessage && (
        <div className="bg-blue-900 text-white p-3 rounded-md mb-4 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-blue-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          {apiMessage}
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-900 text-white p-4 rounded-md mb-6 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 mr-2 text-red-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          Failed to load leaderboard data. Please try again later.
        </div>
      )}

      {/* Content */}
      {isLoading ? (
        <Loading />
      ) : (
        <>
          {/* Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full bg-stone-800 rounded-lg overflow-hidden">
              <thead className="bg-stone-900">
                <tr>
                  {tableColumns.map((col) => (
                    <th
                      key={col.key}
                      className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
                    >
                      {col.label}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="divide-y divide-stone-700">
                {leaderboard.length > 0 ? (
                  leaderboard.map(
                    (item: ChickenLeaderboardItem, index: number) => (
                      <tr
                        key={`chicken-${item.tokenId}`}
                        className={`${
                          index % 2 === 0 ? "bg-stone-800" : "bg-stone-850"
                        } hover:bg-stone-700 transition-colors duration-150`}
                      >
                        {tableColumns.map((col) => {
                          const value = item[col.key];
                          return (
                            <td
                              key={col.key}
                              className={`px-4 py-3 whitespace-nowrap text-sm ${col.className || "text-gray-200"}`}
                            >
                              {col.format ? col.format(value, item) : value}
                            </td>
                          );
                        })}
                      </tr>
                    )
                  )
                ) : (
                  <tr>
                    <td
                      colSpan={tableColumns.length}
                      className="px-4 py-8 text-center text-gray-400"
                    >
                      {isSearching
                        ? `No chicken found with ID: ${searchId} that has played at least 20 games`
                        : `No chickens found that have played at least 20 games`}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination - only show if not searching for a specific ID */}
          {!isSearching && totalPages > 1 && (
            <div className="flex justify-center mt-6 space-x-2">
              <button
                onClick={() => handlePageChange(Math.max(1, page - 1))}
                disabled={page === 1}
                className="px-3 py-1 rounded-md bg-stone-800 text-white hover:bg-stone-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                &laquo;
              </button>
              {paginationItems}
              <button
                onClick={() => handlePageChange(Math.min(totalPages, page + 1))}
                disabled={page === totalPages}
                className="px-3 py-1 rounded-md bg-stone-800 text-white hover:bg-stone-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
              >
                &raquo;
              </button>
            </div>
          )}

          {/* Results summary */}
          <div className="mt-4 text-center text-sm text-gray-400">
            Showing {leaderboard.length} of {totalItems} entries
          </div>
        </>
      )}
    </div>
  );
}
