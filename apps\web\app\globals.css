@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --bg: 0 0% 100%;
    --fg: 240 3% 4%;

    /* Modified to #f7cb42 (converted to HSL: 43 91% 61%) */
    --primary: 43 91% 61%;
    --primary-fg: 43 91% 10%;

    --secondary: 286 4% 92%;
    --secondary-fg: 240 3% 4%;

    --overlay: 0 0% 100%;
    --overlay-fg: 240 3% 4%;

    --accent: 84 89% 52%;
    --accent-fg: 46 48% 9%;

    --muted: 286 1% 97%;
    --muted-fg: 286 3% 55%;

    --success: 163 65% 37%;
    --success-fg: 0 0% 100%;

    --warning: 84 89% 52%;
    --warning-fg: 46 48% 9%;

    --danger: 27 85% 36%;
    --danger-fg: 17 13% 97%;

    --border: 286 6% 91%;
    --input: 286 6% 87%;
    --ring: 43 91% 61%;

    --navbar: 286 1% 97%;
    --navbar-fg: 240 3% 4%;

    --sidebar: 286 1% 97%;
    --sidebar-fg: 240 3% 4%;

    --chart-1: 43 91% 61%;
    --chart-2: 43 91% 71%;
    --chart-3: 43 91% 51%;
    --chart-4: 43 81% 61%;
    --chart-5: 43 71% 61%;

    --radius: 0.5rem;
  }

  .dark {
    --bg: 286 5% 9%;
    --fg: 0 0% 99%;

    /* Slightly darker version for dark mode */
    --primary: 43 91% 55%;
    --primary-fg: 43 91% 10%;

    --secondary: 286 6% 24%;
    --secondary-fg: 0 0% 99%;

    --accent: 84 89% 52%;
    --accent-fg: 46 48% 9%;

    --muted: 286 6% 21%;
    --muted-fg: 286 3% 71%;

    --overlay: 286 6% 17%;
    --overlay-fg: 0 0% 99%;

    --success: 163 65% 37%;
    --success-fg: 0 0% 100%;

    --warning: 84 89% 52%;
    --warning-fg: 46 48% 9%;

    --danger: 27 85% 36%;
    --danger-fg: 17 13% 97%;

    --border: 286 13% 27%;
    --input: 286 13% 29%;
    --ring: 43 91% 55%;

    --navbar: 286 6% 17%;
    --navbar-fg: 0 0% 99%;

    --sidebar: 286 6% 16%;
    --sidebar-fg: 0 0% 99%;

    --chart-1: 43 91% 55%;
    --chart-2: 43 91% 65%;
    --chart-3: 43 91% 45%;
    --chart-4: 43 81% 55%;
    --chart-5: 43 71% 55%;
  }
}

@layer base {
  html {
    @apply scroll-smooth;
  }

  * {
    @apply border-border;
    font-feature-settings: "cv11", "ss01";
    font-variation-settings: "opsz" 850;
    text-rendering: optimizeLegibility;
    scrollbar-width: thin;
  }

  body {
    @apply bg-bg text-fg;
  }

  .dark {
    scrollbar-width: thin;

    @media (prefers-color-scheme: dark) {
      * {
        scrollbar-width: thin;
      }
    }
  }

  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  *::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 5px;
  }

  *::-webkit-scrollbar-thumb {
    @apply bg-muted;
    border-radius: 14px;
    border: 3px solid transparent;
  }

  @keyframes pulse {
    0% {
      filter: drop-shadow(0 0 15px rgba(255, 165, 0, 0.6))
        drop-shadow(0 0 30px rgba(255, 215, 0, 0.4));
    }
    50% {
      filter: drop-shadow(0 0 25px rgba(255, 165, 0, 0.8))
        drop-shadow(0 0 40px rgba(255, 215, 0, 0.6));
    }
    100% {
      filter: drop-shadow(0 0 15px rgba(255, 165, 0, 0.6))
        drop-shadow(0 0 30px rgba(255, 215, 0, 0.4));
    }
  }

  .debug {
    @apply outline-dashed outline-red-400;
  }
  .debug > * {
    @apply outline-dashed outline-green-400;
  }
}

/* Waiting Loading */
@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
.dot {
  animation: blink 1.5s infinite;
  animation-delay: calc(0.5s * var(--i));
}
.dot:nth-child(1) {
  --i: 0;
}
.dot:nth-child(2) {
  --i: 1;
}
.dot:nth-child(3) {
  --i: 2;
}
