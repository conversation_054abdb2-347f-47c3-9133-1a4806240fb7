// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "hardhat/console.sol";

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "@openzeppelin/contracts/token/ERC721/extensions/ERC721Burnable.sol";
import "@openzeppelin/contracts/token/ERC1155/IERC1155.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

contract MockERC20 is IERC20 {
    string public name = "Mock COCK";
    string public symbol = "COCK";
    uint8 public decimals = 18;
    uint256 public override totalSupply;
    mapping(address => uint256) public override balanceOf;
    mapping(address => mapping(address => uint256)) public override allowance;

    function transfer(
        address recipient,
        uint256 amount
    ) public override returns (bool) {
        require(balanceOf[msg.sender] >= amount, "Insufficient Balance");
        balanceOf[msg.sender] -= amount;
        balanceOf[recipient] += amount;
        return true;
    }

    function approve(
        address spender,
        uint256 amount
    ) public override returns (bool) {
        allowance[msg.sender][spender] = amount;
        return true;
    }

    function transferFrom(
        address sender,
        address recipient,
        uint256 amount
    ) public override returns (bool) {
        require(balanceOf[sender] >= amount, "Insufficient Balance");
        require(allowance[sender][msg.sender] >= amount, "Allowance exceeded");
        balanceOf[sender] -= amount;
        balanceOf[recipient] += amount;
        allowance[sender][msg.sender] -= amount;
        return true;
    }

    function mint(address to, uint256 amount) external {
        balanceOf[to] += amount;
        totalSupply += amount;
    }
}

contract MockERC721 is ERC721Enumerable, ERC721Burnable, Ownable {
    uint256 public tokenIdCounter;

    constructor(
        string memory name,
        string memory symbol,
        uint256 startingCount
    ) ERC721(name, symbol) Ownable(msg.sender) {
        tokenIdCounter = startingCount;
    }

    function mint(address to) external returns (uint256) {
        uint256 tokenId = ++tokenIdCounter;
        _mint(to, tokenId);
        return tokenId;
    }

    function burn(uint256 tokenId) public override(ERC721Burnable) {
        _burn(tokenId);
    }

    function supportsInterface(
        bytes4 interfaceId
    ) public view override(ERC721, ERC721Enumerable) returns (bool) {
        return super.supportsInterface(interfaceId);
    }

    function _increaseBalance(
        address account,
        uint128 amount
    ) internal override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, amount);
    }

    function _update(
        address to,
        uint256 tokenId,
        address auth
    ) internal override(ERC721, ERC721Enumerable) returns (address) {
        return super._update(to, tokenId, auth);
    }
}

contract MockERC1155 is IERC1155 {
    mapping(uint256 => mapping(address => uint256)) public balances;

    function balanceOf(
        address account,
        uint256 id
    ) public view override returns (uint256) {
        return balances[id][account];
    }

    function mint(address to, uint256 id, uint256 amount) external {
        balances[id][to] += amount;
    }

    function burnBatch(
        address from,
        uint256[] calldata ids,
        uint256[] calldata amounts
    ) external {
        for (uint256 i = 0; i < ids.length; i++) {
            require(
                balances[ids[i]][from] >= amounts[i],
                "Insufficient balance to burn"
            );
            balances[ids[i]][from] -= amounts[i];
        }
    }

    function setApprovalForAll(address, bool) external override {}

    function isApprovedForAll(
        address,
        address
    ) external pure override returns (bool) {
        return true;
    }

    function balanceOfBatch(
        address[] calldata,
        uint256[] calldata
    ) external pure override returns (uint256[] memory) {
        revert();
    }

    function safeTransferFrom(
        address,
        address,
        uint256,
        uint256,
        bytes calldata
    ) external override {}

    function safeBatchTransferFrom(
        address,
        address,
        uint256[] calldata,
        uint256[] calldata,
        bytes calldata
    ) external override {}

    function supportsInterface(
        bytes4 interfaceId
    ) external view override returns (bool) {}
}

contract BreedingSimulation is Ownable {
    MockERC20 public cock;
    MockERC721 public genesis;
    MockERC721 public legacy;
    MockERC1155 public items;

    constructor() Ownable(msg.sender) {
        cock = new MockERC20();
        genesis = new MockERC721("Genesis", "GNS", 1);
        legacy = new MockERC721("Legacy", "LEG", 2223);
        items = new MockERC1155();
    }

    function setupSimulation(address player) external onlyOwner {
        // Mint COCK tokens to player
        cock.mint(player, 1000 ether);

        // Mint Genesis chicken
        genesis.mint(player);

        // Mint Legacy chicken
        legacy.mint(player);

        // Mint breeding item (Potion)
        items.mint(player, 1, 5);
    }

    function showBalances(
        address player
    ) external view returns (uint256 cockBalance, uint256 potionBalance) {
        cockBalance = cock.balanceOf(player);
        potionBalance = items.balanceOf(player, 1);
    }

    function showChickens(
        address player
    )
        external
        view
        returns (uint256[] memory genesisOwned, uint256[] memory legacyOwned)
    {
        uint256 gCount = genesis.balanceOf(player);
        uint256 lCount = legacy.balanceOf(player);

        genesisOwned = new uint256[](gCount);
        legacyOwned = new uint256[](lCount);

        for (uint256 i = 0; i < gCount; i++) {
            genesisOwned[i] = genesis.tokenOfOwnerByIndex(player, i);
        }
        for (uint256 i = 0; i < lCount; i++) {
            legacyOwned[i] = legacy.tokenOfOwnerByIndex(player, i);
        }
    }
}

// Usage in tests:
// 1. Deploy BreedingSimulation
// 2. Call setupSimulation(player address)
// 3. Check balances and owned chickens
// 4. Integrate breeding logic to interact with SabongSagaBreeding

// Future additions: Attach SabongSagaBreeding contract, simulate breed() with mocked data, validate interactions.
