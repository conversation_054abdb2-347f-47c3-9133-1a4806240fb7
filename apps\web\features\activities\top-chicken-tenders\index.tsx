"use client";

import { Pagination } from "@/components/ui";
import React, { FC } from "react";

export const TopChichenTenders: FC = () => {
  return (
    <div className="mt-4">
      <div className="bg-[#1E1E1E] rounded-xl p-6">
        <table className="w-full text-left">
          <thead className="text-sm uppercase font-semibold text-zinc-500">
            <tr>
              <th className="py-3 px-5">RANK</th>
              <th className="py-3 px-5">PLAYER</th>
              <th className="py-3 px-5 text-right">ACCUMULATED FEATHERS</th>
            </tr>
          </thead>
          <tbody className="text-base text-zinc-200">
            <tr className="">
              <td className="py-2 px-5">123</td>
              <td className="py-2 px-5">you</td>
              <td className="py-2 px-5 text-right">123</td>
            </tr>
            <tr className="bg-[#5F4D15]">
              <td className="py-2 px-5">1</td>
              <td className="py-2 px-5">pos2.ron</td>
              <td className="py-2 px-5 text-right">23000</td>
            </tr>
            <tr className="bg-[#333331]">
              <td className="py-2 px-5">2</td>
              <td className="py-2 px-5">roikyuu.ron</td>
              <td className="py-2 px-5 text-right">22000</td>
            </tr>
            <tr className="bg-[#2B2821]">
              <td className="py-2 px-5">3</td>
              <td className="py-2 px-5">exidz.ron</td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">4</td>
              <td className="py-2 px-5">
                0x617c5d73662282ea7ffd231e020eca6d2b0d552f
              </td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">5</td>
              <td className="py-2 px-5">strag.ron</td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">6</td>
              <td className="py-2 px-5">
                0x617c5d73662282ea7ffd231e020eca6d2b0d552f.ron
              </td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">7</td>
              <td className="py-2 px-5">
                0x617c5d73662282ea7ffd231e020eca6d2b0d552f.ron
              </td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">8</td>
              <td className="py-2 px-5">
                0x617c5d73662282ea7ffd231e020eca6d2b0d552f
              </td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">9</td>
              <td className="py-2 px-5">
                0x617c5d73662282ea7ffd231e020eca6d2b0d552f
              </td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
            <tr className="">
              <td className="py-2 px-5">10</td>
              <td className="py-2 px-5">
                0x617c5d73662282ea7ffd231e020eca6d2b0d552f
              </td>
              <td className="py-2 px-5 text-right">21000</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="mt-6">
        <Pagination>
          <Pagination.List>
            <Pagination.Item variant="first" href="#" />
            <Pagination.Item variant="previous" href="#" />
            <Pagination.Item href="#" isCurrent>
              1
            </Pagination.Item>
            <Pagination.Item href="#">2</Pagination.Item>
            <Pagination.Item variant="ellipsis" />
            <Pagination.Item variant="next" href="#" />
            <Pagination.Item variant="last" href="#" />
          </Pagination.List>
        </Pagination>
      </div>
    </div>
  );
};
