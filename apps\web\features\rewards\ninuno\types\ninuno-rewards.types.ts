/**
 * Ninuno Rewards Types
 *
 * This file contains TypeScript interfaces for the Ninuno Rewards feature.
 */

/**
 * Interface for chicken information including accumulated rewards
 */
export interface IChickenInfo {
  tokenId: string;
  name: string;
  image: string;
  accumulatedRewards: number;
  lastUpdated: string;
}

/**
 * Enum for transfer status
 */
export enum ETransferStatus {
  PENDING = "PENDING",
  COMPLETED = "COMPLETED",
  FAILED = "FAILED",
}

/**
 * Interface for chicken selection state
 */
export interface IChickenSelection {
  [tokenId: string]: boolean;
}

/**
 * Interface for claim request data
 */
export interface IClaimRequest {
  address: string;
  withdrawalRequestId: number;
  claimAmount: string;
  signature?: string;
}
