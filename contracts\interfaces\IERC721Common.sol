// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.0;

interface IERC721Common {
    /**
     * @dev Bulk create new tokens for `_recipients`. Tokens ID will be automatically
     * assigned (and available on the emitted {IERC721Upgradeable-Transfer} event), and the token
     * URI autogenerated based on the base URI passed at construction.
     *
     * See {ERC721Upgradeable-_mint}.
     *
     * Requirements:
     *
     * - the caller must have the `MINTER_ROLE`.
     */
    function bulkMint(
        address[] calldata recipients
    ) external returns (uint256[] memory tokenIds);
}
