"use client";

import React from "react";
import { Breadcrumbs, Separator, Sidebar } from "@/components/ui";

export default function AppSidebarNav() {
  return (
    <Sidebar.Nav className="border-b border-b-[#191C21] !bg-dapp-sidebar">
      <span className="flex items-center gap-x-4">
        <Sidebar.Trigger className="-mx-2" />
        {/* <Separator className="hidden md:block h-6" orientation="vertical" />
        <Breadcrumbs className="md:flex hidden">
          <Breadcrumbs.Item href="/dapp/">Dashboard</Breadcrumbs.Item>
          <Breadcrumbs.Item>Newsletter</Breadcrumbs.Item>
        </Breadcrumbs> */}
      </span>
    </Sidebar.Nav>
  );
}
