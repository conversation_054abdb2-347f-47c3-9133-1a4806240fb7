"use client";

import { SVGProps } from "react";

export function WalletConnectIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      {/* Icon from Simple Icons by Simple Icons Collaborators - https://github.com/simple-icons/simple-icons/blob/develop/LICENSE.md */}
      <path
        fill="currentColor"
        d="M4.913 7.519c3.915-3.831 10.26-3.831 14.174 0l.471.461a.483.483 0 0 1 0 .694l-1.611 1.577a.25.25 0 0 1-.354 0l-.649-.634c-2.73-2.673-7.157-2.673-9.887 0l-.694.68a.255.255 0 0 1-.355 0L4.397 8.719a.48.48 0 0 1 0-.693zm17.506 3.263l1.434 1.404a.483.483 0 0 1 0 .694l-6.466 6.331a.51.51 0 0 1-.709 0l-4.588-4.493a.126.126 0 0 0-.178 0l-4.589 4.493a.51.51 0 0 1-.709 0L.147 12.88a.483.483 0 0 1 0-.694l1.434-1.404a.51.51 0 0 1 .709 0l4.589 4.493c.05.048.129.048.178 0l4.589-4.493a.51.51 0 0 1 .709 0l4.589 4.493c.05.048.128.048.178 0l4.589-4.493a.507.507 0 0 1 .708 0"
      ></path>
    </svg>
  );
}
