[{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "ErrUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "InvalidInitialization", "type": "error"}, {"inputs": [], "name": "NotInitializing", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "affectionPoints", "type": "uint256"}], "name": "AffectionGained", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "totalFeeds", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "successfulFeeds", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalAffectionGained", "type": "uint256"}], "name": "BatchFeedCompleted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "itemId", "type": "uint256"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeed.StatType", "name": "statBoosted", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "boostAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiryTime", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "affectionGained", "type": "uint256"}], "name": "Feed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "itemId", "type": "uint256"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeed.ItemType", "name": "itemType", "type": "uint8"}, {"indexed": false, "internalType": "enum SabongSagaDailyFeed.StatType", "name": "targetStat", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "boostAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "duration", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "affectionPoints", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "isRandom", "type": "bool"}], "name": "FeedItemConfigured", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "itemId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiryTime", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "immortalApplied", "type": "bool"}, {"indexed": false, "internalType": "uint256", "name": "affectionGained", "type": "uint256"}], "name": "FeedWithImmortalItem", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ImmortalExpired", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "version", "type": "uint64"}], "name": "Initialized", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "enum SabongSagaDailyFeed.StatType", "name": "statType", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "boostAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiryTime", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "boostIndex", "type": "uint256"}], "name": "StatBoostAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "enum SabongSagaDailyFeed.StatType", "name": "statType", "type": "uint8"}, {"indexed": false, "internalType": "uint256", "name": "boostIndex", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "expiredAmount", "type": "uint256"}], "name": "StatBoostExpired", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "CHICKENS", "outputs": [{"internalType": "contract IERC721", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "CONFIG_SETTER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "GENESIS", "outputs": [{"internalType": "contract IERC721", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PAUSER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RESOURCES", "outputs": [{"internalType": "contract ERC1155Burnable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256[]", "name": "itemIds", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}], "name": "batchFeedChicken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "itemId", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeed.ItemType", "name": "itemType", "type": "uint8"}, {"internalType": "enum SabongSagaDailyFeed.StatType", "name": "targetStat", "type": "uint8"}, {"internalType": "uint256", "name": "boostAmount", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "affectionPoints", "type": "uint256"}, {"internalType": "bool", "name": "isRandom", "type": "bool"}, {"internalType": "uint256", "name": "healthBoostAmount", "type": "uint256"}, {"internalType": "uint256", "name": "otherBoostAmount", "type": "uint256"}], "name": "configureFeedItem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "itemId", "type": "uint256"}], "name": "deactivateFeedItem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "itemId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "feedChicken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "enum SabongSagaDailyFeed.StatType", "name": "statType", "type": "uint8"}], "name": "getActiveStatBoosts", "outputs": [{"components": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "expiryTime", "type": "uint256"}, {"internalType": "uint256", "name": "itemId", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct SabongSagaDailyFeed.StatBoost[]", "name": "activeBoosts", "type": "tuple[]"}, {"internalType": "uint256", "name": "totalBoost", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getAllActiveBoosts", "outputs": [{"internalType": "uint256", "name": "cockrage", "type": "uint256"}, {"internalType": "uint256", "name": "ferocity", "type": "uint256"}, {"internalType": "uint256", "name": "attack", "type": "uint256"}, {"internalType": "uint256", "name": "speed", "type": "uint256"}, {"internalType": "uint256", "name": "defence", "type": "uint256"}, {"internalType": "uint256", "name": "evasion", "type": "uint256"}, {"internalType": "uint256", "name": "health", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getBaseStats", "outputs": [{"components": [{"internalType": "uint256", "name": "cockrage", "type": "uint256"}, {"internalType": "uint256", "name": "ferocity", "type": "uint256"}, {"internalType": "uint256", "name": "attack", "type": "uint256"}, {"internalType": "uint256", "name": "speed", "type": "uint256"}, {"internalType": "uint256", "name": "defence", "type": "uint256"}, {"internalType": "uint256", "name": "evasion", "type": "uint256"}, {"internalType": "uint256", "name": "health", "type": "uint256"}], "internalType": "struct SabongSagaDailyFeed.BaseStats", "name": "stats", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getChickenStatus", "outputs": [{"components": [{"internalType": "uint256", "name": "cockrage", "type": "uint256"}, {"internalType": "uint256", "name": "ferocity", "type": "uint256"}, {"internalType": "uint256", "name": "attack", "type": "uint256"}, {"internalType": "uint256", "name": "speed", "type": "uint256"}, {"internalType": "uint256", "name": "defence", "type": "uint256"}, {"internalType": "uint256", "name": "evasion", "type": "uint256"}, {"internalType": "uint256", "name": "health", "type": "uint256"}], "internalType": "struct SabongSagaDailyFeed.BaseStats", "name": "stats", "type": "tuple"}, {"internalType": "bool", "name": "isImmortal", "type": "bool"}, {"internalType": "uint256", "name": "immortalTimeLeft", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "itemId", "type": "uint256"}], "name": "getFeedItemConfig", "outputs": [{"components": [{"internalType": "enum SabongSagaDailyFeed.ItemType", "name": "itemType", "type": "uint8"}, {"internalType": "enum SabongSagaDailyFeed.StatType", "name": "targetStat", "type": "uint8"}, {"internalType": "uint256", "name": "boostAmount", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "affectionPoints", "type": "uint256"}, {"internalType": "bool", "name": "isRandom", "type": "bool"}, {"internalType": "bool", "name": "isActive", "type": "bool"}, {"internalType": "uint256", "name": "healthBoostAmount", "type": "uint256"}, {"internalType": "uint256", "name": "otherBoostAmount", "type": "uint256"}], "internalType": "struct SabongSagaDailyFeed.FeedItem", "name": "item", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getImmortalEffect", "outputs": [{"components": [{"internalType": "uint256", "name": "expiryTime", "type": "uint256"}, {"internalType": "bool", "name": "isActive", "type": "bool"}], "internalType": "struct SabongSagaDailyFeed.ImmortalEffect", "name": "immortal", "type": "tuple"}, {"internalType": "bool", "name": "isExpired", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getRoleMember", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMemberCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleMembers", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_RESOURCES", "type": "address"}, {"internalType": "address", "name": "_GENESIS", "type": "address"}, {"internalType": "address", "name": "_CHICKENS", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "isChickenImmortal", "outputs": [{"internalType": "bool", "name": "isImmortal", "type": "bool"}, {"internalType": "uint256", "name": "timeUntilExpiry", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "maxActiveBoostedStats", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "enum SabongSagaDailyFeed.CollectionType", "name": "collection", "type": "uint8"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"components": [{"internalType": "uint256", "name": "cockrage", "type": "uint256"}, {"internalType": "uint256", "name": "ferocity", "type": "uint256"}, {"internalType": "uint256", "name": "attack", "type": "uint256"}, {"internalType": "uint256", "name": "speed", "type": "uint256"}, {"internalType": "uint256", "name": "defence", "type": "uint256"}, {"internalType": "uint256", "name": "evasion", "type": "uint256"}, {"internalType": "uint256", "name": "health", "type": "uint256"}], "internalType": "struct SabongSagaDailyFeed.BaseStats", "name": "stats", "type": "tuple"}], "name": "setBaseStats", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_max", "type": "uint256"}], "name": "setMaxActiveBoostedStats", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]