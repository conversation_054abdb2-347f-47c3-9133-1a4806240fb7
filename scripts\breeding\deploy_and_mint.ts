import { ethers } from "hardhat";
import fs from "fs";
import path from "path";
import abi from "./abi.json";
import chalk from "chalk";

const TREASURY_ADDRESS = "******************************************";
const SIGNER_ADDRESS = "******************************************";
const TEST_WALLET = "******************************************";

// Helper function to create progress bar
function createProgressBar(
  current: number,
  total: number,
  length: number = 40
): string {
  const percentage = current / total;
  const filled = Math.round(length * percentage);
  const empty = length - filled;
  const bar = "█".repeat(filled) + "░".repeat(empty);
  return `${bar} ${Math.round(percentage * 100)}%`;
}

// Helper function to format numbers with commas
function formatNumber(num: number): string {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Helper function to print minting statistics
function printMintingStats(
  tokenType: string,
  current: number,
  total: number,
  batchSize: number,
  batchNumber: number,
  totalBatches: number,
  startTime: number
) {
  const elapsedTime = (Date.now() - startTime) / 1000; // in seconds
  const avgTimePerBatch = elapsedTime / batchNumber;
  const remainingBatches = totalBatches - batchNumber;
  const estimatedTimeRemaining = remainingBatches * avgTimePerBatch;

  console.clear();
  console.log(chalk.cyan(`\n🪙 ${tokenType} Minting Progress:`));
  console.log(chalk.white(createProgressBar(current, total)));
  console.log(chalk.yellow("\n📊 Statistics:"));
  console.log(
    chalk.white(
      `• Minted: ${formatNumber(current)} / ${formatNumber(total)} tokens`
    )
  );
  console.log(chalk.white(`• Current Batch: ${batchNumber} / ${totalBatches}`));
  console.log(chalk.white(`• Batch Size: ${formatNumber(batchSize)} tokens`));
  console.log(chalk.white(`• Time Elapsed: ${elapsedTime.toFixed(1)}s`));
  console.log(
    chalk.white(
      `• Estimated Time Remaining: ${estimatedTimeRemaining.toFixed(1)}s`
    )
  );
  console.log(
    chalk.white(`• Average Time per Batch: ${avgTimePerBatch.toFixed(1)}s`)
  );
}

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Deploying contracts with the account:", deployer.address);
  const accountBalance = await deployer.provider.getBalance(deployer.address);
  console.log("Account balance:", accountBalance.toString());

  // Deploy Genesis Contract
  const ERC721Sale = await ethers.getContractFactory("ERC721Sale");
  const genesisContract = await ERC721Sale.deploy(
    TREASURY_ADDRESS,
    "Sabong Saga Genesis",
    "SSGENESIS",
    "https://chicken-api-ivory.vercel.app/api/"
  );
  await genesisContract.waitForDeployment();
  const genesisAddress = await genesisContract.getAddress();
  console.log("Genesis Contract Address:", genesisAddress);

  // Deploy Legacy Contract Implementation
  const SabongSagaChickens = await ethers.getContractFactory(
    "SabongSagaChickens"
  );
  const legacyImplementation = await SabongSagaChickens.deploy();
  await legacyImplementation.waitForDeployment();

  // Deploy Proxy Admin
  const ProxyAdmin = await ethers.getContractFactory("ProxyAdmin");
  const proxyAdmin = await ProxyAdmin.deploy(deployer.address);
  await proxyAdmin.waitForDeployment();

  // Prepare initialization data
  const initData = SabongSagaChickens.interface.encodeFunctionData(
    "initialize",
    [
      "Sabong Saga Legacy",
      "SSLEGACY",
      "https://chicken-api-ivory.vercel.app/api/",
    ]
  );

  // Deploy Transparent Proxy
  const TransparentUpgradeableProxy = await ethers.getContractFactory(
    "TransparentUpgradeableProxy"
  );
  const proxy = await TransparentUpgradeableProxy.deploy(
    await legacyImplementation.getAddress(),
    await proxyAdmin.getAddress(),
    initData
  );
  await proxy.waitForDeployment();

  // Get the proxy address and create a contract instance
  const legacyAddress = await proxy.getAddress();
  console.log("Legacy Contract Address:", legacyAddress);
  console.log("Legacy Contract Initialized");

  // Deploy Items Contract
  const ERC1155Common = await ethers.getContractFactory("ERC1155Common");
  const itemsContract = await ERC1155Common.deploy(
    TREASURY_ADDRESS,
    "Sabong Saga Items",
    "SSITEMS",
    "https://item-api-beta.vercel.app/api/"
  );
  await itemsContract.waitForDeployment();
  const itemsAddress = await itemsContract.getAddress();
  console.log("Items Contract Address:", itemsAddress);

  // Deploy COCK Token Contract
  const ERC20Common = await ethers.getContractFactory("ERC20Common");
  const cockContract = await ERC20Common.deploy(
    TREASURY_ADDRESS,
    TREASURY_ADDRESS,
    TREASURY_ADDRESS,
    true,
    "COCK",
    "COCK",
    18
  );
  await cockContract.waitForDeployment();
  const cockAddress = await cockContract.getAddress();
  console.log("COCK Token Address:", cockAddress);

  // Deploy Breeding Contract
  const SabongSagaBreeding = await ethers.getContractFactory(
    "SabongSagaBreeding"
  );
  const breedingContract = await SabongSagaBreeding.deploy(
    cockAddress,
    genesisAddress,
    legacyAddress,
    itemsAddress,
    TREASURY_ADDRESS,
    SIGNER_ADDRESS
  );
  await breedingContract.waitForDeployment();
  const breedingAddress = await breedingContract.getAddress();
  console.log("Breeding Contract Address:", breedingAddress);

  // Grant MINTER_ROLE to breeding contract
  const MINTER_ROLE = ethers.keccak256(ethers.toUtf8Bytes("MINTER_ROLE"));
  const legacyContractWithAccess = await ethers.getContractAt(
    [
      "function grantRole(bytes32,address)",
      ...SabongSagaChickens.interface.fragments,
    ],
    legacyAddress,
    deployer
  );
  await legacyContractWithAccess.grantRole(MINTER_ROLE, breedingAddress);
  console.log("Granted MINTER_ROLE to breeding contract");

  // Mint initial assets for testing
  console.log("\nMinting initial assets to:", TEST_WALLET);

  // Mint Genesis NFTs in batches
  console.log(chalk.green("\n🚀 Starting Genesis NFT Minting Process"));
  const GENESIS_TOTAL = 2222;
  const GENESIS_BATCH_SIZE = 100;
  const genesisStartTime = Date.now();
  const genesisTotalBatches = Math.ceil(GENESIS_TOTAL / GENESIS_BATCH_SIZE);

  for (
    let i = 0, batchNum = 1;
    i < GENESIS_TOTAL;
    i += GENESIS_BATCH_SIZE, batchNum++
  ) {
    const batchSize = Math.min(GENESIS_BATCH_SIZE, GENESIS_TOTAL - i);
    const tx = await genesisContract.mintPresale(
      TEST_WALLET,
      BigInt(batchSize),
      ethers.toUtf8Bytes("")
    );
    await tx.wait();

    printMintingStats(
      "Genesis NFTs",
      Math.min(i + batchSize, GENESIS_TOTAL),
      GENESIS_TOTAL,
      batchSize,
      batchNum,
      genesisTotalBatches,
      genesisStartTime
    );
  }
  console.log(chalk.green("\n✅ Genesis NFT Minting Completed"));

  // Mint Legacy NFTs in batches
  console.log(chalk.green("\n🚀 Starting Legacy NFT Minting Process"));
  const LEGACY_TOTAL = 8888;
  const LEGACY_BATCH_SIZE = 100;
  const legacyStartTime = Date.now();
  const legacyTotalBatches = Math.ceil(LEGACY_TOTAL / LEGACY_BATCH_SIZE);

  for (
    let i = 0, batchNum = 1;
    i < LEGACY_TOTAL;
    i += LEGACY_BATCH_SIZE, batchNum++
  ) {
    const batchSize = Math.min(LEGACY_BATCH_SIZE, LEGACY_TOTAL - i);
    const tx = await legacyContractWithAccess.mintLaunchpad(
      TEST_WALLET,
      BigInt(batchSize),
      ethers.toUtf8Bytes("")
    );
    await tx.wait();

    printMintingStats(
      "Legacy NFTs",
      Math.min(i + batchSize, LEGACY_TOTAL),
      LEGACY_TOTAL,
      batchSize,
      batchNum,
      legacyTotalBatches,
      legacyStartTime
    );
  }
  console.log(chalk.green("\n✅ Legacy NFT Minting Completed"));

  // Final Summary
  console.log(chalk.cyan("\n📈 Minting Summary:"));
  console.log(
    chalk.white(`• Total Genesis NFTs Minted: ${formatNumber(GENESIS_TOTAL)}`)
  );
  console.log(
    chalk.white(`• Total Legacy NFTs Minted: ${formatNumber(LEGACY_TOTAL)}`)
  );
  console.log(
    chalk.white(
      `• Total NFTs Minted: ${formatNumber(GENESIS_TOTAL + LEGACY_TOTAL)}`
    )
  );

  // Mint Items
  let tx = await itemsContract.bulkMint(
    1,
    [TEST_WALLET],
    [BigInt(10)],
    [ethers.toUtf8Bytes("")]
  );
  await tx.wait();
  console.log("Minted 10 Items");

  // Mint COCK tokens
  tx = await cockContract.mint(TEST_WALLET, ethers.parseEther("1000000"));
  await tx.wait();
  console.log("Minted 1,000,000 COCK tokens");

  // Update abi.json with new addresses and ABIs
  const updatedAbi = {
    ...abi,
    sabong_saga_breeding_address: breedingAddress,
    sabong_saga_genesis_address: genesisAddress,
    sabong_saga_legacy_address: legacyAddress,
    sabong_saga_items_address: itemsAddress,
    sabong_saga_cock_address: cockAddress,
  };

  // Write updated ABI to file
  const abiPath = path.join(__dirname, "abi.json");
  fs.writeFileSync(abiPath, JSON.stringify(updatedAbi, null, 2), "utf8");
  console.log("\nUpdated abi.json with new addresses and ABIs");

  // Log all deployed addresses
  console.log("\nDeployed Contract Addresses:");
  console.log("----------------------------");
  console.log("Genesis:", genesisAddress);
  console.log("Legacy:", legacyAddress);
  console.log("Items:", itemsAddress);
  console.log("COCK Token:", cockAddress);
  console.log("Breeding:", breedingAddress);
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("❌ Error:"), error);
    process.exit(1);
  });
