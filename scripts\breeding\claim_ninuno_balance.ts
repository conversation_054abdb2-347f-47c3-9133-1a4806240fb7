import { ethers } from "hardhat";
import abi from "./abi.json";

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Executing script with the account:", deployer.address);

  const breeding = new ethers.Contract(
    abi.sabong_saga_breeding_address,
    abi.sabong_saga_breeding,
    deployer
  );

  const cock = new ethers.Contract(
    abi.sabong_saga_cock_address,
    abi.erc20_common,
    deployer
  );

  const balanceOfBreeding = await cock.balanceOf(breeding.address);
  const messageHash = ethers.solidityPackedKeccak256(
    ["address", "uint256", "uint256"],
    [deployer.address, 1, balanceOfBreeding]
  );
  let signature = await deployer.signMessage(ethers.getBytes(messageHash));

  let tx = await breeding.claimNinunoBalance(1, balanceOfBreeding, signature);
  await tx.wait();
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
