"use client";

import CraftButton from "@/components/shared/crafting/craft-button";
import CraftingRequirements from "@/components/shared/crafting/crafting-requirements";
import ItemGrid from "@/components/shared/crafting/item-grid";
import QuantitySelector from "@/components/shared/crafting/quantity-selector";
import AppNavbar from "@/components/shared/navbar";
import TokenBalance from "@/components/shared/token-balance";
import WalletBalances from "@/components/shared/wallet-balances";
import { Badge, Button, Input } from "@/components/ui";
import useAuthStore from "@/store/auth";
import useFoodCraftingStore from "@/store/food-crafting";
import { delay } from "@/utils/delay";
import { useCallback, useEffect, useMemo, useState } from "react";
import { formatEther } from "viem";
import CraftingAnim from "./crafting-anim";
import { CraftingTab } from "./tab";

import items from "@/data/crafting_data.json";
import { CraftingRecipe, CraftResult } from "@/types/crafting";

export default function CraftingPage() {
  const [activeTab, setActiveTab] = useState(0);
  const [selectedItem, setSelectedItem] = useState<number | null>(0);
  const [quantity, setQuantity] = useState(0);
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(100000);
  const [craftResult, setCraftResult] = useState<{
    tokenIds: bigint[];
    amounts: bigint[];
  } | null>();
  const [showCraftPopup, setShowCraftPopup] = useState(false);
  const [show, setShow] = useState(false);

  const { feathers, cock, legendaryFeathers } = useAuthStore();
  const {
    randomCockPrice,
    randomFeatherPrice,
    craftFoods,

    isPending,
    foodBalances,
    fetchingPrice,
    craftItems,
    craftableItems,
    fetchCraftingRecipes,
  } = useFoodCraftingStore();

  useEffect(() => {
    fetchCraftingRecipes();
  }, []);

  // Helper function to get recipe for an item
  const getRecipeForItem = (itemId: number) => {
    return craftableItems.find(
      (recipe) => recipe.tokenId === itemId && recipe.exists
    );
  };

  const randomCookieCraftPrice = useMemo(() => {
    if (randomCockPrice && randomFeatherPrice) {
      return {
        cockPrice: Number(formatEther(randomCockPrice)),
        featherPrice: Number(randomFeatherPrice),
      };
    }
    return { cockPrice: 0, featherPrice: 0 };
  }, [randomCockPrice, randomFeatherPrice]);

  const craftRandomCookie = useCallback(async () => {
    try {
      const returnedData = await craftFoods(quantity, referralCode ?? "");
      // Check that we have valid data with amounts and tokenIds

      if (
        returnedData &&
        returnedData.amounts != null &&
        returnedData.tokenIds
      ) {
        // Set the craft result and open the pop up
        if (returnedData) {
          setShow(true);
          setCraftResult(returnedData);
          await delay(5000);
          setShow(false);
          await delay(1000);
          setShowCraftPopup(true);
        }
      }
    } catch (error) {}
  }, [quantity, craftFoods]);

  const craftItemHandler = useCallback(async () => {
    try {
      if (selectedItemId != null) {
        const returnedData = await craftItems(
          selectedItemId,
          quantity,
          referralCode ?? ""
        );

        if (
          returnedData &&
          returnedData.amounts != null &&
          returnedData.tokenIds
        ) {
          // Set the craft result and open the pop up
          if (returnedData) {
            setShow(true);
            setCraftResult(returnedData);
            await delay(5000);
            setShow(false);
            await delay(1000);
            setShowCraftPopup(true);
          }
        }
      }
    } catch (error) {}
  }, [craftItems, quantity, selectedItemId]);

  const enoughBalance = useMemo(() => {
    if (quantity > 0) {
      const cockCost = randomCookieCraftPrice.cockPrice * quantity;
      const featherCost = randomCookieCraftPrice.featherPrice * quantity;
      // Ensure that both balances are sufficient

      if (
        Number(formatEther(cock)) >= cockCost &&
        Number(feathers) >= featherCost
      ) {
        return true;
      }
    }
    return false;
  }, [feathers, cock, quantity, randomCookieCraftPrice]);

  // Check if user has enough materials for recipe-based crafting
  const enoughRecipeMaterials = useMemo(() => {
    if (selectedItem === null || quantity <= 0) return false;

    const item = items[selectedItem];
    if (!item || item.id === 100000) return false; // Skip random cookie

    const recipe = getRecipeForItem(item.id);
    if (!recipe) return false;

    // Check each material requirement
    return recipe.materials.every((material) => {
      const requiredAmount = material.amount * BigInt(quantity);

      if (material.tokenType === 0) {
        // ERC20 token
        if (
          material.tokenAddress.toLowerCase() ===
          process.env.NEXT_PUBLIC_COCK_CONTRACT?.toLowerCase()
        ) {
          return cock >= requiredAmount;
        }
        // Add other ERC20 token checks here
        return false;
      } else {
        // ERC1155 token
        if (
          material.tokenAddress.toLowerCase() ===
          process.env.NEXT_PUBLIC_FEATHERS_CONTRACT?.toLowerCase()
        ) {
          if (material.tokenId === 1n) {
            return BigInt(feathers) >= requiredAmount;
          }
          if (material.tokenId === 2n) {
            return BigInt(legendaryFeathers) >= requiredAmount;
          }
        }
        // Check food balances for other ERC1155 tokens
        const tokenId = Number(material.tokenId);
        const userBalance = foodBalances[tokenId] || 0n;
        return userBalance >= requiredAmount;
      }
    });
  }, [selectedItem, quantity, craftableItems, cock, feathers, foodBalances]);

  const handleQuantityChange = (value: number) => {
    setQuantity(value);
  };

  const incrementQuantity = () =>
    setQuantity((prev) => (prev < 100 ? prev + 1 : prev));
  const decrementQuantity = () =>
    setQuantity((prev) => (prev > 0 ? prev - 1 : 0));

  useEffect(() => {
    if (typeof window !== "undefined") {
      // Access localStorage only on the client side
      setReferralCode(localStorage.getItem("referralCode"));
    }
  }, []);

  return (
    <div className="min-h-screen relative bg-gradient-to-b from-stone-900 via-black to-stone-900 overflow-hidden">
      <CraftingAnim show={show} />

      <div className="sticky top-0 z-20 bg-stone-900/80 backdrop-blur-sm border-b border-primary/10">
        <AppNavbar />
      </div>

      <div className="relative max-w-[1680px] w-full mx-auto mt-6 px-4 md:px-6 lg:px-8">
        <div className="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <h1 className="font-bold mb-2 md:mb-0 font-Arcadia text-primary text-3xl md:text-4xl">
            Crafting
          </h1>

          <WalletBalances>
            <TokenBalance
              iconSrc="/images/feathers.png"
              alt="Feathers"
              balance={Number(feathers).toLocaleString() || 0}
            />
            <TokenBalance
              iconSrc="/images/legendary-feathers.png"
              alt="Legendary Feathers"
              balance={Number(legendaryFeathers).toLocaleString() || 0}
            />
            <TokenBalance
              iconSrc="/images/COCK_TOKEN_BLUE.webp"
              alt="Token"
              balance={
                Number(formatEther(cock)).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                }) || "0.00"
              }
            />
          </WalletBalances>
        </div>

        <div className="flex gap-2 w-full items-center mb-6">
          <CraftingTab activeTab={activeTab} setActiveTab={setActiveTab} />
        </div>

        <div className="flex flex-col lg:flex-row gap-6">
          <div className="w-full lg:w-3/5 bg-stone-900/50 rounded-lg border border-primary/5 p-4">
            <h2 className="text-lg font-medium text-primary/80 mb-3">
              Inventory
            </h2>

            <div className="max-h-[550px] overflow-y-auto pr-2 custom-scrollbar">
              <ItemGrid
                items={items}
                selectedItem={selectedItem}
                setSelectedItemId={setSelectedItemId}
                onSelectItem={setSelectedItem}
                balances={foodBalances}
              />
            </div>
          </div>

          <div className="w-full lg:w-2/5">
            {selectedItem !== null &&
            selectedItem >= 0 &&
            selectedItemId != null &&
            selectedItem < items.length ? (
              <div className="bg-stone-900 rounded-lg border border-primary/10 p-6 h-full">
                <div className="flex items-start gap-4 mb-4">
                  <img
                    src={items[selectedItem]?.image}
                    alt={items[selectedItem]?.name}
                    className="w-20 h-20 object-contain bg-stone-800/50 rounded-lg p-2"
                  />

                  <div>
                    <div className="flex flex-wrap gap-2 mb-1">
                      {items[selectedItem]?.craftable ||
                      getRecipeForItem(selectedItemId!) ? (
                        <Badge intent="success">Craftable</Badge>
                      ) : (
                        <Badge intent="danger">Not Yet Craftable</Badge>
                      )}

                      {selectedItem !== undefined &&
                        items[selectedItem]?.item &&
                        items[selectedItem]?.id !== undefined && (
                          <Badge intent="secondary">
                            Storage:{" "}
                            {(() => {
                              const id = items[selectedItem]?.id;
                              return id !== undefined
                                ? foodBalances[id]?.toString() || "0"
                                : "0";
                            })()}
                          </Badge>
                        )}
                    </div>

                    <h2 className="text-xl font-bold text-primary/90">
                      {items[selectedItem]?.name}
                    </h2>

                    <p className="text-sm text-white/70 mt-1">
                      {items[selectedItem]?.description}
                    </p>
                  </div>
                </div>

                <div className="h-px w-full bg-gradient-to-r from-transparent via-primary/30 to-transparent my-4"></div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  {(() => {
                    // Only proceed if we have a valid selected item
                    if (selectedItem === undefined || !items[selectedItem])
                      return null;

                    const item = items[selectedItem];

                    return (
                      <>
                        {item?.effect && item.effect.length > 0 && (
                          <div className="bg-stone-800/30 p-3 rounded-lg">
                            <p className="font-medium text-primary/80 mb-1">
                              Effects:
                            </p>
                            <ul className="space-y-1">
                              {item.effect.map((effect, idx) => (
                                <li
                                  key={idx}
                                  className="text-sm text-white/70 flex items-start"
                                >
                                  <span className="text-primary mr-1">•</span>{" "}
                                  {effect}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {item?.disliked && item.disliked.length > 0 && (
                          <div className="bg-stone-800/30 p-3 rounded-lg">
                            <p className="font-medium text-primary/80 mb-1">
                              Disliked by:
                            </p>
                            <ul className="space-y-1">
                              {item.disliked.map((instinct, idx) => (
                                <li
                                  key={idx}
                                  className="text-sm text-white/70 flex items-start"
                                >
                                  <span className="text-red-400 mr-1">•</span>{" "}
                                  {instinct}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>

                {/* Updated Crafting Section */}
                {(() => {
                  const item = items[selectedItem];
                  if (!item) return null;

                  // Check if this is the random cookie (special case)
                  if (item.id === 100000) {
                    return (
                      item.craftable && (
                        <div className="bg-stone-800/50 rounded-lg p-4 border border-primary/10">
                          <p className="font-medium text-primary/80 mb-3">
                            Crafting Requirements
                          </p>

                          <CraftingRequirements
                            featherPrice={
                              quantity != 0
                                ? randomCookieCraftPrice.featherPrice * quantity
                                : randomCookieCraftPrice.featherPrice
                            }
                            cockPrice={
                              quantity != 0
                                ? randomCookieCraftPrice.cockPrice * quantity
                                : randomCookieCraftPrice.cockPrice
                            }
                            className="mb-4"
                          />

                          <div className="flex flex-col gap-1 mb-4 md:mb-3">
                            <p className="text-muted-fg text-sm ">
                              Do you have a referral code? Enter it here!
                            </p>
                            <Input
                              value={referralCode ?? ""}
                              onChange={(e) => setReferralCode(e.target.value)}
                              className={
                                "border border-border w-full md:w-1/2 rounded-md"
                              }
                            />
                          </div>

                          <div className="flex flex-col gap-3">
                            <div className="flex items-center justify-between">
                              <p className="text-white/80">
                                Quantity to craft:
                              </p>
                              <QuantitySelector
                                quantity={quantity}
                                onChange={handleQuantityChange}
                                onIncrement={incrementQuantity}
                                onDecrement={decrementQuantity}
                                max={100}
                              />
                            </div>

                            {!enoughBalance && quantity > 0 && (
                              <p className="text-red-400 text-sm flex items-center gap-1">
                                <span className="inline-block w-4 h-4">⚠️</span>{" "}
                                Not enough resources
                              </p>
                            )}

                            <CraftButton
                              isPending={isPending}
                              isDisabled={
                                quantity <= 0 || !enoughBalance || isPending
                              }
                              onPress={craftRandomCookie}
                              className="mt-2"
                            />
                          </div>
                        </div>
                      )
                    );
                  }

                  // For regular items, check if recipe exists
                  const recipe = getRecipeForItem(item.id);

                  if (!recipe) {
                    return null; // No recipe available
                  }

                  return (
                    <div className="bg-stone-800/50 rounded-lg p-4 border border-primary/10">
                      <p className="font-medium text-primary/80 mb-3">
                        Crafting Requirements
                      </p>

                      {/* Display recipe materials */}
                      <div className=" flex gap-1 items-center mb-4">
                        {recipe.materials.map((material, index) => (
                          <div
                            key={index}
                            className="flex items-center w-fit bg-stone-700/30 p-3 rounded-lg"
                          >
                            <div className="flex items-center gap-1">
                              {/* Material icon based on token address */}
                              <img
                                src={
                                  material.tokenAddress.toLowerCase() ===
                                  process.env.NEXT_PUBLIC_COCK_CONTRACT?.toLowerCase()
                                    ? "/images/COCK_TOKEN_BLUE.webp"
                                    : material.tokenAddress.toLowerCase() ===
                                        process.env.NEXT_PUBLIC_FEATHERS_CONTRACT?.toLowerCase()
                                      ? Number(material.tokenId) != 1
                                        ? "/images/legendary-feathers.png"
                                        : "/images/feathers.png"
                                      : `https://sabong-saga-resources.s3.ap-southeast-1.amazonaws.com/${material.tokenId}.png` // fallback
                                }
                                alt="Material"
                                className="w-auto h-8 object-contain"
                              />

                              <p className="font-Poppins">
                                {material.tokenType === 0
                                  ? Number(
                                      formatEther(
                                        material.amount * BigInt(quantity || 1)
                                      )
                                    ).toLocaleString()
                                  : (
                                      material.amount * BigInt(quantity || 1)
                                    ).toString()}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>

                      <div className="flex flex-col gap-1 mb-4 md:mb-3">
                        <p className="text-muted-fg text-sm ">
                          Do you have a referral code? Enter it here!
                        </p>
                        <Input
                          value={referralCode ?? ""}
                          onChange={(e) => setReferralCode(e.target.value)}
                          className={
                            "border border-border w-full md:w-1/2 rounded-md"
                          }
                        />
                      </div>

                      <div className="flex flex-col gap-3">
                        <div className="flex items-center justify-between">
                          <p className="text-white/80">Quantity to craft:</p>
                          <QuantitySelector
                            quantity={quantity}
                            onChange={handleQuantityChange}
                            onIncrement={incrementQuantity}
                            onDecrement={decrementQuantity}
                            max={100}
                          />
                        </div>

                        {!enoughRecipeMaterials && quantity > 0 && (
                          <p className="text-red-400 text-sm flex items-center gap-1">
                            <span className="inline-block w-4 h-4">⚠️</span>
                            Not enough materials
                          </p>
                        )}

                        <CraftButton
                          isPending={isPending}
                          isDisabled={
                            quantity <= 0 || !enoughRecipeMaterials || isPending
                          }
                          onPress={craftItemHandler}
                          className="mt-2"
                        />
                      </div>
                    </div>
                  );
                })()}
              </div>
            ) : (
              <div className="bg-stone-900/30 rounded-lg border border-dashed border-primary/20 p-6 h-full flex flex-col items-center justify-center text-center">
                <span className="text-4xl mb-3">🔍</span>
                <h3 className="text-lg font-medium text-primary/70">
                  Select an item
                </h3>
                <p className="text-sm text-white/50 mt-1">
                  Choose an item from the list to view details and craft
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Improved crafting result popup */}
      {showCraftPopup && craftResult && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/80 backdrop-blur-md z-50 p-4">
          <div className="bg-gradient-to-b from-stone-800 to-stone-900 rounded-xl shadow-2xl max-w-3xl w-full border border-primary/30 overflow-hidden">
            <div className="bg-primary/10 p-4 flex items-center justify-between">
              <h2 className="text-2xl font-Arcadia text-primary">
                Crafting Results
              </h2>
              <Button
                intent="danger"
                onPress={() => {
                  setShowCraftPopup(false);
                  setCraftResult(null);
                }}
                className="w-8 h-8 rounded-full"
              >
                <span>×</span>
              </Button>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-6">
                {(() => {
                  const combinedResults: Record<number, CraftResult> = {};

                  craftResult.tokenIds.forEach((tokenId, index) => {
                    const itemId = Number(tokenId) % 9;
                    const amount = craftResult.amounts[index] || 1n;

                    if (combinedResults[itemId]) {
                      combinedResults[itemId].amount += amount;
                    } else {
                      combinedResults[itemId] = {
                        tokenId,
                        amount,
                        item: items.find((i) => i.id === itemId),
                      };
                    }
                  });

                  return Object.values(combinedResults).map((result, index) => (
                    <div
                      key={index}
                      className="flex flex-col items-center bg-stone-800/70 p-4 rounded-lg border border-primary/20 hover:border-primary/40 transition-all"
                    >
                      <div className="relative mb-3">
                        <div className="absolute inset-0 bg-primary/10 rounded-full animate-pulse opacity-30"></div>
                        <img
                          src={result.item?.image || "/images/cookie-1.jpg"}
                          alt={result.item?.name || "Cookie"}
                          className="w-20 h-20 object-contain relative z-10"
                        />
                      </div>

                      <span className="text-primary font-medium text-center">
                        {result.item?.name || `Cookie #${result.tokenId}`}
                      </span>

                      <span className="text-white/70 text-sm mt-1 bg-stone-700/50 px-2 py-0.5 rounded-full">
                        x{result.amount.toString()}
                      </span>
                    </div>
                  ));
                })()}
              </div>

              <div className="flex justify-center">
                <Button
                  intent="primary"
                  onPress={() => {
                    setShowCraftPopup(false);
                    setCraftResult(null);
                  }}
                  className="px-8 py-2 font-medium"
                >
                  Continue Crafting
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
