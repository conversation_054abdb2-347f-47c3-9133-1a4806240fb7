"use client";

import axios from "@/lib/api";
import { useQuery } from "@tanstack/react-query";
import { IBlockchainConfig } from "../types/blockchain.types";

// fetcher
const fetchBlockchain = async () => {
  const { data } = await axios.get<IBlockchainConfig>("/blockchain");
  return data;
};

// hooks
const useBlockchain = () => {
  const blockchainQuery = useQuery({
    queryKey: ["blockchain"],
    queryFn: fetchBlockchain,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return { blockchainQuery };
};

export default useBlockchain;
