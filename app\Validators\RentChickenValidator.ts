import { schema, CustomMessages } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ValidatorReporter } from './Reporters/ValidatorReporter'

export default class RentChickenValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    rentalId: schema.number(),
  })

  public reporter = ValidatorReporter

  public messages: CustomMessages = {
    required: '{{ field }} is required',
    number: '{{ field }} is not a valid number',
  }
}
