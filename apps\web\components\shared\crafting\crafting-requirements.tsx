"use client";

import TokenBalance from "../token-balance";
import { cn } from "@/components/ui";

interface CraftingRequirementsProps {
  featherPrice: number;
  cockPrice: number;
  className?: string;
}

export default function CraftingRequirements({
  featherPrice,
  cockPrice,
  className,
}: CraftingRequirementsProps) {
  return (
    <div className={cn("flex gap-3", className)}>
      <div className="bg-stone-800 p-2 rounded-lg">
        <TokenBalance
          iconSrc="/images/feathers.png"
          alt="Feathers"
          balance={featherPrice ?? 0}
          textClassName="font-medium"
        />
      </div>

      <div className="bg-stone-800 p-2 rounded-lg">
        <TokenBalance
          iconSrc="/images/COCK_TOKEN_BLUE.webp"
          alt="Token"
          balance={cockPrice ?? 0}
          textClassName="font-medium"
        />
      </div>
    </div>
  );
}
