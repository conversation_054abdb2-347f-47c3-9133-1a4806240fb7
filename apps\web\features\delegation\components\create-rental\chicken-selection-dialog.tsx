"use client";

import { Search, Star, WifiOff, ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useMemo, useState, useEffect } from "react";
import { <PERSON>ton, Modal, TextField, cn } from "ui";
import {
  useChickensForDelegation,
  IChickenForDelegation,
} from "../../hooks/useChickensForDelegation";

interface IChickenSelectionDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  onSelect: (chicken: IChickenForDelegation) => void;
  selectedChickenId?: number | null;
}

export function ChickenSelectionDialog({
  isOpen,
  onOpenChange,
  onSelect,
  selectedChickenId,
}: IChickenSelectionDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("genesis");
  const [currentPage, setCurrentPage] = useState(1);

  const ITEMS_PER_PAGE = 12;

  // Use the real hook to fetch chickens
  const { chickens, isLoading, error, isConnected } =
    useChickensForDelegation();

  const getTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case "genesis":
        return "bg-orange-500 text-white shadow-lg shadow-orange-500/30";
      case "legacy":
        return "bg-blue-500 text-white shadow-lg shadow-blue-500/30";
      case "ordinary":
        return "bg-gray-600 text-white shadow-lg shadow-gray-600/30";
      default:
        return "bg-gray-600 text-white shadow-lg shadow-gray-600/30";
    }
  };

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, filterType]);

  const filteredChickens = useMemo(() => {
    return chickens.filter((chicken) => {
      const matchesSearch =
        chicken.tokenId.toString().includes(searchQuery) ||
        chicken.metadata?.name
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        chicken.metadata?.nickname
          ?.toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        chicken.type?.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesFilter =
        chicken.type?.toLowerCase() === filterType.toLowerCase();

      return matchesSearch && matchesFilter;
    });
  }, [chickens, searchQuery, filterType]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredChickens.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const currentPageChickens = filteredChickens.slice(
    startIndex,
    startIndex + ITEMS_PER_PAGE
  );

  // Results display text
  const startItem = filteredChickens.length === 0 ? 0 : startIndex + 1;
  const endItem = Math.min(
    startIndex + ITEMS_PER_PAGE,
    filteredChickens.length
  );
  const resultsText =
    filteredChickens.length === 0
      ? "No chickens found"
      : `Showing ${startItem}-${endItem} of ${filteredChickens.length} chickens`;

  const handleSelect = (chicken: IChickenForDelegation) => {
    if (!chicken.isAvailable) return;
    onSelect(chicken);
    onOpenChange(false);
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <Modal.Content
        size="5xl"
        classNames={{
          content: "max-h-none h-auto",
          overlay: "overflow-y-auto",
        }}
      >
        <Modal.Header>
          <Modal.Title>Select a Chicken to List</Modal.Title>
          <Modal.Description>
            Choose which chicken you want to rent out to other players
          </Modal.Description>
        </Modal.Header>

        <Modal.Body className="space-y-6">
          {/* Search Section */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 z-10" />
            <TextField
              placeholder="Search chickens by name, rarity, or token ID..."
              value={searchQuery}
              onChange={setSearchQuery}
              className="pl-10 w-full"
            />
          </div>

          {/* Filter Badges */}
          <div className="flex justify-center gap-2 flex-wrap">
            <button
              onClick={() => setFilterType("ordinary")}
              className={cn(
                "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                filterType === "ordinary"
                  ? "bg-gray-500 text-white"
                  : "bg-gray-500/20 text-gray-400 border border-gray-500/30 hover:bg-gray-500/30"
              )}
            >
              Ordinary
            </button>
            <button
              onClick={() => setFilterType("legacy")}
              className={cn(
                "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                filterType === "legacy"
                  ? "bg-blue-500 text-white"
                  : "bg-blue-500/20 text-blue-400 border border-blue-500/30 hover:bg-blue-500/30"
              )}
            >
              Legacy
            </button>
            <button
              onClick={() => setFilterType("genesis")}
              className={cn(
                "px-3 py-1 text-xs rounded-full font-medium transition-all duration-200",
                filterType === "genesis"
                  ? "bg-orange-500 text-white"
                  : "bg-orange-500/20 text-orange-400 border border-orange-500/30 hover:bg-orange-500/30"
              )}
            >
              Genesis
            </button>
          </div>

          {/* Results Count */}
          <div className="text-center">
            <span className="text-gray-400">{!isLoading && resultsText}</span>
          </div>

          {/* Chickens Display */}
          <div>
            {!isConnected ? (
              <div className="text-center py-12 text-gray-400">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-stone-800 flex items-center justify-center">
                  <WifiOff className="w-6 h-6" />
                </div>
                <p className="text-lg font-medium mb-2">Wallet not connected</p>
                <p className="text-sm">
                  Please connect your wallet to view your chickens
                </p>
              </div>
            ) : isLoading ? (
              <div className="text-center py-16">
                {/* Animated Loading Spinner */}
                <div className="relative w-20 h-20 mx-auto mb-6">
                  {/* Outer ring */}
                  <div className="absolute inset-0 rounded-full border-4 border-stone-700"></div>
                  {/* Spinning ring */}
                  <div className="absolute inset-0 rounded-full border-4 border-transparent border-t-orange-500 animate-spin"></div>
                  {/* Inner pulsing dot */}
                  <div className="absolute inset-4 rounded-full bg-orange-500/20 animate-pulse"></div>
                  {/* Center dot */}
                  <div className="absolute inset-6 rounded-full bg-orange-500"></div>
                </div>

                {/* Loading Text */}
                <div className="space-y-2">
                  <h3 className="text-xl font-semibold text-white">
                    Loading chickens...
                  </h3>
                  <p className="text-gray-400 text-sm">
                    Fetching your chickens from the blockchain
                  </p>
                </div>

                {/* Loading Progress Dots */}
                <div className="flex justify-center space-x-1 mt-6">
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                  <div className="w-2 h-2 bg-orange-500 rounded-full animate-bounce"></div>
                </div>
              </div>
            ) : error ? (
              <div className="text-center py-12 text-gray-400">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-800 flex items-center justify-center">
                  <WifiOff className="w-6 h-6" />
                </div>
                <p className="text-lg font-medium mb-2">
                  Error loading chickens
                </p>
                <p className="text-sm">
                  Failed to fetch your chickens. Please try again.
                </p>
              </div>
            ) : filteredChickens.length === 0 ? (
              <div className="text-center py-12 text-gray-400">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-stone-800 flex items-center justify-center">
                  <Search className="w-6 h-6" />
                </div>
                <p className="text-lg font-medium mb-2">No chickens found</p>
                <p className="text-sm">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
                  {currentPageChickens.map((chicken) => (
                    <ChickenGridCard
                      key={chicken.tokenId}
                      chicken={chicken}
                      isSelected={selectedChickenId === chicken.tokenId}
                      onSelect={handleSelect}
                      getTypeColor={getTypeColor}
                    />
                  ))}
                </div>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <div className="flex items-center justify-between pt-4">
                    <Button
                      appearance="outline"
                      onPress={() => setCurrentPage((p) => Math.max(1, p - 1))}
                      isDisabled={currentPage === 1}
                      className="flex items-center gap-2"
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>

                    <span className="text-sm text-gray-400">
                      Page {currentPage} of {totalPages}
                    </span>

                    <Button
                      appearance="outline"
                      onPress={() =>
                        setCurrentPage((p) => Math.min(totalPages, p + 1))
                      }
                      isDisabled={currentPage === totalPages}
                      className="flex items-center gap-2"
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </Modal.Body>

        <Modal.Footer>
          <Button appearance="outline" onPress={() => onOpenChange(false)}>
            Cancel
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}

// Grid Card Component
function ChickenGridCard({
  chicken,
  isSelected,
  onSelect,
  getTypeColor,
}: {
  chicken: IChickenForDelegation;
  isSelected: boolean;
  onSelect: (chicken: IChickenForDelegation) => void;
  getTypeColor: (type: string) => string;
}) {
  const isDisabled = !chicken.isAvailable;

  return (
    <div
      onClick={() => onSelect(chicken)}
      className={cn(
        "group relative rounded-xl transition-all duration-300 overflow-hidden bg-stone-800 border border-stone-700",
        isDisabled
          ? "opacity-60 cursor-not-allowed"
          : "cursor-pointer hover:border-stone-500 hover:shadow-lg hover:shadow-stone-500/10",
        isSelected ? "border-yellow-500 shadow-lg shadow-yellow-500/20" : ""
      )}
    >
      {/* Chicken Image with Rarity Badge */}
      <div className="aspect-square relative overflow-hidden bg-gradient-to-br from-stone-700 to-stone-800">
        <Image
          src={chicken.image}
          alt={`Chicken #${chicken.tokenId}`}
          width={200}
          height={200}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Rarity Badge */}
        {chicken.type && (
          <div className="absolute top-3 left-3">
            <span
              className={cn(
                "px-2 py-1 text-xs rounded-md font-medium",
                getTypeColor(chicken.type)
              )}
            >
              {chicken.type}
            </span>
          </div>
        )}

        {/* Unavailable Overlay */}
        {!chicken.isAvailable && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
            <span
              className={`text-xs font-medium px-2 py-1 rounded-full border ${
                chicken.rentalStatus?.rentalStatus === "rented"
                  ? "text-red-400 bg-red-500/20 border-red-500/30"
                  : chicken.rentalStatus?.rentalStatus === "delegated"
                    ? "text-purple-400 bg-purple-500/20 border-purple-500/30"
                    : chicken.rentalStatus?.rentalStatus === "listed"
                      ? "text-yellow-400 bg-yellow-500/20 border-yellow-500/30"
                      : chicken.rentalStatus?.statusLabel === "Fainted"
                        ? "text-red-400 bg-red-500/20 border-red-500/30"
                        : chicken.rentalStatus?.statusLabel === "Dead"
                          ? "text-gray-400 bg-gray-500/20 border-gray-500/30"
                          : chicken.rentalStatus?.statusLabel === "Breeding"
                            ? "text-blue-400 bg-blue-500/20 border-blue-500/30"
                            : "text-gray-400 bg-gray-500/20 border-gray-500/30"
              }`}
            >
              {chicken.rentalStatus?.statusLabel || "Unavailable"}
            </span>
          </div>
        )}
      </div>

      {/* Chicken Info */}
      <div className="p-4 space-y-3">
        {/* Chicken Name */}
        <div>
          <h3 className="font-semibold text-white">
            {chicken.metadata?.name || `Chicken #${chicken.tokenId}`}
          </h3>
        </div>

        {/* Stats Row */}
        <div className="flex items-center justify-between">
          {/* Level */}
          <div className="flex items-center gap-1">
            <Star className="w-4 h-4 text-yellow-400" />
            <span className="text-white text-sm font-medium">
              Level {chicken.level || 1}
            </span>
          </div>

          {/* Daily Feathers */}
          <div className="flex items-center gap-1">
            <span className="text-orange-400 text-sm">🪶</span>
            <span className="text-white text-sm font-medium">
              {chicken.dailyFeathers}/day
            </span>
          </div>
        </div>

        {/* Win Rate */}
        <div className="text-center">
          <span className="text-gray-400 text-sm">Win Rate: </span>
          <span className="text-white font-medium">
            {chicken.winRate?.toFixed(1) || "0.0"}%
          </span>
        </div>
      </div>
    </div>
  );
}
