"use client";

import axios from "@/lib/api";
import useBlockchain from "@/lib/hooks/useBlockchain";
import useTokens from "@/lib/hooks/useTokens";
import { useStateContext } from "@/providers/app/state";
import { breedingAbi } from "@/providers/web3/abi/breeding-abi";
import { cockAbi } from "@/providers/web3/abi/cock-abi";
import { chain } from "@/providers/web3/web3-provider";
import { useHookstate } from "@hookstate/core";
import { keepPreviousData, useMutation, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Address, formatEther } from "viem";
import {
  EProcessed,
  iClaimHistoryResponse,
} from "../types/claim-request-history";
import { IClaimRequest } from "../types/ninuno.types";

// fetch
export const initiateClaimRequest = async (amount: number) => {
  const { data } = await axios.post("/ninuno-rewards/initiate-claim-request", {
    amount: BigInt(amount * 10 ** 18).toString(),
  });
  return data.data as IClaimRequest;
};

export const reInitiateClaimRequest = async (withdrawalRequestId: number) => {
  const { data } = await axios.post(
    "/ninuno-rewards/reinitiate-claim-request",
    {
      withdrawalRequestId,
    }
  );
  return data.data as IClaimRequest;
};

export const claimRequestHistory = async (
  page: number,
  status: EProcessed | "ALL" = "ALL"
) => {
  const { data } = await axios.get(`/ninuno-rewards/view-claim-request`, {
    params: {
      page,
      limit: 10,
      status: status === "ALL" ? [] : [status],
    },
  });
  return data.data as iClaimHistoryResponse;
};

// hooks
export const useClaimBalance = () => {
  const { publicClient, walletClient, address, Disconnect } = useStateContext();
  const { cock } = useTokens();
  const { blockchainQuery } = useBlockchain();
  const cockAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.cock_address
    : ("" as Address);
  const breedingAddress = blockchainQuery.isSuccess
    ? blockchainQuery.data?.breeding_address
    : ("" as Address);

  const loadingClaimBalance = useHookstate(false);
  const page = useHookstate(1); // Default page to 1
  const statusFilter = useHookstate<EProcessed | "ALL">("ALL");

  const initiateClaimRequestMutation = useMutation({
    mutationFn: initiateClaimRequest,
  });
  const reInitiateClaimRequestMutation = useMutation({
    mutationFn: reInitiateClaimRequest,
  });

  const claimRequestHistoryQuery = useQuery({
    queryKey: ["claimRequestHistory", page.value, statusFilter.value],
    queryFn: () => claimRequestHistory(page.value, statusFilter.value),
    placeholderData: keepPreviousData,
  });

  const executeClaimBalance = async (
    withdrawalRequestId: number,
    amount: string,
    signature: `0x${string}`
  ) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot claim balance", {
          description: "Wallet not connected",
          position: "top-right",
        });
        Disconnect();
        return;
      }

      loadingClaimBalance.set(true);

      await approveCock(BigInt(amount));

      const simulateReq = await publicClient.simulateContract({
        address: breedingAddress,
        abi: breedingAbi,
        functionName: "claimNinunoBalance",
        args: [BigInt(withdrawalRequestId), BigInt(amount), signature],
        chain,
        account: address,
      });

      // Estimate gas for the transaction
      const gasEstimate = await publicClient.estimateContractGas({
        address: breedingAddress,
        abi: breedingAbi,
        functionName: "claimNinunoBalance",
        args: [BigInt(withdrawalRequestId), BigInt(amount), signature],
        account: address,
      });

      // Add 10% buffer to gas estimate
      const gasWithBuffer = (gasEstimate * 115n) / 100n;

      const request = simulateReq.request;
      request.gas = gasWithBuffer;

      // Continue with transaction if simulation succeeded
      const hash = await walletClient.writeContract(request);

      toast.info("Claim transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-center",
      });

      // Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      // Refetch cock balance
      cock.cock.refetch();

      if (receipt.status === "success") {
        toast.success("Claim transaction successful", {
          description: "Successfully claimed your balance",
          position: "top-center",
        });
      } else {
        toast.error("Claim transaction failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-center",
        });
      }
    } catch (e) {
      toast.error("Claim transaction failed", {
        description: "The transaction was processed but failed on-chain",
        position: "top-center",
      });
      console.error(e);
    } finally {
      loadingClaimBalance.set(false);
    }
  };

  const approveCock = async (amount: bigint) => {
    try {
      if (!address || !walletClient) {
        toast.error("Cannot approve COCK token", {
          description: "Wallet not connected",
          position: "top-right",
        });
        return;
      }

      const cockAllowance = await publicClient.readContract({
        address: cockAddress,
        abi: cockAbi,
        functionName: "allowance",
        args: [address, breedingAddress],
      });

      if (cockAllowance >= amount) {
        toast.success("ERC20 approval successful", {
          description: `Approved ${formatEther(amount)} COCK tokens for spending`,
          position: "top-right",
        });
        return;
      }

      toast.info("Preparing approval transaction...", {
        description: `Approving ${formatEther(amount)} COCK tokens for spending`,
        position: "top-right",
      });

      const { request } = await publicClient.simulateContract({
        address: cockAddress,
        abi: cockAbi,
        functionName: "approve",
        args: [breedingAddress, amount],
        chain, // Explicitly specify the chain
        account: address, // Explicitly specify the account
      });
      // Approve amount for ERC20 token
      const hash = await walletClient!.writeContract(request);

      toast.info("Approval transaction sent", {
        description: `Transaction hash: ${hash}`,
        position: "top-right",
      });

      // Wait for transaction to be mined
      const receipt = await publicClient.waitForTransactionReceipt({ hash });

      if (receipt.status === "success") {
        toast.success("ERC20 approval successful", {
          description: `Approved ${formatEther(amount)} COCK tokens for spending`,
          position: "top-right",
        });
      } else {
        toast.error("ERC20 approval failed", {
          description: "The transaction was processed but failed on-chain",
          position: "top-right",
        });
      }
    } catch (e) {
      console.error("approveCock", e);
      toast.error("Failed to approve Cock token", {
        description: "Please try again later",
        position: "top-right",
      });
    }
  };

  return {
    initiateClaimRequestMutation,
    reInitiateClaimRequestMutation,
    claimRequestHistoryQuery,
    page,
    statusFilter,
    loadingClaimBalance,
    executeClaimBalance,
    approveCock,
  };
};
