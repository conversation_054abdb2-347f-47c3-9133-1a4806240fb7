import { HardhatRuntimeEnvironment } from "hardhat/types";

const deploy = async ({
  getNamedAccounts,
  deployments,
}: HardhatRuntimeEnvironment) => {
  const { deploy } = deployments;
  const { deployer } = await getNamedAccounts();

  await deploy("SabongSagaRentalLogic", {
    contract: "SabongSagaRentalUpgradeable",
    from: deployer,
    log: true,
  });
};

deploy.tags = ["SabongSagaRentalLogic"];
deploy.dependencies = ["VerifyContracts"];

export default deploy;
