import Env from '@ioc:Adonis/Core/Env'
import { chickenGenesis<PERSON>bi } from './abi/chicken-genesis-abi'
import { chickenLegacy<PERSON><PERSON> } from './abi/chicken-legacy-abi'
import { cockAbi } from './abi/cock-abi'
import { itemAbi } from './abi/item-abi'
import { breedingAbi } from './abi/breeding-abi'
import { resourcesAbi } from './abi/resources-abi'
import { referralAbi } from './abi/referral-abi'
import { rentalAbi } from './abi/rental-abi'

const SABONG_CONFIG = {
  ENV: Env.get('NODE_ENV'),
  API_URL: Env.get('API_URL'),
  ADMIN_ADDRESSES: Env.get('ADMIN_ADDRESSES')
    .split(',')
    .map((address) => address.toLowerCase()) as string[],
  SIGNER_KEY: Env.get('SIGNER_KEY'),
  RONIN_RPC: Env.get('RONIN_RPC'),
  CONTRACTS: {
    CHICKEN_GENESIS_ADDRESS: Env.get('CHICKEN_GENESIS_ADDRESS'),
    CHICKEN_LEGACY_ADDRESS: Env.get('CHICKEN_LEGACY_ADDRESS'),
    COCK_ADDRESS: Env.get('COCK_ADDRESS'),
    ITEMS_ADDRESS: Env.get('ITEMS_ADDRESS'),
    BREEDING_ADDRESS: Env.get('BREEDING_ADDRESS'),
    RESOURCES_ADDRESS: Env.get('RESOURCES_ADDRESS'),
    REFERRAL_ADDRESS: Env.get('REFERRAL_ADDRESS'),
    RENTAL_ADDRESS: Env.get('RENTAL_ADDRESS'),
  },
  ABIS: {
    CHICKEN_GENESIS_ABI: chickenGenesisAbi,
    CHICKEN_LEGACY_ABI: chickenLegacyAbi,
    COCK_ABI: cockAbi,
    ITEMS_ABI: itemAbi,
    BREEDING_ABI: breedingAbi,
    RESOURCES_ABI: resourcesAbi,
    REFERRAL_ABI: referralAbi,
    RENTAL_ABI: rentalAbi,
  },
  BREEDING_COOLDOWN_MAX_COUNT_DURATION: Number(Env.get('BREEDING_COOLDOWN_MAX_COUNT_DURATION')),
  BREEDING_COOLDOWN_MAX_COUNT_FEE: Number(Env.get('BREEDING_COOLDOWN_MAX_COUNT_FEE')),
  BREEDING_NINUNO_PERCENTAGE_PER_PARENT: Number(Env.get('BREEDING_NINUNO_PERCENTAGE_PER_PARENT')),
  BREEDING_HATCHING_DAYS: Number(Env.get('BREEDING_HATCHING_DAYS')),
  CHICKEN_GENESIS_THRESHOLD: 2222,
  CHICKEN_LEGACY_THRESHOLD: 11110,
  BREEDING_CONFIG: {
    TRAITS_WEIGHTS: [37.5, 37.5, 9.4, 9.4, 2.3, 2.3, 0.8, 0.8], //[p,p,h1,h1,h2,h2,h3,h3]
    NINUNO_DISTRIBUTION_WEIGHTS: [10, 5, 3, 2, 1], //[3x Legendary, 2x Legendary, 1x Legendary, Genesis, Legacy]
  },
  BREEDING_CREATION_BLOCKNUMBER: BigInt(Env.get('BREEDING_CREATION_BLOCKNUMBER')),
  CHICKEN_IVORY_API_URL: Env.get('CHICKEN_IVORY_API_URL'),
  CHICKEN_IVORY_API_KEY: Env.get('CHICKEN_IVORY_API_KEY'),
  SKYMAVIS_API_KEY: Env.get('SKYMAVIS_API_KEY'),
  RENTAL_CREATION_BLOCKNUMBER: BigInt(Env.get('RENTAL_CREATION_BLOCKNUMBER', '0')),
}

export default SABONG_CONFIG
