import { ethers, upgrades, network } from "hardhat";
// import * as hre from "hardhat";
import fs from "fs";
import path from "path";
import chalk from "chalk";
// import { HardhatRuntimeEnvironment } from "hardhat/types";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("Upgrading contract with the account:", deployer.address);

  // Get deployment info
  const deploymentFile = path.join(
    __dirname,
    `../../deployments/breeding_${network.name}.json`
  );
  if (!fs.existsSync(deploymentFile)) {
    throw new Error(`Deployment file not found: ${deploymentFile}`);
  }
  const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, "utf8"));

  // Get contract factory for new implementation
  const SabongSagaBreeding = await ethers.getContractFactory(
    "SabongSagaBreedingUpgradeable"
  );

  console.log("Upgrading SabongSagaBreeding...");
  const breeding = await upgrades.upgradeProxy(
    deploymentInfo.proxy,
    SabongSagaBreeding
  );
  await breeding.waitForDeployment();

  // Get new implementation address
  const newImplementationAddress =
    await upgrades.erc1967.getImplementationAddress(deploymentInfo.proxy);
  console.log("New implementation deployed to:", newImplementationAddress);

  // Update deployment info
  deploymentInfo.previousImplementations =
    deploymentInfo.previousImplementations || [];
  deploymentInfo.previousImplementations.push(deploymentInfo.implementation);
  deploymentInfo.implementation = newImplementationAddress;
  deploymentInfo.lastUpgrade = new Date().toISOString();

  // Save updated deployment info
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  console.log(`Updated deployment info saved to ${deploymentFile}`);

  // Get the ABI from artifacts
  const artifactPath = path.join(
    __dirname,
    "../../artifacts/contracts/upgradeable/SabongSagaBreedingUpgradeable.sol/SabongSagaBreedingUpgradeable.json"
  );
  const artifactJson = JSON.parse(fs.readFileSync(artifactPath, "utf8"));

  // Update abi.json
  const abiPath = path.join(__dirname, "abi.json");
  const currentAbi = JSON.parse(fs.readFileSync(abiPath, "utf8"));

  const updatedAbi = {
    ...currentAbi,
    sabong_saga_breeding_address: deploymentInfo.proxy,
    sabong_saga_breeding: artifactJson.abi,
  };

  // Write updated ABI to file
  fs.writeFileSync(abiPath, JSON.stringify(updatedAbi, null, 2));
  console.log(`Updated ABI saved to ${abiPath}`);

  // Verify new implementation if on a supported network -- disabled for now
  // if (network.name !== "hardhat" && network.name !== "localhost") {
  //   console.log("\nVerifying new implementation...");
  //   try {
  //     await hre.run("verify:verify", {
  //       address: newImplementationAddress,
  //       contract:
  //         "contracts/upgradeable/SabongSagaBreedingUpgradeable.sol:SabongSagaBreedingUpgradeable",
  //     });
  //     console.log("New implementation contract verified");
  //   } catch (error) {
  //     console.log("Error verifying contract:", error);
  //   }
  // }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("❌ Error:"), error);
    process.exit(1);
  });
