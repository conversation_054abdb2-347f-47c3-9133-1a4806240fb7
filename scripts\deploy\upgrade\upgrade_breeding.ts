import { HardhatRuntimeEnvironment } from "hardhat/types";
import { DeployFunction } from "hardhat-deploy/types";

const upgrade: DeployFunction = async ({
  getNamedAccounts,
  deployments,
}: HardhatRuntimeEnvironment) => {
  const { deployer } = await getNamedAccounts();
  const { log, execute } = deployments;

  // Get the Proxy Admin and Proxy
  const proxyAdmin = await deployments.get("SabongSagaBreedingProxyAdmin");
  const proxy = await deployments.get("SabongSagaBreedingProxy");

  // Deploy the new logic contract
  const newLogic = await deployments.get("SabongSagaBreedingLogic");

  log(`New logic contract deployed at: ${newLogic.address}`);

  // Upgrade the proxy to use the new logic contract
  await execute(
    "SabongSagaBreedingProxyAdmin",
    { from: deployer, log: true },
    "upgrade",
    proxy.address,
    newLogic.address
  );

  log(`Proxy upgraded to new logic contract at: ${newLogic.address}`);
};

upgrade.dependencies = [
  "VerifyContracts",
  "SabongSagaBreedingProxyAdmin",
  "SabongSagaBreedingLogic",
];

export default upgrade;
