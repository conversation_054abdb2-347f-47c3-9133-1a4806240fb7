import { HardhatUserConfig, vars } from "hardhat/config";
import "@nomicfoundation/hardhat-toolbox";
import "@openzeppelin/hardhat-upgrades";
import "hardhat-deploy";
const RONIN_RPC = vars.get("RONIN_RPC");
const SAIGON_DEPLOYER_ACCOUNT = vars.get("SAIGON_DEPLOYER_ACCOUNT");
const RONIN_DEPLOYER_ACCOUNT = vars.get("RONIN_DEPLOYER_ACCOUNT");
const SAIGON_SIGNER_ACCOUNT = vars.get("SAIGON_SIGNER_ACCOUNT");
const config: HardhatUserConfig = {
  solidity: {
    version: "0.8.28",
    settings: {
      optimizer: {
        enabled: true,
        runs: 200,
      },
    },
  },
  namedAccounts: {
    deployer: 0,
  },
  typechain: {
    outDir: "./typechain-types",
  },
  paths: {
    sources: "./contracts",
    cache: "./cache/hardhat",
    deploy: ["./scripts/deploy"],
  },
  networks: {
    local: {
      url: "http://127.0.0.1:8545",
      accounts: [
        "0xa267530f49f8280200edf313ee7af6b827f2a8bce2897751d06a843f644967b1",
      ],
    },
    hardhat: {
      forking: {
        url: "https://ronin-saigon.g.alchemy.com/v2/B8QTyaz47EInX5HMhlAoefRvt4zz-4vQ",
        blockNumber: ********,
      },
      // accounts: [
      //   {
      //     privateKey: SAIGON_DEPLOYER_ACCOUNT,
      //     balance: "132996754016093037385",
      //   },
      //   {
      //     privateKey:
      //       "0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80",
      //     balance: "132996754016093037385",
      //   },
      //   {
      //     privateKey:
      //       "0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d",
      //     balance: "132996754016093037385",
      //   },
      //   {
      //     privateKey: SAIGON_SIGNER_ACCOUNT,
      //     balance: "132996754016093037385",
      //   },
      //   {
      //     privateKey: SAIGON_SIGNER_ACCOUNT,
      //     balance: "132996754016093037385",
      //   },

      //   {
      //     privateKey:
      //       "0x47e179ec197488593b187f80a00eb0da91f1b9d0b13f8733639f19c30a34926a",
      //     balance: "132996754016093037385",
      //   },

      //   {
      //     privateKey:
      //       "0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba",
      //     balance: "132996754016093037385",
      //   },

      //   {
      //     privateKey:
      //       "0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba",
      //     balance: "132996754016093037385",
      //   },

      //   {
      //     privateKey:
      //       "0x8b3a350cf5c34c9194ca85829a2df0ec3153be0318b5e2d3348e872092edffba",
      //     balance: "132996754016093037385",
      //   },
      // ],
    },
    saigon: {
      url: "https://saigon-testnet.roninchain.com/rpc",
      accounts: [SAIGON_DEPLOYER_ACCOUNT],
      gasPrice: 21_000_000_000,
    },
    ronin: {
      url: RONIN_RPC,
      accounts: [RONIN_DEPLOYER_ACCOUNT],
      gasPrice: 21_000_000_000,
    },
  },
};

export default config;
