import { ethers } from "hardhat";
import abi from "./abi.json";
const signer = "******************************************";

async function main() {
  const [deployer] = await ethers.getSigners();

  console.log("Executing script with the account:", deployer.address);

  const itemsContract = new ethers.Contract(
    abi.sabong_saga_items_address,
    abi.erc1155_common,
    deployer
  );

  let tx = await itemsContract.bulkMint(
    1,
    ["******************************************"],
    [BigInt(1000000)],
    [ethers.toUtf8Bytes("")]
  );
  await tx.wait();
}
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
