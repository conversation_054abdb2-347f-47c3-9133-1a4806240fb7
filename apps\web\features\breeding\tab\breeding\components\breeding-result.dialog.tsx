"use client";

import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/ui";
import { useBreeding } from "../hooks/useBreeding";
import { useHookstate } from "@hookstate/core";
import { IconCheck, IconTriangleExclamationFill } from "justd-icons";
import { useGlobalState } from "@/lib/store";

export default function BreedingResultDialog() {
  const gState = useGlobalState();
  const { state } = useBreeding();
  const dialogState = useHookstate(state.dialog.breed);

  return (
    <Modal
      isOpen={dialogState.isOpen.value}
      onOpenChange={(isOpen) => dialogState.isOpen.set(isOpen)}
    >
      <Modal.Content size="md" closeButton>
        <Modal.Header>
          <Modal.Title>
            <div className="flex items-center gap-2">
              {dialogState.type.value === "success" ? (
                <div className="text-primary text-2xl">✨</div>
              ) : (
                <div className="text-red-500 text-2xl">❌</div>
              )}
              {dialogState.title.value}
            </div>
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="py-8">
            {dialogState.type.value === "success" ? (
              <div className="flex flex-col items-center gap-6">
                <div className="relative">
                  <div className="absolute -inset-1 rounded-full bg-primary/20 blur-xl" />
                  <div className="relative size-24 rounded-full bg-primary/10 grid place-items-center">
                    <IconCheck className="size-12 text-primary" />
                  </div>
                </div>
                <p className="text-center text-lg">
                  {dialogState.description.value}
                </p>
              </div>
            ) : (
              <div className="flex flex-col items-center gap-6">
                <div className="relative">
                  <div className="absolute -inset-1 rounded-full bg-red-500/20 blur-xl" />
                  <div className="relative size-24 rounded-full bg-red-500/10 grid place-items-center">
                    <IconTriangleExclamationFill className="size-12 text-red-500" />
                  </div>
                </div>
                <p className="text-center text-lg text-red-400">
                  {dialogState.description.value}
                </p>
              </div>
            )}
          </div>
        </Modal.Body>
        <Modal.Footer className="justify-center">
          <Button
            className={
              dialogState.type.value === "success" ? "bg-primary" : "bg-red-500"
            }
            onPress={() => {
              dialogState.isOpen.set(false);
              if (dialogState.type.value === "success") {
                gState.breeding.tab.set("hatching");
              }
            }}
          >
            {dialogState.type.value === "success" ? "Awesome!" : "Close"}
          </Button>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
