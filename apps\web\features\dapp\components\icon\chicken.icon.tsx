"use client";

import React, { SVGProps } from "react";

export function ChickenIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="1em"
      height="1em"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M19.886 7.426c.057-.011.126-.018.179-.026a.4.4 0 0 1 .2.049a.061.061 0 0 0 .089-.067c-.02-.063-.049-.138-.065-.19a.14.14 0 0 0-.1-.093a.106.106 0 0 1-.081-.126a.3.3 0 0 1 .049-.108a.76.76 0 0 0 .142-.64a.5.5 0 0 0-.2-.261a.077.077 0 0 1-.02-.115a.23.23 0 0 0 .044-.13a.9.9 0 0 0-.06-.294a.5.5 0 0 1 0-.168c.016-.078-.086-.126-.086-.126s.077-.082 0-.246s-.6-.215-.6-.215s.077-.335-.345-.5c-.341-.13-.613.4-.706.617a.124.124 0 0 1-.142.067a.08.08 0 0 1-.053-.13l.28-.3a.112.112 0 0 0-.041-.179l-.032-.015l.016-.007a.236.236 0 0 0 .146-.3v-.001a.24.24 0 0 0-.142-.145l-.058-.023a.27.27 0 0 1-.138-.126l-.044-.089a.28.28 0 0 0-.268-.145a1.46 1.46 0 0 0-.894.412c-.511.469-.869 2.523-.93 2.928a.25.25 0 0 1-.06.127q-.08.096-.268.305a3.73 3.73 0 0 1-1.708 1.027a18 18 0 0 1-2.3.126a6.7 6.7 0 0 1-2.347-.405c-.538-.314-.997-1.946-1.61-2.552a3.4 3.4 0 0 0-1.559-.811a1.1 1.1 0 0 0-.341-.015a.076.076 0 0 1-.077-.112a1 1 0 0 0 .073-.138c.008-.048-.069-.167-.069-.167l-.138-.112l-.057-.078l-.155-.048l-.028-.019a.072.072 0 0 0-.1.037l-.045.131s-.1-.2-.167-.168c-.109.056 0 .35 0 .35s-.2-.335-.312-.283c-.2.09.04.521.04.521s-.15-.264-.255-.223c-.053.018 0 .35 0 .35s-.109-.038-.15-.019s.045.279.045.279H4.31a.08.08 0 0 0-.077.052l-.016.045l-.073.059a.21.21 0 0 0-.073.2l.016.086l-.021.089l.009.1a1.8 1.8 0 0 0-.313.294a.83.83 0 0 0-.118.457a.4.4 0 0 1 .179-.119l.191-.1a.66.66 0 0 1 .381-.067h.24a1.3 1.3 0 0 0-.1.361a.574.574 0 0 0 .669.566a.54.54 0 0 0 .39-.257A7 7 0 0 0 4.952 8.8a4.64 4.64 0 0 0 .921 3.318C7.035 13.6 8.781 13.971 9.2 14.272s.974.923 1.364 1.131c.345.186.845.729 1.393 1.15a3.7 3.7 0 0 0 .642.431a12 12 0 0 1-.662 2.065a.28.28 0 0 1-.285.156a6 6 0 0 0-.836-.022a2.3 2.3 0 0 0-.54.167c-.041.015-.024.067.016.067c.191 0 .483 0 .593.011c0 0 .219.067.223.067c-.032 0-.3.008-.345.012a7 7 0 0 0-1.218.186a.036.036 0 0 0 .008.07c.2.019.585.056.755.082c.256.041.362.164.63.212a1.7 1.7 0 0 1-.309.164c-.13.03-.312.335-.422.536c-.016.029.02.063.057.048c.227-.119.731-.387.792-.409a5.6 5.6 0 0 1 1.27-.376c.029-.007.078-.019.159-.145c.219.03.463.071.662.108a1.6 1.6 0 0 1-.309.163c-.13.03-.3.324-.418.529a.039.039 0 0 0 .057.048c.2-.115.572-.335.735-.42a7 7 0 0 1 1.319-.354c.033-.007.094-.026.2-.216a.3.3 0 0 1 .171-.137a1.8 1.8 0 0 1 .426-.108c.09.1.382.1.512.093h.085a.025.025 0 0 0 .02-.041l-.056-.059c0-.008-.143-.153-.248-.253a1.5 1.5 0 0 0-.646-.1c.094-.253.207-.577.333-1.012c.118-.372.224-.748.309-1.075a2.08 2.08 0 0 0 1.027-1.112a2.96 2.96 0 0 0 1.218-2a8.5 8.5 0 0 0-.58-2.392a3.9 3.9 0 0 0 1.266-.4a2.68 2.68 0 0 0 .845-1.715a8 8 0 0 0 .049-1.034a.22.22 0 0 1 .122-.175a.7.7 0 0 0 .146-.085A2.7 2.7 0 0 1 20.243 8a.067.067 0 0 0 .053-.1a.9.9 0 0 0-.207-.205l-.236-.156a.064.064 0 0 1 .033-.113M14.2 19.041c-.04.09-.2.09-.308.082a5 5 0 0 0-.861-.022a3 3 0 0 0-.333.048c.089-.245.585-1.856.682-2.221a4.5 4.5 0 0 0 .755-.457c.285-.264.309-.395.309-.395a1.4 1.4 0 0 0 .451.8a12.6 12.6 0 0 1-.695 2.165"
      ></path>
    </svg>
  );
}
