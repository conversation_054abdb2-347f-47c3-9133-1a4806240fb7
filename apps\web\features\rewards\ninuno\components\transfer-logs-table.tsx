"use client";

import React from "react";
import { <PERSON><PERSON>, Card, Table } from "ui";
import { useTransferBalance } from "../hooks/useTransferBalance";
import { ETransferStatus } from "../types/ninuno.types";

interface ITransferLogsTableProps {
  className?: string;
}

/**
 * TransferLogsTable Component
 *
 * Displays the history of transfer balance operations with pagination.
 * MVP version with mock data and simplified functionality.
 */
export const TransferLogsTable: React.FC<ITransferLogsTableProps> = ({
  className,
}) => {
  const { transferHistoryQuery, page } = useTransferBalance();

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get status badge class based on status
  const getStatusBadgeClass = (status: ETransferStatus) => {
    switch (status) {
      case ETransferStatus.COMPLETED:
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case ETransferStatus.PENDING:
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";
      case ETransferStatus.FAILED:
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  // Render loading state
  if (transferHistoryQuery.isPending) {
    return (
      <Card className={className}>
        <Card.Header>
          <Card.Title>Transfer History</Card.Title>
          <Card.Description>Loading your transfer history...</Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="animate-pulse space-y-4">
            {[...Array(5)].map((_, index) => (
              <div
                key={index}
                className="h-12 bg-gray-200 dark:bg-gray-700 rounded"
              ></div>
            ))}
          </div>
        </Card.Content>
      </Card>
    );
  }

  // Render empty state
  if (
    transferHistoryQuery.isSuccess &&
    transferHistoryQuery.data.data.length === 0
  ) {
    return (
      <Card className={className}>
        <Card.Header>
          <Card.Title>Transfer History</Card.Title>
          <Card.Description>
            Your history of $COCK rewards transfers
          </Card.Description>
        </Card.Header>
        <Card.Content>
          <div className="text-center py-8">
            <p className="text-muted-fg">No transfer history found.</p>
            <p className="text-sm text-muted-fg mt-2">
              When you transfer rewards from your chickens to your claimable
              balance, the transactions will appear here.
            </p>
          </div>
        </Card.Content>
      </Card>
    );
  }

  // Render transfer logs table
  return (
    <Card className={className}>
      <Card.Header>
        <Card.Title>Transfer History</Card.Title>
        <Card.Description>
          Your history of $COCK rewards transfers
        </Card.Description>
      </Card.Header>
      <Card.Content>
        <Table aria-label="Transfer logs table">
          <Table.Header>
            <Table.Column isRowHeader={true}>Date</Table.Column>
            <Table.Column>Chicken</Table.Column>
            <Table.Column>Amount</Table.Column>
            <Table.Column>Status</Table.Column>
          </Table.Header>
          <Table.Body>
            {transferHistoryQuery.isSuccess &&
              transferHistoryQuery.data.data.map((log) => (
                <Table.Row key={log.id}>
                  <Table.Cell>{formatDate(log.updated_at)}</Table.Cell>
                  <Table.Cell>
                    <div className="flex flex-wrap gap-1">
                      <span className="inline-block px-2 py-1 text-xs bg-secondary rounded-full">
                        Chicken #{log.chicken_token_id}
                      </span>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <span className="font-medium text-primary">
                      {(parseFloat(log.amount) / 10 ** 18).toFixed(2)} $COCK
                    </span>
                  </Table.Cell>
                  <Table.Cell>
                    <span
                      className={`inline-block px-2 py-1 text-xs rounded-full ${getStatusBadgeClass(ETransferStatus.COMPLETED)}`}
                    >
                      {ETransferStatus.COMPLETED}
                    </span>
                  </Table.Cell>
                </Table.Row>
              ))}
          </Table.Body>
        </Table>

        {/* Pagination */}
        {transferHistoryQuery.isSuccess && (
          <div className="flex items-center justify-center gap-4 my-4">
            <Button
              intent="secondary"
              size="small"
              isDisabled={page.value <= 1}
              onPress={() => page.set((v) => v - 1)}
            >
              Previous
            </Button>

            <span className="text-sm text-muted-fg">
              Page {page.value} of{" "}
              {transferHistoryQuery.data.meta.last_page || 1}
            </span>

            <Button
              intent="secondary"
              size="small"
              isDisabled={
                page.value >= transferHistoryQuery.data.meta.last_page
              }
              onPress={() => page.set((v) => v + 1)}
            >
              Next
            </Button>
          </div>
        )}
      </Card.Content>
    </Card>
  );
};
