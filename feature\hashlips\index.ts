import SABONG_CONFIG from 'Config/sabong'
import { generate, getRandomNonLegendaryTrait } from './generator'

export type TraitCategory = 'Feet' | 'Tail' | 'Body' | 'Wings' | 'Eyes' | 'Beak' | 'Comb' | 'Color'

export type Traits = Record<TraitCategory, { p: string; h1: string; h2: string; h3: string }> &
  Record<'Innate Attack' | 'Innate Defense' | 'Innate Speed' | 'Innate Health', number> &
  Record<'Instinct', string>

const legendaryTraitMappings = {
  Beak: [
    'Chim Lạc',
    'Thunderbird',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
  ],
  Body: [] as string[],
  Comb: [
    '<PERSON>oka<PERSON>',
    'Adarna',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
  ],
  Eyes: [
    'Garuda',
    'Minokawa',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
  ],
  Feet: [
    '<PERSON><PERSON><PERSON>w',
    'Alicanto Oro',
    'Alicanto Plata',
    'Thunderbird', // Legendary traits (0-3)
  ],
  Tail: [
    'Simurgh',
    'Chim Lạc',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>', // Legendary traits (0-3)
  ],
  Wings: [
    'Adarna',
    'Minokawa',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
  ],
  Color: [] as string[],
  Instinct: [] as string[],
}

function getRandomTrait(traits: string[]): string {
  const weights = SABONG_CONFIG.BREEDING_CONFIG.TRAITS_WEIGHTS
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)
  const random = Math.random() * totalWeight

  let cumulativeWeight = 0
  for (let i = 0; i < traits.length; i++) {
    cumulativeWeight += weights[i]
    if (random <= cumulativeWeight) {
      return traits[i]
    }
  }

  // Fallback to first trait (should never happen with proper weights)
  return traits[0]
}

const generateOffspringIPTraits = (parent1Stats, parent2Stats) => {
  const ipTraits = [
    { trait_type: 'Innate Attack', value: 0, display_type: 'number' },
    { trait_type: 'Innate Defense', value: 0, display_type: 'number' },
    { trait_type: 'Innate Speed', value: 0, display_type: 'number' },
    { trait_type: 'Innate Health', value: 0, display_type: 'number' },
  ]

  // For each trait, randomly select from either parent
  ipTraits.forEach((trait, index) => {
    const parent1Value = parent1Stats[index].value
    const parent2Value = parent2Stats[index].value

    // Randomly select from either parent
    trait.value = Math.random() < 0.5 ? parent1Value : parent2Value

    // Generate random factor between -15 and +10
    const randomFactor = Math.floor(Math.random() * 26) - 15

    // Add random factor
    trait.value = Math.max(0, Math.min(40, trait.value + randomFactor))
  })

  return ipTraits
}

// Helper function to extract IP stats from a chicken's traits
const extractIPStats = (traits) => {
  return [
    { trait_type: 'Innate Attack', value: traits['Innate Attack'], display_type: 'number' },
    { trait_type: 'Innate Defense', value: traits['Innate Defense'], display_type: 'number' },
    { trait_type: 'Innate Speed', value: traits['Innate Speed'], display_type: 'number' },
    { trait_type: 'Innate Health', value: traits['Innate Health'], display_type: 'number' },
  ]
}

// Function to generate a weighted instinct
function generateWeightedInstinct(parentInstincts: string[]) {
  const instincts = [
    'Aggressive',
    'Steadfast',
    'Swift',
    'Stalwart',
    'Balanced',
    'Reckless',
    'Resolute',
    'Elusive',
    'Tenacious',
    'Unyielding',
    'Vicious',
    'Adaptive',
    'Versatile',
    'Relentless',
    'Blazing',
    'Bulwark',
    'Enduring',
  ]

  // Create weights map with base weight of 1
  const weights = new Map<string, number>()
  instincts.forEach((instinct) => weights.set(instinct, 1))

  // Add extra weight (1.5) to parent instincts
  parentInstincts.forEach((instinct) => {
    if (weights.has(instinct)) {
      weights.set(instinct, 1.5)
    }
  })

  // Calculate total weight
  const totalWeight = Array.from(weights.values()).reduce((sum, weight) => sum + weight, 0)

  // Generate random number between 0 and total weight
  let random = Math.random() * totalWeight

  // Select instinct based on weights
  for (const [instinct, weight] of weights.entries()) {
    random -= weight
    if (random <= 0) {
      return instinct
    }
  }

  // Fallback to first instinct (should never happen)
  return instincts[0]
}

export async function breed(
  chickenLeft: Traits,
  chickenRight: Traits,
  newChickenTokenId: number,
  chickenLeftTokenId: number,
  chickenRightTokenId: number,
  generation: string = 'Gen 1'
): Promise<{ traits: Traits; genes: string; metadata: any }> {
  const parent1Stats = extractIPStats(chickenLeft)
  const parent2Stats = extractIPStats(chickenRight)

  // Generate offspring IP traits based on parents
  const offspringInnatePoints = generateOffspringIPTraits(parent1Stats, parent2Stats)

  // Get parent instincts
  const parentInstincts = [chickenLeft.Instinct, chickenRight.Instinct].filter(Boolean) // Remove any undefined/null values

  // Generate weighted instinct
  const offspringInstinct = generateWeightedInstinct(parentInstincts)

  const offspring: Partial<
    Record<TraitCategory, { p: string; h1: string; h2: string; h3: string }>
  > = {}

  for (const category of Object.keys(chickenLeft) as TraitCategory[]) {
    if (!['Feet', 'Tail', 'Body', 'Wings', 'Eyes', 'Beak', 'Comb', 'Color'].includes(category))
      continue

    const traitsPool = [
      chickenLeft[category].p,
      chickenRight[category].p,
      chickenLeft[category].h1,
      chickenRight[category].h1,
      chickenLeft[category].h2,
      chickenRight[category].h2,
      chickenLeft[category].h3,
      chickenRight[category].h3,
    ].map((trait, _, original) =>
      legendaryTraitMappings[category].includes(trait)
        ? original.slice(2)[Math.floor(Math.random() * (original.length - 2))] // Only select from traits after index 1
        : trait
    )

    const p = getRandomTrait(traitsPool)
    const h1 = getRandomTrait(traitsPool)
    const h2 = getRandomTrait(traitsPool)
    const h3 = getRandomNonLegendaryTrait(category)
    // const h3 = getRandomTrait(traitsPool)

    offspring[category] = {
      p,
      h1,
      h2,
      h3,
    }
  }

  const offspringForcedDNA = Object.entries(offspring).map(([trait, values]) => ({
    trait,
    value: values.p,
  }))

  const metadata = await generate(
    newChickenTokenId,
    offspringForcedDNA,
    chickenLeftTokenId,
    chickenRightTokenId,
    generation,
    offspring,
    offspringInstinct,
    offspringInnatePoints
  )

  const genes = metadata?.attributes.find((attr) => attr.trait_type === 'Genes').value!

  return { traits: offspring as Traits, genes, metadata }
}
