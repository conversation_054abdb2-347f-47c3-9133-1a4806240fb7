import { Abi, Address } from "viem";

export interface IBlockchainConfig {
  chicken_genesis_address: Address;
  chicken_genesis_abi: Abi;
  chicken_legacy_abi: Abi;
  chicken_legacy_address: Address;
  cock_address: Address;
  cock_abi: Abi;
  items_address: Address;
  items_abi: Abi;
  breeding_address: Address;
  breeding_abi: Abi;
  breeding_cooldown_max_count_duration: number;
  chicken_genesis_threshold: number;
  chicken_legacy_threshold: number;
  resources_address: Address;
  resources_abi: Abi;
  rental_address: Address;
  rental_abi: Abi;
}
