import { IMeta } from "./transfer-history.types";

export interface IClaimHistory {
  id: number;
  address: string;
  amount: string;
  processed: number;
  created_at: string;
  updated_at: string;
}

export interface iClaimHistoryResponse {
  meta: IMeta;
  data: IClaimHistory[];
}

export enum EProcessed {
  PENDING = 0,
  COMPLETED = 1,
}

export const PROCESSED_STATUS = {
  ALL: "All Statuses",
  0: "Pending",
  1: "Completed",
};
