"use client";

import { cn } from "@/components/ui";

interface TokenBalanceProps {
  iconSrc: string;
  balance: string | number;
  alt?: string;
  className?: string;
  iconClassName?: string;
  textClassName?: string;
}

export default function TokenBalance({
  iconSrc,
  balance,
  alt = "Token",
  className,
  iconClassName,
  textClassName,
}: TokenBalanceProps) {
  return (
    <div className={cn("flex items-center gap-1.5", className)}>
      <img
        src={iconSrc}
        className={cn("w-auto h-8", iconClassName)}
        alt={alt}
      />
      <span className={cn("text-white/90", textClassName)}>{balance}</span>
    </div>
  );
}
