"use client";

import { useMemo } from "react";
import { Table, Button } from "ui";
import {
  RefreshCw,
  Calendar,
  Clock,
  Coins,
  Users,
  Gamepad2,
  Info,
} from "lucide-react";
import { IRentalWithMetadata } from "../../types/delegation.types";
import {
  ERentalStatus,
  ERewardDistributionType,
  EDelegatedTaskType,
  REWARD_DISTRIBUTION_LABELS,
  DELEGATED_TASK_LABELS,
} from "../../types/delegation.types";

interface IRentalHistoryTableProps {
  history: IRentalWithMetadata[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  } | null;
  isLoading: boolean;
  error: any;
  currentUserAddress?: string;
  onRefresh: () => void;
  onNextPage: () => void;
  onPreviousPage: () => void;
  onFirstPage: () => void;
  onLastPage: () => void;
}

interface IHistoryEvent {
  id: string;
  chickenTokenId: number;
  chickenImage?: string;
  chickenType?: string;
  action: string;
  actionType: "listed" | "delegated" | "expired" | "cancelled";
  price: {
    daily: string;
    total: string | null;
  };
  duration: string;
  date: string;
  delegationTerms: string;
  isOwner: boolean;
  otherParty?: string;
  rental: IRentalWithMetadata; // Add rental data for shared terms calculation
}

export function RentalHistoryTable({
  history,
  pagination,
  isLoading,
  error,
  currentUserAddress,
  onRefresh,
  onNextPage,
  onPreviousPage,
  onFirstPage,
  onLastPage,
}: IRentalHistoryTableProps) {
  // Helper functions
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatPrice = (priceWei: string, rentalPeriod: number) => {
    if (priceWei === "0") return { daily: "Free", total: null };
    const priceEth = parseFloat(priceWei) / 10 ** 18;
    const days = Math.floor(rentalPeriod / (24 * 60 * 60));
    const totalPrice = priceEth * days;
    return {
      daily: `${priceEth.toFixed(4)} RON/day`,
      total: `${totalPrice.toFixed(4)} RON total`,
    };
  };

  const formatDuration = (seconds: number) => {
    const days = Math.floor(seconds / (24 * 60 * 60));
    if (days === 1) return "1 day";
    return `${days} days`;
  };

  const getDelegationTermsLabel = (
    rewardDistribution: ERewardDistributionType,
    delegatedTask: EDelegatedTaskType
  ) => {
    const accessLabel = DELEGATED_TASK_LABELS[delegatedTask] || "Unknown";
    const rewardLabel =
      REWARD_DISTRIBUTION_LABELS[rewardDistribution] || "Unknown";
    return `${accessLabel} • ${rewardLabel}`;
  };

  const getSharedRewardDetails = (rental: IRentalWithMetadata) => {
    if (
      rental.rewardDistribution !== ERewardDistributionType.SHARED ||
      !rental.sharedRewardAmount
    ) {
      return null;
    }

    const delegateeShare = rental.sharedRewardAmount;
    const totalDaily = rental.dailyFeathers || 0;
    const delegatorShare = Math.max(0, totalDaily - delegateeShare);

    return {
      delegateeShare,
      delegatorShare,
      totalDaily,
    };
  };

  const getActionDescription = (
    rental: IRentalWithMetadata,
    isOwner: boolean
  ): {
    action: string;
    actionType: IHistoryEvent["actionType"];
    date: string;
  } => {
    const otherParty = isOwner ? rental.renterAddress : rental.ownerAddress;
    const truncatedAddress = otherParty
      ? `${otherParty.slice(0, 6)}...${otherParty.slice(-4)}`
      : "";

    switch (rental.status) {
      case ERentalStatus.AVAILABLE:
        return {
          action: isOwner ? "Listed for delegation" : "Available for rent",
          actionType: "listed",
          date: rental.createdAt,
        };

      case ERentalStatus.RENTED:
        if (rental.rentedAt) {
          return {
            action: isOwner
              ? `Delegated to ${truncatedAddress}`
              : `Rented from ${truncatedAddress}`,
            actionType: "delegated",
            date: rental.rentedAt,
          };
        } else {
          return {
            action: isOwner ? "Listed for delegation" : "Available for rent",
            actionType: "listed",
            date: rental.createdAt,
          };
        }

      case ERentalStatus.EXPIRED:
        return {
          action: isOwner ? "Delegation expired" : "Rental expired",
          actionType: "expired",
          date: rental.expiresAt || rental.updatedAt,
        };

      case ERentalStatus.CANCELLED:
        return {
          action: isOwner ? "Cancelled listing" : "Listing cancelled",
          actionType: "cancelled",
          date: rental.updatedAt,
        };

      default:
        return {
          action: "Unknown action",
          actionType: "listed",
          date: rental.createdAt,
        };
    }
  };

  // Transform rental data into history events
  const historyEvents: IHistoryEvent[] = useMemo(() => {
    if (!currentUserAddress) return [];

    return history
      .map((rental) => {
        const isOwner =
          rental.ownerAddress.toLowerCase() ===
          currentUserAddress.toLowerCase();
        const { action, actionType, date } = getActionDescription(
          rental,
          isOwner
        );
        const otherParty = isOwner ? rental.renterAddress : rental.ownerAddress;

        return {
          id: `${rental.id}-${actionType}`,
          chickenTokenId: rental.chickenTokenId,
          chickenImage: rental.chickenMetadata?.image,
          chickenType: String(
            rental.chickenMetadata?.attributes?.find(
              (attr) => attr.trait_type === "Type"
            )?.value || "Unknown"
          ),
          action,
          actionType,
          price: formatPrice(rental.roninPrice, rental.rentalPeriod),
          duration: formatDuration(rental.rentalPeriod),
          date: formatDate(date),
          delegationTerms: getDelegationTermsLabel(
            rental.rewardDistribution,
            rental.delegatedTask
          ),
          isOwner,
          otherParty: otherParty || undefined,
          rental: rental,
        };
      })
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [history, currentUserAddress]);

  const getActionIcon = (actionType: IHistoryEvent["actionType"]) => {
    switch (actionType) {
      case "listed":
        return <Calendar className="w-4 h-4" />;
      case "delegated":
        return <Users className="w-4 h-4" />;
      case "expired":
        return <Clock className="w-4 h-4" />;
      case "cancelled":
        return <RefreshCw className="w-4 h-4" />;
      default:
        return <Calendar className="w-4 h-4" />;
    }
  };

  const getActionBadgeColor = (actionType: IHistoryEvent["actionType"]) => {
    switch (actionType) {
      case "listed":
        return "bg-green-600/90 text-white border-green-500";
      case "delegated":
        return "bg-blue-600/90 text-white border-blue-500";
      case "expired":
        return "bg-gray-600/90 text-white border-gray-500";
      case "cancelled":
        return "bg-red-600/90 text-white border-red-500";
      default:
        return "bg-gray-600/90 text-white border-gray-500";
    }
  };

  const getActionTextColor = (actionType: IHistoryEvent["actionType"]) => {
    switch (actionType) {
      case "listed":
        return "text-green-400";
      case "delegated":
        return "text-blue-400";
      case "expired":
        return "text-gray-400";
      case "cancelled":
        return "text-red-400";
      default:
        return "text-gray-400";
    }
  };

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-400 mb-4">Failed to load rental history</div>
        <Button onPress={onRefresh} appearance="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Loading State */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">
            Loading delegation history...
          </div>
        </div>
      ) : historyEvents.length > 0 ? (
        <>
          {/* Table */}
          <div className="bg-stone-800 rounded-lg overflow-hidden">
            <Table aria-label="Delegation history table">
              <Table.Header>
                <Table.Column isRowHeader={true}>Chicken</Table.Column>
                <Table.Column>Action</Table.Column>
                <Table.Column>Price</Table.Column>
                <Table.Column>Duration</Table.Column>
                <Table.Column>Date</Table.Column>
                <Table.Column>Terms</Table.Column>
              </Table.Header>
              <Table.Body>
                {historyEvents.map((event) => (
                  <Table.Row key={event.id}>
                    {/* Chicken */}
                    <Table.Cell>
                      <div className="flex items-center gap-3">
                        {event.chickenImage && (
                          <img
                            src={event.chickenImage}
                            alt={`Chicken #${event.chickenTokenId}`}
                            className="w-10 h-10 rounded-lg object-cover"
                          />
                        )}
                        <div>
                          <div className="font-medium">
                            #{event.chickenTokenId}
                          </div>
                          <div className="text-xs text-gray-400">
                            {event.chickenType}
                          </div>
                        </div>
                      </div>
                    </Table.Cell>

                    {/* Action */}
                    <Table.Cell>
                      <div className="flex items-center gap-2">
                        <span
                          className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border backdrop-blur-sm ${getActionBadgeColor(event.actionType)}`}
                        >
                          {getActionIcon(event.actionType)}
                          <span className="capitalize">{event.actionType}</span>
                        </span>
                        <span
                          className={`text-sm ${getActionTextColor(event.actionType)}`}
                        >
                          {event.action}
                        </span>
                      </div>
                    </Table.Cell>

                    {/* Price */}
                    <Table.Cell>
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-1">
                          <Coins className="w-4 h-4 text-yellow-500" />
                          <span className="font-medium">
                            {event.price.daily}
                          </span>
                        </div>
                        {event.price.total && (
                          <div className="text-xs text-gray-400">
                            {event.price.total}
                          </div>
                        )}
                      </div>
                    </Table.Cell>

                    {/* Duration */}
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4 text-blue-500" />
                        <span>{event.duration}</span>
                      </div>
                    </Table.Cell>

                    {/* Date */}
                    <Table.Cell>
                      <span className="text-sm">{event.date}</span>
                    </Table.Cell>

                    {/* Terms */}
                    <Table.Cell>
                      <div className="flex items-center gap-1">
                        <Gamepad2 className="w-4 h-4 text-purple-500" />
                        <span className="text-xs">{event.delegationTerms}</span>
                        {event.rental.rewardDistribution ===
                          ERewardDistributionType.SHARED && (
                          <div
                            className="relative group cursor-help"
                            title="Hover to see reward distribution"
                          >
                            <Info className="w-3 h-3 text-blue-400" />
                            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                              {(() => {
                                const details = getSharedRewardDetails(
                                  event.rental
                                );
                                if (!details)
                                  return "No reward details available";

                                const isOwner = event.isOwner;
                                const ownerShare = isOwner
                                  ? details.delegatorShare
                                  : details.delegateeShare;
                                const renterShare = isOwner
                                  ? details.delegateeShare
                                  : details.delegatorShare;

                                return (
                                  <div className="text-center">
                                    <div className="font-semibold mb-1">
                                      Daily Feather Distribution
                                    </div>
                                    <div>Owner: {ownerShare} feathers</div>
                                    <div>Renter: {renterShare} feathers</div>
                                    <div className="text-gray-300 text-xs mt-1">
                                      Total: {details.totalDaily} feathers/day
                                    </div>
                                  </div>
                                );
                              })()}
                            </div>
                          </div>
                        )}
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          </div>

          {/* Pagination */}
          {pagination && pagination.totalPages > 1 && (
            <div className="flex items-center justify-between pt-4 border-t border-stone-700">
              <div className="text-sm text-gray-400">
                Showing{" "}
                {(pagination.currentPage - 1) * pagination.itemsPerPage + 1} to{" "}
                {Math.min(
                  pagination.currentPage * pagination.itemsPerPage,
                  pagination.totalItems
                )}{" "}
                of {pagination.totalItems} events
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onPress={onFirstPage}
                  appearance="outline"
                  isDisabled={!pagination.hasPreviousPage}
                  size="small"
                >
                  First
                </Button>
                <Button
                  onPress={onPreviousPage}
                  appearance="outline"
                  isDisabled={!pagination.hasPreviousPage}
                  size="small"
                >
                  Previous
                </Button>
                <span className="text-sm text-gray-400">
                  Page {pagination.currentPage} of {pagination.totalPages}
                </span>
                <Button
                  onPress={onNextPage}
                  appearance="outline"
                  isDisabled={!pagination.hasNextPage}
                  size="small"
                >
                  Next
                </Button>
                <Button
                  onPress={onLastPage}
                  appearance="outline"
                  isDisabled={!pagination.hasNextPage}
                  size="small"
                >
                  Last
                </Button>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="text-center py-8">
          <div className="text-gray-400 mb-2">No delegation history found</div>
          <p className="text-sm text-gray-500">
            Your delegation activities will appear here once you start listing
            or renting chickens.
          </p>
        </div>
      )}
    </div>
  );
}
