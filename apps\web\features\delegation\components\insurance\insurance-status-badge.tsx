"use client";

import { cn } from "@/utils/classes";
import { AlertTriangle, CheckCircle, Clock, HelpCircle } from "lucide-react";
import { Tooltip } from "ui";

interface IInsuranceStatusBadgeProps {
  isDead?: boolean;
  isVerified?: boolean;
  confidence?: "high" | "low";
  requiresReview?: boolean;
  verificationMethod?: "api" | "contract" | "dual" | "genesis";
  className?: string;
}

export function InsuranceStatusBadge({
  isDead,
  isVerified,
  confidence,
  requiresReview,
  verificationMethod,
  className,
}: IInsuranceStatusBadgeProps) {
  // Determine badge appearance based on status
  const getBadgeConfig = () => {
    if (requiresReview) {
      return {
        icon: HelpCircle,
        text: "Needs Review",
        bgColor: "bg-yellow-500/20",
        textColor: "text-yellow-400",
        borderColor: "border-yellow-500/50",
        tooltip:
          "Death verification requires manual review due to conflicting results",
      };
    }

    if (!isVerified) {
      return {
        icon: Clock,
        text: "Verifying",
        bgColor: "bg-blue-500/20",
        textColor: "text-blue-400",
        borderColor: "border-blue-500/50",
        tooltip: "Chicken death status is being verified",
      };
    }

    if (isDead) {
      return {
        icon: AlertTriangle,
        text: "Chicken Died",
        bgColor: "bg-red-500/20",
        textColor: "text-red-400",
        borderColor: "border-red-500/50",
        tooltip: `Chicken died during rental (${confidence} confidence, ${verificationMethod} verification)`,
      };
    }

    return {
      icon: CheckCircle,
      text: "Chicken Alive",
      bgColor: "bg-green-500/20",
      textColor: "text-green-400",
      borderColor: "border-green-500/50",
      tooltip: `Chicken survived rental (${confidence} confidence, ${verificationMethod} verification)`,
    };
  };

  const config = getBadgeConfig();
  const IconComponent = config.icon;

  return (
    <Tooltip delay={0}>
      <Tooltip.Trigger
        className={cn(
          "inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium border",
          config.bgColor,
          config.textColor,
          config.borderColor,
          className
        )}
      >
        <IconComponent className="w-3 h-3" />
        <span>{config.text}</span>
      </Tooltip.Trigger>
      <Tooltip.Content>{config.tooltip}</Tooltip.Content>
    </Tooltip>
  );
}
