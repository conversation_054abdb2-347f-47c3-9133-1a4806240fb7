import { create } from "zustand";
import { Client, Room } from "colyseus.js";
import useAuthStore from "./auth";

interface MatchmakingStateSchema {
  waitingCount: number;
  players: {
    [key: string]: {
      sessionId: string;
      fighterId: number;
      joinedAt: number;
    };
  };
}

interface MatchmakingState {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  connectionError: string | null;

  // Queue state
  isInQueue: boolean;
  queuePosition: number;
  waitingCount: number;

  // Match state
  matchFound: boolean;
  matchCode: string | null;
  gameUrl: string | null;

  // Selected fighter
  selectedFighterId: number | null;

  // Battle verification data
  battleSignature: string | null;
  battleNonce: number | null;

  // Colyseus client and room
  client: Client | null;
  room: Room | null;

  address: string | null;

  // Actions
  setSelectedFighter: (fighterId: number) => void;
  setBattleVerification: (signature: string, nonce: number) => void;
  connectToServer: (serverUrl: string) => Promise<void>;
  joinMatchmaking: () => Promise<void>;
  leaveMatchmaking: () => void;
  resetMatchState: () => void;
  disconnect: () => void;
  setAddress: (address: string) => void;
}

const useMatchmakingStore = create<MatchmakingState>((set, get) => ({
  // Initial state
  isConnected: false,
  isConnecting: false,
  connectionError: null,
  isInQueue: false,
  queuePosition: 0,
  waitingCount: 0,
  matchFound: false,
  matchCode: null,
  gameUrl: null,
  selectedFighterId: null,
  battleSignature: null,
  battleNonce: null,
  client: null,
  room: null,
  address: null,

  // Set selected fighter
  setSelectedFighter: (fighterId: number) => {
    set({ selectedFighterId: fighterId });
  },

  // Set battle verification data
  setBattleVerification: (signature: string, nonce: number) => {
    set({ battleSignature: signature, battleNonce: nonce });
  },

  setAddress: (address: string) => {
    set({ address: address });
  },

  // Connect to Colyseus server
  connectToServer: async (serverUrl: string) => {
    set({ isConnecting: true, connectionError: null });

    try {
      const client = new Client(serverUrl);
      set({ client, isConnected: true, isConnecting: false });
    } catch (error) {
      console.error("Failed to connect to server:", error);
      set({
        connectionError:
          error instanceof Error
            ? error.message
            : "Unknown error connecting to server",
        isConnecting: false,
      });
    }
  },

  // Join matchmaking queue
  joinMatchmaking: async () => {
    const { client, selectedFighterId, address, battleSignature, battleNonce } =
      get();

    if (!client) {
      set({ connectionError: "Not connected to server" });
      return;
    }

    if (!selectedFighterId) {
      set({ connectionError: "No fighter selected" });
      return;
    }

    if (!address) {
      set({
        connectionError: "No address connected. Please connect your wallet",
      });
      return;
    }

    if (!battleSignature || !battleNonce) {
      set({
        connectionError:
          "Battle verification required. Please verify chicken ownership first.",
      });
      return;
    }

    try {
      set({ isInQueue: true });
      const { rns } = useAuthStore.getState();
      // Join matchmaking room with battle verification data
      const room = await client.joinOrCreate<MatchmakingStateSchema>(
        "matchmaking",
        {
          fighterId: selectedFighterId,
          address: address,
          signature: battleSignature,
          nonce: battleNonce,
          rns,
        }
      );

      set({ room });

      // Set up event listeners
      room.onStateChange((state: any) => {
        if (!state) {
          set({ waitingCount: 0, queuePosition: 0 });
          return;
        }

        // Get the waitingCount or default to 0
        const waitingCount =
          typeof state.waitingCount === "number" ? state.waitingCount : 0;

        // Calculate queue position
        let queuePosition = 0;

        // Define player interface to match your schema
        interface QueuePlayerData {
          sessionId: string;
          fighterId: number;
          joinedAt: number;
        }

        // Handle players data safely
        if (state.players) {
          let players: QueuePlayerData[] = [];

          // Check if players is an array, Map, or object
          if (Array.isArray(state.players)) {
            players = state.players as QueuePlayerData[];
          } else if (
            state.players instanceof Map ||
            typeof state.players.values === "function"
          ) {
            // For MapSchema or Map
            players = Array.from(state.players.values()) as QueuePlayerData[];
          } else if (typeof state.players === "object") {
            // For plain object representation
            players = Object.values(state.players) as QueuePlayerData[];
          }

          // Now safely sort and find position with proper type annotations
          if (players.length > 0) {
            queuePosition =
              players
                .sort(
                  (a: QueuePlayerData, b: QueuePlayerData) =>
                    (a.joinedAt || 0) - (b.joinedAt || 0)
                )
                .findIndex(
                  (p: QueuePlayerData) => p && p.sessionId === room.sessionId
                ) + 1;

            // If not found, default to last position
            if (queuePosition <= 0) {
              queuePosition = players.length;
            }
          }
        }

        set({ waitingCount, queuePosition });
      });

      // Listen for match found
      room.onMessage("match_found", (message) => {
        set({
          matchFound: true,
          matchCode: message.code,
          gameUrl: message.gameUrl,
        });
      });

      // Handle disconnection
      room.onLeave((code) => {
        if (code !== 1000) {
          // Abnormal close
          set({
            connectionError: `Disconnected from matchmaking: Code ${code}`,
            isInQueue: false,
            room: null,
          });
        } else {
          set({
            isInQueue: false,
            room: null,
          });
        }
      });
    } catch (error) {
      console.error("Failed to join matchmaking:", error);
      set({
        connectionError:
          error instanceof Error ? error.message : "Failed to join matchmaking",
        isInQueue: false,
      });
    }
  },

  // Leave matchmaking queue
  leaveMatchmaking: () => {
    const { room } = get();

    if (room) {
      room.leave();
    }

    set({
      isInQueue: false,
      room: null,
      queuePosition: 0,
      waitingCount: 0,
    });
  },

  // Reset match state (after redirect)
  resetMatchState: () => {
    set({
      matchFound: false,
      matchCode: null,
      gameUrl: null,
      connectionError: null,
      battleSignature: null,
      battleNonce: null,
    });
  },

  // Disconnect from server
  disconnect: () => {
    const { room, client } = get();

    if (room) {
      room.leave();
    }

    if (client) {
      // Close client connection
      // Note: Colyseus.js doesn't have an explicit disconnect method
      // but we can set it to null to allow garbage collection
    }

    set({
      isConnected: false,
      isInQueue: false,
      room: null,
      client: null,
      queuePosition: 0,
      waitingCount: 0,
    });
  },
}));

export default useMatchmakingStore;
