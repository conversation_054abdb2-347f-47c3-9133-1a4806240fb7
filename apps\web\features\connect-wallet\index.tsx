"use client";

import { ArrowDownRoundedIcon } from "@/components/common/icons/arrow-down.icon";
import { CopyIcon } from "@/components/common/icons/copy.icon";
import { LogoutIcon } from "@/components/common/icons/logout.icon";
import { MetamaskIcon } from "@/components/common/icons/metamask.icon";
import { WalletConnectIcon } from "@/components/common/icons/wallet-connect.icon";
import { XIcon } from "@/components/common/icons/x.icon";
import Loading from "@/components/common/loading";
import LoadingDots2 from "@/components/common/loading/loading-dots";
import { Button, cn, Modal } from "@/components/ui";
import useTokens from "@/lib/hooks/useTokens";
import { useGlobalStatePersist } from "@/lib/store/persist";
import { shortAddr } from "@/lib/utils";
import { useMediaQuery } from "@/lib/utils/hook/use-media-query";
import { useStateContext } from "@/providers/app/state";
import { chainId } from "@/providers/web3/web3-provider";
import { useHookstate } from "@hookstate/core";
import Jdenticon from "react-jdenticon";
import { toast } from "sonner";
import {
  Connector,
  useAccount,
  useConnect,
  useDisconnect,
  useSwitchChain,
} from "wagmi";

export default function ConnectWallet() {
  const gStateP = useGlobalStatePersist();
  const isMobile = useMediaQuery("(max-width: 640px)");

  // States
  const { loading, Disconnect } = useStateContext();
  const connectModal = useHookstate(false);
  const disconnectModal = useHookstate(false);
  const loadingIcon = useHookstate("none");

  // Wagmi
  const { chainId: connectedChainId, isConnected } = useAccount();
  const { connectors, connect, error, isError, isPending } = useConnect();
  const { switchChain, isPending: isSwitching } = useSwitchChain();
  const tokens = useTokens();

  const handleConnect = async (connector: Connector) => {
    loadingIcon.set(connector.id);

    try {
      // Always disconnect first to prevent "Connector already connected" error
      if (isConnected) {
        await new Promise<void>((resolve) => {
          Disconnect();
          // Give a small delay to ensure disconnection is complete
          setTimeout(resolve, 500);
        });
      }

      connect(
        { connector, chainId: chainId },
        {
          onSuccess: () => {
            connectModal.set(false);
            gStateP.recentConnectorId.set(connector.id);
          },
          onError: (error) => {
            console.error(error);
            // Don't show the "Connector already connected" error to user
            if (!error?.message?.includes("Connector already connected")) {
              toast.error(error?.message);
            }
          },
          onSettled: () => {
            loadingIcon.set("none");
          },
        }
      );
    } catch (error) {
      console.error("Connection error:", error);
      loadingIcon.set("none");
    }
  };

  // Prepare sorted connectors with recent on top
  const sortedConnectors = (() => {
    const filtered = connectors.filter(
      (connector) => connector.id !== "injected"
    );
    const sorted = [...filtered].sort((a, b) => {
      if (a.id === "walletConnect") return 1;
      if (b.id === "walletConnect") return -1;
      return 0;
    });
    const recentId = gStateP.recentConnectorId.value;
    if (recentId) {
      const idx = sorted.findIndex((c) => c.id === recentId);
      if (idx > 0) {
        const [recent] = sorted.splice(idx, 1);
        if (recent) {
          sorted.unshift(recent);
        }
      }
    }
    return sorted;
  })();

  return (
    <div className="flex items-center gap-4">
      {/* Not connected */}
      {!gStateP.address.value && (
        <Button
          className={cn(
            "font-bold text-lg text-black/75",
            "transition-all duration-300 hover:scale-105 pressed:scale-95"
          )}
          isDisabled={loading.value}
          onPress={() => {
            if (gStateP.address.value) Disconnect();
            connectModal.set(true);
          }}
        >
          {loading.value ? (
            <div className="flex items-center gap-2">
              <Loading />
              <LoadingDots2 message="Connecting" dots="." />
            </div>
          ) : (
            <span>Login</span>
          )}
        </Button>
      )}

      {/* Wrong Network */}
      {isConnected && connectedChainId !== chainId && (
        <Button
          intent="danger"
          onPress={() => {
            switchChain({
              chainId,
            });
          }}
          isPending={isSwitching}
        >
          {isSwitching ? `Switching...` : "Wrong Network"}
        </Button>
      )}

      {/* Connected */}
      {gStateP.address.value &&
        !(isConnected && connectedChainId !== chainId) && (
          <Button
            className={cn(
              "flex items-center gap-1.5 !bg-[#2D292E] text-white h-9 !px-2",
              "transition-all duration-300 hover:scale-105 pressed:scale-95"
            )}
            onPress={() => disconnectModal.set(true)}
          >
            <div className="rounded-full overflow-hidden size-[24px] grid place-items-center">
              <Jdenticon size="24" value={gStateP.address.value} />
            </div>
            {!isMobile && (
              <span className="font-semibold">
                {shortAddr(gStateP.address.value)}
              </span>
            )}
            <ArrowDownRoundedIcon className="size-5" />
          </Button>
        )}

      {/* Connected Dialog */}
      <Modal isOpen={connectModal.value} onOpenChange={connectModal.set}>
        <Modal.Content size="sm" isDismissable={false} role="dialog">
          <Modal.Body className="relative !p-0">
            {/* Close Button */}
            <div className="absolute top-4 right-4">
              <Button
                className={cn(
                  "!bg-[#464349] !size-6 rounded-full border !border-[#4D4A50] grid place-items-center",
                  "transition-all duration-300 hover:scale-105 pressed:scale-95"
                )}
                onPress={() => connectModal.set(false)}
                shape="circle"
                size="square-petite"
              >
                <XIcon className="text-[#A2A6B6] size-4" />
              </Button>
            </div>

            <div className="flex flex-col gap-4 p-6">
              <div className="font-bold text-lg">Connect a Wallet</div>

              <div className="text-sm text-primary font-semibold">
                Installed
              </div>

              <div className="flex flex-col gap-2">
                {/* Render connectors with recent on top */}
                {sortedConnectors.map((connector) => {
                  const isWalletConnect = connector.id === "walletConnect";
                  const isMetaMask = connector.id === "metaMaskSDK";
                  const isRecent =
                    gStateP.recentConnectorId.value === connector.id;

                  return (
                    <Button
                      key={connector.uid}
                      onPress={() => {
                        handleConnect(connector);
                      }}
                      appearance="plain"
                      className="justify-start p-1"
                      isDisabled={isPending}
                    >
                      <div className="flex items-center gap-3">
                        <div className="rounded-md overflow-hidden size-8 relative">
                          {isWalletConnect && (
                            <WalletConnectIcon className="size-8 text-[#3396FF]" />
                          )}
                          {isMetaMask && <MetamaskIcon className="size-8" />}
                          <img
                            className={cn(
                              "size-8",
                              isWalletConnect && "hidden",
                              isMetaMask && "hidden"
                            )}
                            src={connector.icon}
                            alt=""
                          />
                          {loadingIcon.value === connector.id && (
                            <div className="absolute inset-0 grid place-items-center bg-black/70">
                              <Loading />
                            </div>
                          )}
                        </div>
                        <div className="text-left">
                          <div className="leading-none">{connector.name}</div>
                          {isRecent && (
                            <div className="text-primary text-xs mt-[3px]">
                              Recent
                            </div>
                          )}
                        </div>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </div>
          </Modal.Body>
        </Modal.Content>
      </Modal>

      {/* Disconnect Dialog */}
      <Modal isOpen={disconnectModal.value} onOpenChange={disconnectModal.set}>
        <Modal.Content size="sm" isDismissable={false} role="dialog">
          <Modal.Body className="relative py-6">
            {/* Close Button */}
            <div className="absolute top-4 right-4">
              <Button
                className={cn(
                  "!bg-[#464349] !size-6 rounded-full border !border-[#4D4A50] grid place-items-center",
                  "transition-all duration-300 hover:scale-105 pressed:scale-95"
                )}
                onPress={() => disconnectModal.set(false)}
                shape="circle"
                size="square-petite"
              >
                <XIcon className="text-[#A2A6B6] size-4" />
              </Button>
            </div>

            <div className="grid place-items-center">
              <div className="rounded-full overflow-hidden size-[84px] grid place-items-center">
                <Jdenticon size="84" value={gStateP.address.value} />
              </div>

              {/* Address */}
              <span className="font-black mt-4 text-lg">
                {shortAddr(gStateP.address.value)}
              </span>

              {/* Balance */}
              <div className="flex items-center gap-4 mt-1">
                <div className="flex items-center gap-2">
                  <img src={tokens.ron.iconImage} alt="RON" className="h-4" />
                  <span>
                    {tokens.ron.formatted.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <img src={tokens.cock.iconImage} alt="COCK" className="h-4" />
                  <span>
                    {tokens.cock.formatted.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <img
                    src={tokens.feather.iconImage}
                    alt="FEATHER"
                    className="h-4"
                  />
                  <span>
                    {tokens.feather.formatted.toLocaleString(undefined, {
                      minimumFractionDigits: 2,
                      maximumFractionDigits: 2,
                    })}
                  </span>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="grid grid-cols-2 gap-2 mt-4">
              <Button
                className={cn(
                  "flex items-center gap-1.5 !bg-[#47464D] text-white min-h-max !px-2",
                  "transition-all duration-300 hover:scale-[1.01] pressed:scale-95"
                )}
                onPress={() => {
                  navigator.clipboard.writeText(
                    gStateP.address.value.toLowerCase()
                  );
                  toast.success("Address copied to clipboard");
                }}
              >
                <div className="grid place-items-center gap-1">
                  <CopyIcon className="size-5" />
                  <div className="text-xs">Copy Address</div>
                </div>
              </Button>

              <Button
                className={cn(
                  "flex items-center gap-1.5 !bg-[#47464D] text-white min-h-max !px-2",
                  "transition-all duration-300 hover:scale-[1.01] pressed:scale-95 active:scale-95"
                )}
                onPress={() => {
                  disconnectModal.set(false);
                  Disconnect();
                }}
              >
                <div className="grid place-items-center gap-1">
                  <LogoutIcon className="size-5" />
                  <div className="text-xs">Disconnect</div>
                </div>
              </Button>
            </div>
          </Modal.Body>
        </Modal.Content>
      </Modal>
    </div>
  );
}
